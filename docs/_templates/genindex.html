{%- extends "basic/genindex.html" %}

{% block body %}
  {{ super() }}
  <!-- Inject some JavaScript to convert the index names into something useful. -->
  <script>
    let elements = document.querySelectorAll("table.indextable a");

    // this is pretty finicky but it should work.
    for(let el of elements) {
      let key = el.getAttribute('href').split('#', 2)[1]
      if(!key.startsWith('discord.')) {
        continue;
      }

      if(key.startsWith('discord.ext.')) {
        key = key.substr(12); // discord.ext.
      }

      if(el.textContent.indexOf('()') !== -1) {
        key = key + '()'
      }
      el.textContent = key;
    }
    document.querySelectorAll("td").forEach(el => el.style.width = 'auto');
  </script>
{% endblock %}
