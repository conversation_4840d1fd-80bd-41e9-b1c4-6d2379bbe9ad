/* light theme: default */
.highlight .hll { background-color: #ffffcc }
.highlight  { background: #f0f0f0; }
.highlight .c { color: #60a0b0; font-style: italic } /* Comment */
.highlight .err { border: 1px solid #FF0000 } /* Error */
.highlight .k { color: #007020; font-weight: bold } /* Keyword */
.highlight .o { color: #666666 } /* Operator */
.highlight .ch { color: #60a0b0; font-style: italic } /* Comment.Hashbang */
.highlight .cm { color: #60a0b0; font-style: italic } /* Comment.Multiline */
.highlight .cp { color: #007020 } /* Comment.Preproc */
.highlight .cpf { color: #60a0b0; font-style: italic } /* Comment.PreprocFile */
.highlight .c1 { color: #60a0b0; font-style: italic } /* Comment.Single */
.highlight .cs { color: #60a0b0; background-color: #fff0f0 } /* Comment.Special */
.highlight .gd { color: #A00000 } /* Generic.Deleted */
.highlight .ge { font-style: italic } /* Generic.Emph */
.highlight .gr { color: #FF0000 } /* Generic.Error */
.highlight .gh { color: #000080; font-weight: bold } /* Generic.Heading */
.highlight .gi { color: #00A000 } /* Generic.Inserted */
.highlight .go { color: #888888 } /* Generic.Output */
.highlight .gp { color: #c65d09; font-weight: bold } /* Generic.Prompt */
.highlight .gs { font-weight: bold } /* Generic.Strong */
.highlight .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.highlight .gt { color: #0044DD } /* Generic.Traceback */
.highlight .kc { color: #007020; font-weight: bold } /* Keyword.Constant */
.highlight .kd { color: #007020; font-weight: bold } /* Keyword.Declaration */
.highlight .kn { color: #007020; font-weight: bold } /* Keyword.Namespace */
.highlight .kp { color: #007020 } /* Keyword.Pseudo */
.highlight .kr { color: #007020; font-weight: bold } /* Keyword.Reserved */
.highlight .kt { color: #902000 } /* Keyword.Type */
.highlight .m { color: #40a070 } /* Literal.Number */
.highlight .s { color: #4070a0 } /* Literal.String */
.highlight .na { color: #4070a0 } /* Name.Attribute */
.highlight .nb { color: #007020 } /* Name.Builtin */
.highlight .nc { color: #0e84b5; font-weight: bold } /* Name.Class */
.highlight .no { color: #60add5 } /* Name.Constant */
.highlight .nd { color: #555555; font-weight: bold } /* Name.Decorator */
.highlight .ni { color: #d55537; font-weight: bold } /* Name.Entity */
.highlight .ne { color: #007020 } /* Name.Exception */
.highlight .nf { color: #06287e } /* Name.Function */
.highlight .nl { color: #002070; font-weight: bold } /* Name.Label */
.highlight .nn { color: #0e84b5; font-weight: bold } /* Name.Namespace */
.highlight .nt { color: #062873; font-weight: bold } /* Name.Tag */
.highlight .nv { color: #bb60d5 } /* Name.Variable */
.highlight .ow { color: #007020; font-weight: bold } /* Operator.Word */
.highlight .w { color: #bbbbbb } /* Text.Whitespace */
.highlight .mb { color: #40a070 } /* Literal.Number.Bin */
.highlight .mf { color: #40a070 } /* Literal.Number.Float */
.highlight .mh { color: #40a070 } /* Literal.Number.Hex */
.highlight .mi { color: #40a070 } /* Literal.Number.Integer */
.highlight .mo { color: #40a070 } /* Literal.Number.Oct */
.highlight .sa { color: #4070a0 } /* Literal.String.Affix */
.highlight .sb { color: #4070a0 } /* Literal.String.Backtick */
.highlight .sc { color: #4070a0 } /* Literal.String.Char */
.highlight .dl { color: #4070a0 } /* Literal.String.Delimiter */
.highlight .sd { color: #4070a0; font-style: italic } /* Literal.String.Doc */
.highlight .s2 { color: #4070a0 } /* Literal.String.Double */
.highlight .se { color: #4070a0; font-weight: bold } /* Literal.String.Escape */
.highlight .sh { color: #4070a0 } /* Literal.String.Heredoc */
.highlight .si { color: #70a0d0; font-style: italic } /* Literal.String.Interpol */
.highlight .sx { color: #c65d09 } /* Literal.String.Other */
.highlight .sr { color: #235388 } /* Literal.String.Regex */
.highlight .s1 { color: #4070a0 } /* Literal.String.Single */
.highlight .ss { color: #517918 } /* Literal.String.Symbol */
.highlight .bp { color: #007020 } /* Name.Builtin.Pseudo */
.highlight .fm { color: #06287e } /* Name.Function.Magic */
.highlight .vc { color: #bb60d5 } /* Name.Variable.Class */
.highlight .vg { color: #bb60d5 } /* Name.Variable.Global */
.highlight .vi { color: #bb60d5 } /* Name.Variable.Instance */
.highlight .vm { color: #bb60d5 } /* Name.Variable.Magic */
.highlight .il { color: #40a070 } /* Literal.Number.Integer.Long */

/* dark theme: modified "native" */
:root[data-theme="dark"] .highlight pre { background-color: #2a2a2e }
:root[data-theme="dark"] .highlight .hll { background-color: #2a2a2e }
:root[data-theme="dark"] .highlight .c { color: #999999; font-style: italic } /* Comment */
:root[data-theme="dark"] .highlight .err { color: #a61717; background-color: #e3d2d2 } /* Error */
:root[data-theme="dark"] .highlight .g { color: #d0d0d0 } /* Generic */
:root[data-theme="dark"] .highlight .k { color: #6ab825; font-weight: bold } /* Keyword */
:root[data-theme="dark"] .highlight .l { color: #d0d0d0 } /* Literal */
:root[data-theme="dark"] .highlight .n { color: #d0d0d0 } /* Name */
:root[data-theme="dark"] .highlight .o { color: #d0d0d0 } /* Operator */
:root[data-theme="dark"] .highlight .x { color: #d0d0d0 } /* Other */
:root[data-theme="dark"] .highlight .p { color: #d0d0d0 } /* Punctuation */
:root[data-theme="dark"] .highlight .cm { color: #999999; font-style: italic } /* Comment.Multiline */
:root[data-theme="dark"] .highlight .cp { color: #cd2828; font-weight: bold } /* Comment.Preproc */
:root[data-theme="dark"] .highlight .c1 { color: #999999; font-style: italic } /* Comment.Single */
:root[data-theme="dark"] .highlight .cs { color: #e50808; font-weight: bold; background-color: #520000 } /* Comment.Special */
:root[data-theme="dark"] .highlight .gd { color: #d22323 } /* Generic.Deleted */
:root[data-theme="dark"] .highlight .ge { color: #d0d0d0; font-style: italic } /* Generic.Emph */
:root[data-theme="dark"] .highlight .gr { color: #d22323 } /* Generic.Error */
:root[data-theme="dark"] .highlight .gh { color: #ffffff; font-weight: bold } /* Generic.Heading */
:root[data-theme="dark"] .highlight .gi { color: #589819 } /* Generic.Inserted */
:root[data-theme="dark"] .highlight .go { color: #cccccc } /* Generic.Output */
:root[data-theme="dark"] .highlight .gp { color: #aaaaaa } /* Generic.Prompt */
:root[data-theme="dark"] .highlight .gs { color: #d0d0d0; font-weight: bold } /* Generic.Strong */
:root[data-theme="dark"] .highlight .gu { color: #ffffff; text-decoration: underline } /* Generic.Subheading */
:root[data-theme="dark"] .highlight .gt { color: #d22323 } /* Generic.Traceback */
:root[data-theme="dark"] .highlight .kc { color: #6ab825; font-weight: bold } /* Keyword.Constant */
:root[data-theme="dark"] .highlight .kd { color: #6ab825; font-weight: bold } /* Keyword.Declaration */
:root[data-theme="dark"] .highlight .kn { color: #6ab825; font-weight: bold } /* Keyword.Namespace */
:root[data-theme="dark"] .highlight .kp { color: #6ab825 } /* Keyword.Pseudo */
:root[data-theme="dark"] .highlight .kr { color: #6ab825; font-weight: bold } /* Keyword.Reserved */
:root[data-theme="dark"] .highlight .kt { color: #6ab825; font-weight: bold } /* Keyword.Type */
:root[data-theme="dark"] .highlight .ld { color: #d0d0d0 } /* Literal.Date */
:root[data-theme="dark"] .highlight .m { color: #7fb1d7 } /* Literal.Number */
:root[data-theme="dark"] .highlight .s { color: #ed9d13 } /* Literal.String */
:root[data-theme="dark"] .highlight .na { color: #bbbbbb; } /* Name.Attribute */
:root[data-theme="dark"] .highlight .nb { color: #29a5b3; } /* Name.Builtin */
:root[data-theme="dark"] .highlight .nc { color: #6494d8;} /* Name.Class */
:root[data-theme="dark"] .highlight .no { color: #40ffff; } /* Name.Constant */
:root[data-theme="dark"] .highlight .nd { color: #ffa500; } /* Name.Decorator */
:root[data-theme="dark"] .highlight .ni { color: #d0d0d0; } /* Name.Entity */
:root[data-theme="dark"] .highlight .ne { color: #bbbbbb; } /* Name.Exception */
:root[data-theme="dark"] .highlight .nf { color: #6494d8; } /* Name.Function */
:root[data-theme="dark"] .highlight .fm { color: #6494d8; } /* Name.Function.Magic */
:root[data-theme="dark"] .highlight .nl { color: #d0d0d0; } /* Name.Label */
:root[data-theme="dark"] .highlight .nn { color: #6494d8;} /* Name.Namespace */
:root[data-theme="dark"] .highlight .nx { color: #d0d0d0; } /* Name.Other */
:root[data-theme="dark"] .highlight .py { color: #d0d0d0; } /* Name.Property */
:root[data-theme="dark"] .highlight .nt { color: #6ab825; font-weight: bold } /* Name.Tag */
:root[data-theme="dark"] .highlight .nv { color: #40ffff; } /* Name.Variable */
:root[data-theme="dark"] .highlight .ow { color: #6ab825; font-weight: bold } /* Operator.Word */
:root[data-theme="dark"] .highlight .w  { color: #666666; } /* Text.Whitespace */
:root[data-theme="dark"] .highlight .mf { color: #7fb1d7; } /* Literal.Number.Float */
:root[data-theme="dark"] .highlight .mh { color: #7fb1d7; } /* Literal.Number.Hex */
:root[data-theme="dark"] .highlight .mi { color: #7fb1d7; } /* Literal.Number.Integer */
:root[data-theme="dark"] .highlight .mo { color: #7fb1d7; } /* Literal.Number.Oct */
:root[data-theme="dark"] .highlight .sb { color: #ed9d13; } /* Literal.String.Backtick */
:root[data-theme="dark"] .highlight .sc { color: #ed9d13; } /* Literal.String.Char */
:root[data-theme="dark"] .highlight .sd { color: #ed9d13; } /* Literal.String.Doc */
:root[data-theme="dark"] .highlight .s2 { color: #ed9d13; } /* Literal.String.Double */
:root[data-theme="dark"] .highlight .se { color: #ed9d13; } /* Literal.String.Escape */
:root[data-theme="dark"] .highlight .sh { color: #ed9d13; } /* Literal.String.Heredoc */
:root[data-theme="dark"] .highlight .si { color: #ed9d13; } /* Literal.String.Interpol */
:root[data-theme="dark"] .highlight .sx { color: #ffa500; } /* Literal.String.Other */
:root[data-theme="dark"] .highlight .sr { color: #ed9d13; } /* Literal.String.Regex */
:root[data-theme="dark"] .highlight .s1 { color: #ed9d13; } /* Literal.String.Single */
:root[data-theme="dark"] .highlight .ss { color: #ed9d13; } /* Literal.String.Symbol */
:root[data-theme="dark"] .highlight .bp { color: #29a5b3; } /* Name.Builtin.Pseudo */
:root[data-theme="dark"] .highlight .vc { color: #40ffff; } /* Name.Variable.Class */
:root[data-theme="dark"] .highlight .vg { color: #40ffff; } /* Name.Variable.Global */
:root[data-theme="dark"] .highlight .vi { color: #40ffff; } /* Name.Variable.Instance */
:root[data-theme="dark"] .highlight .il { color: #7fb1d7; } /* Literal.Number.Integer.Long */
