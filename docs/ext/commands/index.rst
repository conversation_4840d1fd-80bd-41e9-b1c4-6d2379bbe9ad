.. _discord_ext_commands:

``discord.ext.commands`` -- Bot commands framework
====================================================

``discord.py`` offers a lower level aspect on interacting with Discord. Often times, the library is used for the creation of
bots. However this task can be daunting and confusing to get correctly the first time. Many times there comes a repetition in
creating a bot command framework that is extensible, flexible, and powerful. For this reason, ``discord.py`` comes with an
extension library that handles this for you.


.. toctree::
    :maxdepth: 2

    commands
    cogs
    extensions
    api
