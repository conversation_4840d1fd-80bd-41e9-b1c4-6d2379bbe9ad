msgid ""
msgstr ""
"Project-Id-Version: discordpy\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-21 01:17+0000\n"
"PO-Revision-Date: 2024-04-17 02:43\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: discordpy\n"
"X-Crowdin-Project-ID: 362783\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: intents.pot\n"
"X-Crowdin-File-ID: 86\n"
"Language: ja_JP\n"

#: ../../intents.rst:8
msgid "A Primer to Gateway Intents"
msgstr "ゲートウェイインテント入門"

#: ../../intents.rst:10
msgid "In version 1.5 comes the introduction of :class:`Intents`. This is a radical change in how bots are written. An intent basically allows a bot to subscribe to specific buckets of events. The events that correspond to each intent is documented in the individual attribute of the :class:`Intents` documentation."
msgstr "バージョン1.5では :class:`Intents` が導入されました。これにより、Botの書き方が根本的に変わります。基本的にインテントは、特定のイベント群をBotにサブスクライブさせるためのものです。各インテントに対応するイベントは、 :class:`Intents` のドキュメントに個別の属性として記載されています。"

#: ../../intents.rst:12
msgid "These intents are passed to the constructor of :class:`Client` or its subclasses (:class:`AutoShardedClient`, :class:`~.AutoShardedBot`, :class:`~.Bot`) with the ``intents`` argument."
msgstr "インテントは ``intents`` 引数を介して :class:`Client` またはそのサブクラス(:class:`AutoShardedClient`, :class:`~.AutoShardedBot`, :class:`~.Bot`)のコンストラクタに渡されます。"

#: ../../intents.rst:15
msgid "What intents are needed?"
msgstr "どのインテントが必要でしょうか？"

#: ../../intents.rst:17
msgid "The intents that are necessary for your bot can only be dictated by yourself. Each attribute in the :class:`Intents` class documents what :ref:`events <discord-api-events>` it corresponds to and what kind of cache it enables."
msgstr "あなたのBotに必要なインテントは、あなたにしかわかりません。 :class:`Intents` クラスのドキュメントの各属性欄には、どの :ref:`events <discord-api-events>` に対応し、どのようなキャッシュを有効化するのか記述されています。"

#: ../../intents.rst:19
msgid "For example, if you want a bot that functions without spammy events like presences or typing then we could do the following:"
msgstr "たとえば、プレゼンスやタイピングといったわずらわしいイベント抜きに動作するbotを書きたい場合、次のようにします。"

#: ../../intents.rst:35
msgid "Note that this doesn't enable :attr:`Intents.members` since it's a privileged intent."
msgstr "なお、特権インテントであるため、 :attr:`Intents.members` は有効になりません。"

#: ../../intents.rst:37
msgid "Another example showing a bot that only deals with messages and guild information:"
msgstr "別の例: メッセージとギルド情報だけを扱うボット:"

#: ../../intents.rst:56
msgid "Privileged Intents"
msgstr "特権インテント"

#: ../../intents.rst:58
msgid "With the API change requiring bot authors to specify intents, some intents were restricted further and require more manual steps. These intents are called **privileged intents**."
msgstr "API変更に伴い、Bot製作者がインテントを指定する必要が生じました。いくつかのインテントはより制約され、手動での設定を必要とするようになりました。これらのインテントを **特権インテント** と呼びます。"

#: ../../intents.rst:60
msgid "A privileged intent is one that requires you to go to the developer portal and manually enable it. To enable privileged intents do the following:"
msgstr "特権インテントは、開発者ポータルで手動で有効にしなければならないインテントです。有効化の手順は以下のとおりです。"

#: ../../intents.rst:62
msgid "Make sure you're logged on to the `Discord website <https://discord.com>`_."
msgstr "まずは `Discordのウェブサイト <https://discord.com>`_ にログインしてください。"

#: ../../intents.rst:63
msgid "Navigate to the `application page <https://discord.com/developers/applications>`_."
msgstr "`アプリケーションページ <https://discord.com/developers/applications>`_ に移動します。"

#: ../../intents.rst:64
msgid "Click on the bot you want to enable privileged intents for."
msgstr "特権インテントを有効にしたいBotをクリックします。"

#: ../../intents.rst:65
msgid "Navigate to the bot tab on the left side of the screen."
msgstr "画面の左側にあるBotタブに移動します。"

#: ../../intents.rst:0
msgid "The bot tab in the application page."
msgstr "アプリケーションページの「bot」タブ"

#: ../../intents.rst:70
msgid "Scroll down to the \"Privileged Gateway Intents\" section and enable the ones you want."
msgstr "「特権ゲートウェイインテント」セクションまでスクロールし、必要なものを有効にします。"

#: ../../intents.rst:0
msgid "The privileged gateway intents selector."
msgstr "特権ゲートウェイのセレクタ"

#: ../../intents.rst:77
msgid "Enabling privileged intents when your bot is in over 100 guilds requires going through `bot verification <https://support.discord.com/hc/en-us/articles/360040720412>`_. If your bot is already verified and you would like to enable a privileged intent you must go through `Discord support <https://dis.gd/contact>`_ and talk to them about it."
msgstr "もしあなたのBotが100以上のギルドで利用されているなら、特権インテントを有効化するために `bot verification <https://support.discord.com/hc/en-us/articles/360040720412>`_ が必要です。すでに認証済みのBotで特権インテントを有効化したい場合、 `Discord support <https://dis.gd/contact>`_ で報告してください。"

#: ../../intents.rst:81
msgid "Even if you enable intents through the developer portal, you still have to enable the intents through code as well."
msgstr "開発者ポータルでインテントを有効化した後は、コード内でインテントを有効化しましょう。"

#: ../../intents.rst:85
msgid "Do I need privileged intents?"
msgstr "特権インテントは必要ですか？"

#: ../../intents.rst:87
msgid "This is a quick checklist to see if you need specific privileged intents."
msgstr "それぞれの特権インテントが必要かどうかを確認する簡単なチェックリストを用意しました。"

#: ../../intents.rst:92
msgid "Presence Intent"
msgstr "プレゼンスインテント"

#: ../../intents.rst:94
msgid "Whether you use :attr:`Member.status` at all to track member statuses."
msgstr "メンバーのステータスを追跡するために :attr:`Member.status` を使用するかどうか。"

#: ../../intents.rst:95
msgid "Whether you use :attr:`Member.activity` or :attr:`Member.activities` to check member's activities."
msgstr ":attr:`Member.activity` か :attr:`Member.activities` を使用してメンバーのアクティビティを確認するかどうか。"

#: ../../intents.rst:100
msgid "Member Intent"
msgstr "メンバーインテント"

#: ../../intents.rst:102
msgid "Whether you track member joins or member leaves, corresponds to :func:`on_member_join` and :func:`on_member_remove` events."
msgstr "メンバーの参加や退出を追跡するかどうか。それぞれ :func:`on_member_join` と :func:`on_member_remove` イベントが対応します。"

#: ../../intents.rst:103
msgid "Whether you want to track member updates such as nickname or role changes."
msgstr "ニックネームやロールの変更など、メンバーの更新を追跡するかどうか。"

#: ../../intents.rst:104
msgid "Whether you want to track user updates such as usernames, avatars, discriminators, etc."
msgstr "ユーザー名、アバター、識別子などのユーザーの更新を追跡するかどうか。"

#: ../../intents.rst:105
msgid "Whether you want to request the guild member list through :meth:`Guild.chunk` or :meth:`Guild.fetch_members`."
msgstr ":meth:`Guild.chunk` か :meth:`Guild.fetch_members` でギルドメンバーリストをリクエストするかどうか。"

#: ../../intents.rst:106
msgid "Whether you want high accuracy member cache under :attr:`Guild.members`."
msgstr ":attr:`Guild.members` で高精度メンバーキャッシュを使用するかどうか。"

#: ../../intents.rst:111
msgid "Message Content"
msgstr "メッセージ内容のインテント"

#: ../../intents.rst:113
msgid "Whether you use :attr:`Message.content` to check message content."
msgstr "メッセージの内容を確認するために :attr:`Message.content` を使用するかどうか。"

#: ../../intents.rst:114
msgid "Whether you use :attr:`Message.attachments` to check message attachments."
msgstr "メッセージの添付ファイルを確認するために :attr:`Message.attachments` を使用するかどうか。"

#: ../../intents.rst:115
msgid "Whether you use :attr:`Message.embeds` to check message embeds."
msgstr "メッセージの埋め込みを確認するために :attr:`Message.embeds` を使用するかどうか。"

#: ../../intents.rst:116
msgid "Whether you use :attr:`Message.components` to check message components."
msgstr "メッセージコンポーネントをチェックするために :attr:`Message.components` を使用するかどうか。"

#: ../../intents.rst:117
msgid "Whether you use the commands extension with a non-mentioning prefix."
msgstr "コマンド拡張機能をメンションなしプレフィックスで使用するかどうか。"

#: ../../intents.rst:122
msgid "Member Cache"
msgstr "メンバーキャッシュ"

#: ../../intents.rst:124
msgid "Along with intents, Discord now further restricts the ability to cache members and expects bot authors to cache as little as is necessary. However, to properly maintain a cache the :attr:`Intents.members` intent is required in order to track the members who left and properly evict them."
msgstr "Discordは、インテントとともにメンバーをキャッシュする機能をさらに制限し、必要に応じてボット作成者がキャッシュできるようになりました。 しかし、退出したメンバーを追跡・退出させるには、適切にキャッシュを維持する :attr:`Intents.members` インテントが必要です。"

#: ../../intents.rst:126
msgid "To aid with member cache where we don't need members to be cached, the library now has a :class:`MemberCacheFlags` flag to control the member cache. The documentation page for the class goes over the specific policies that are possible."
msgstr "キャッシュされる必要のないメンバーキャッシュを手伝うために、ライブラリにメンバーキャッシュを制御するための :class:`MemberCacheFlags` フラグが追加されました。クラスのドキュメントページではできる限り明確なポリシーを調べることができます。"

#: ../../intents.rst:128
msgid "It should be noted that certain things do not need a member cache since Discord will provide full member information if possible. For example:"
msgstr "可能な限りDiscordがメンバー情報を提供するため、メンバーキャッシュを必要としないことがあります。例えば："

#: ../../intents.rst:130
msgid ":func:`on_message` will have :attr:`Message.author` be a member even if cache is disabled."
msgstr "キャッシュが無効になっていても、:func:`on_message` は :attr:`Message.author` がメンバーになります。"

#: ../../intents.rst:131
msgid ":func:`on_voice_state_update` will have the ``member`` parameter be a member even if cache is disabled."
msgstr "キャッシュが無効の場合でも、:func:`on_voice_state_update` は ``member`` パラメータをメンバーにします。"

#: ../../intents.rst:132
msgid ":func:`on_reaction_add` will have the ``user`` parameter be a member when in a guild even if cache is disabled."
msgstr ":func:`on_reaction_add` は、キャッシュが無効になっている場合でもサーバー内であれば ``user`` パラメータをメンバーにします。"

#: ../../intents.rst:133
msgid ":func:`on_raw_reaction_add` will have :attr:`RawReactionActionEvent.member` be a member when in a guild even if cache is disabled."
msgstr ":func:`on_raw_reaction_add` は、キャッシュが無効になっていてもサーバー内にいれば :attr:`RawReactionActionEvent.member` がメンバーになります。"

#: ../../intents.rst:134
msgid "The reaction add events do not contain additional information when in direct messages. This is a Discord limitation."
msgstr "ダイレクトメッセージで追加されたリアクションは追加情報を含んでいません。これはDiscordの制限です。"

#: ../../intents.rst:135
msgid "The reaction removal events do not have member information. This is a Discord limitation."
msgstr "リアクション削除イベントにメンバー情報はありません。これはDiscordの制限です。"

#: ../../intents.rst:137
msgid "Other events that take a :class:`Member` will require the use of the member cache. If absolute accuracy over the member cache is desirable, then it is advisable to have the :attr:`Intents.members` intent enabled."
msgstr ":class:`Member` を取るその他のイベントでは、メンバーキャッシュの使用が必要になります。メンバーキャッシュの絶対的な精度が必要な場合は、 :attr:`Intents.members` インテントを有効にすることをお勧めします。"

#: ../../intents.rst:142
msgid "Retrieving Members"
msgstr "メンバーの取得"

#: ../../intents.rst:144
msgid "If the cache is disabled or you disable chunking guilds at startup, we might still need a way to load members. The library offers a few ways to do this:"
msgstr "キャッシュが無効になっている場合、または起動時にサーバーのチャンキングを無効にしている場合でも、membersをロードする方法が必要になる場合があります。 ライブラリには、以下の方法が用意されています。"

#: ../../intents.rst:148
msgid ":meth:`Guild.query_members`"
msgstr ":meth:`Guild.query_members`"

#: ../../intents.rst:147
msgid "Used to query members by a prefix matching nickname or username."
msgstr "ニックネームまたはユーザー名の一致でメンバーを照会するために使用されます。"

#: ../../intents.rst:148
msgid "This can also be used to query members by their user ID."
msgstr "これは、ユーザーIDによってメンバーを照会するためにも使用できます。"

#: ../../intents.rst:149
msgid "This uses the gateway and not the HTTP."
msgstr "これはHTTPではなくゲートウェイを使用します。"

#: ../../intents.rst:150
msgid ":meth:`Guild.chunk`"
msgstr ":meth:`Guild.chunk`"

#: ../../intents.rst:151
msgid "This can be used to fetch the entire member list through the gateway."
msgstr "これはゲートウェイからメンバーリスト全体を取得するために使用できます。"

#: ../../intents.rst:152
msgid ":meth:`Guild.fetch_member`"
msgstr ":meth:`Guild.fetch_member`"

#: ../../intents.rst:153
msgid "Used to fetch a member by ID through the HTTP API."
msgstr "HTTP APIを介してIDからメンバーを取得するために使用されます。"

#: ../../intents.rst:155
msgid ":meth:`Guild.fetch_members`"
msgstr ":meth:`Guild.fetch_members`"

#: ../../intents.rst:155
msgid "used to fetch a large number of members through the HTTP API."
msgstr "HTTP APIを介して多数のメンバーを取得するために使用されます。"

#: ../../intents.rst:157
msgid "It should be noted that the gateway has a strict rate limit of 120 requests per 60 seconds."
msgstr "ゲートウェイには、60秒あたり120リクエストという厳格なレート制限があることに注意してください。"

#: ../../intents.rst:160
msgid "Troubleshooting"
msgstr "トラブルシューティング"

#: ../../intents.rst:162
msgid "Some common issues relating to the mandatory intent change."
msgstr "強制的なインテントの変更に関連するいくつかの一般的な問題。"

#: ../../intents.rst:165
msgid "Where'd my members go?"
msgstr "私のメンバーはどこに行ったのか？"

#: ../../intents.rst:167
msgid "Due to an :ref:`API change <intents_member_cache>` Discord is now forcing developers who want member caching to explicitly opt-in to it. This is a Discord mandated change and there is no way to bypass it. In order to get members back you have to explicitly enable the :ref:`members privileged intent <privileged_intents>` and change the :attr:`Intents.members` attribute to true."
msgstr ":ref:`APIの変更 <intents_member_cache>` に伴い、Discordはメンバーキャッシュを必要とする開発者に明示的なオプトインを求めるようになりました。これはDiscordが強制的に行っている変更で、これを回避する方法はありません。これを取り戻すためには、明示的に :ref:`メンバー特権インテント <privileged_intents>` を有効にして、 :attr:`Intents.members` 属性をtrueに変更する必要があります。"

#: ../../intents.rst:169
msgid "For example:"
msgstr "例："

#: ../../intents.rst:185
msgid "Why does ``on_ready`` take so long to fire?"
msgstr "``on_ready`` が発火するまでに時間がかかるのはなぜか？"

#: ../../intents.rst:187
msgid "As part of the API change regarding intents, Discord also changed how members are loaded in the beginning. Originally the library could request 75 guilds at once and only request members from guilds that have the :attr:`Guild.large` attribute set to ``True``. With the new intent changes, Discord mandates that we can only send 1 guild per request. This causes a 75x slowdown which is further compounded by the fact that *all* guilds, not just large guilds are being requested."
msgstr "インテントに関するAPIの変更の一環として、Discordは起動時のメンバーの読み込み方法も変更しました。 もともとライブラリは 75 個のギルドを一度にリクエストでき、 :attr:`Guild.large` 属性が ``True`` になるサーバーのみのメンバーをリクエストしました。Intentが新しく変更された場合、Discordはリクエストごとに1つのサーバーしか送信できません。 これは75倍の減速を引き起こし、大規模なサーバーだけでなく*すべての*サーバーが要求されているという事実によってさらに複雑になります。"

#: ../../intents.rst:189
msgid "There are a few solutions to fix this."
msgstr "これを修正するためにいくつかの解決策があります。"

#: ../../intents.rst:191
msgid "The first solution is to request the privileged presences intent along with the privileged members intent and enable both of them. This allows the initial member list to contain online members just like the old gateway. Note that we're still limited to 1 guild per request but the number of guilds we request is significantly reduced."
msgstr "最初の解決策は、presence intentとmember intentの両方を有効にすることです。 これにより、初期メンバーリストに以前のゲートウェイと同様にオンラインメンバーを含めることができます。 我々はまだリクエストごとに1つのギルドに制限されていることに注意してください。しかし、我々がリクエストするギルドの数は大幅に減少します。"

#: ../../intents.rst:193
msgid "The second solution is to disable member chunking by setting ``chunk_guilds_at_startup`` to ``False`` when constructing a client. Then, when chunking for a guild is necessary you can use the various techniques to :ref:`retrieve members <retrieving_members>`."
msgstr "二つ目の解決方法は、クライアントを構築する際に ``chunk_guilds_at_startup`` を ``False`` に設定することでメンバーのチャンクを無効にすることです。 サーバーのチャンキングが必要な場合は、 :ref:`メンバーの取得 <retrieving_members>` の様々なテクニックを使うことができます。"

#: ../../intents.rst:195
msgid "To illustrate the slowdown caused by the API change, take a bot who is in 840 guilds and 95 of these guilds are \"large\" (over 250 members)."
msgstr "APIの変更によって引き起こされる減速を説明するために、840サーバーに参加していて、そのうち95サーバーは「大きい」(250人以上のメンバーがいる) というボットを使います。"

#: ../../intents.rst:197
msgid "Under the original system this would result in 2 requests to fetch the member list (75 guilds, 20 guilds) roughly taking 60 seconds. With :attr:`Intents.members` but not :attr:`Intents.presences` this requires 840 requests, with a rate limit of 120 requests per 60 seconds means that due to waiting for the rate limit it totals to around 7 minutes of waiting for the rate limit to fetch all the members. With both :attr:`Intents.members` and :attr:`Intents.presences` we mostly get the old behaviour so we're only required to request for the 95 guilds that are large, this is slightly less than our rate limit so it's close to the original timing to fetch the member list."
msgstr "元のシステムでは、メンバーリスト(75サーバーと、20サーバー)を取得するために2回のリクエストを行うと、およそ60秒かかります。 :attr:`Intents.presences` は使用せず :attr:`Intents.members` を使用した場合、840回のリクエストが必要となり、60秒あたり120回のリクエストというレート制限があるため、すべてのメンバーを取得するためにレート制限を待つ時間は合計で約7分となります。 :attr:`Intents.members` と :attr:`Intents.presences` の両方がある状況では、ほとんどが以前の動作になるので、大規模な95のギルドに対してのみリクエストする必要があることになります。これはレートリミットよりわずかに少ないので、元のシステムでメンバーリストを取得する時間と近くなります。"

#: ../../intents.rst:199
msgid "Unfortunately due to this change being required from Discord there is nothing that the library can do to mitigate this."
msgstr "申し訳ありませんが、この変更はDiscordから要求されているため、ライブラリがこれを軽減するためにできることはありません。"

#: ../../intents.rst:201
msgid "If you truly dislike the direction Discord is going with their API, you can contact them via `support <https://dis.gd/contact>`_."
msgstr "DiscordのAPIに関する方向性が本当に嫌いなら、`サポート <https://dis.gd/contact>`_ で連絡できます。"

