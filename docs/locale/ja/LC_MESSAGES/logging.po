msgid ""
msgstr ""
"Project-Id-Version: discordpy\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-26 03:41+0000\n"
"PO-Revision-Date: 2024-04-17 02:43\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: discordpy\n"
"X-Crowdin-Project-ID: 362783\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: logging.pot\n"
"X-Crowdin-File-ID: 84\n"
"Language: ja_JP\n"

#: ../../logging.rst:8
msgid "Setting Up Logging"
msgstr "ログの設定"

#: ../../logging.rst:10
msgid "*discord.py* logs errors and debug information via the :mod:`logging` python module. In order to streamline this process, the library provides default configuration for the ``discord`` logger when using :meth:`Client.run`. It is strongly recommended that the logging module is configured, as no errors or warnings will be output if it is not set up."
msgstr "*discord.py* は、エラーとデバッグ情報を :mod:`logging` Python モジュールを用いて出力します。このプロセスを容易にするため、ライブラリは :meth:`Client.run` を使用したときに ``discord`` ロガーのデフォルト構成を提供します。設定されていないとエラーや警告は一切出力されないため、 logging モジュールを設定することを強く推奨します。"

#: ../../logging.rst:13
msgid "The default logging configuration provided by the library will print to :data:`sys.stderr` using coloured output. You can configure it to send to a file instead by using one of the built-in :mod:`logging.handlers`, such as :class:`logging.FileHandler`."
msgstr "ライブラリが提供するデフォルトの logging 構成は色付きの出力を :data:`sys.stderr` に出力します。 :class:`logging.FileHandler` などのビルトイン :mod:`logging.handlers` を使用すると、ファイルに出力するようにできます。"

#: ../../logging.rst:15
msgid "This can be done by passing it through :meth:`Client.run`:"
msgstr "これは、 :meth:`Client.run` に渡してください:"

#: ../../logging.rst:26
msgid "You can also disable the library's logging configuration completely by passing ``None``:"
msgstr "``None`` を渡して、ライブラリの logging 構成を完全に無効化することもできます。"

#: ../../logging.rst:33
msgid "Likewise, configuring the log level to ``logging.DEBUG`` is also possible:"
msgstr "ログレベルを ``logging.DEBUG`` に設定することも可能です:"

#: ../../logging.rst:44
msgid "This is recommended, especially at verbose levels such as ``DEBUG``, as there are a lot of events logged and it would clog the stderr of your program."
msgstr "特に、 ``DEBUG`` といった冗長なイベントレベルを設定している場合、プログラムの標準エラー出力をつまらせてしまう原因になるため、ファイルへの出力が推奨されます。"

#: ../../logging.rst:46
msgid "If you want the logging configuration the library provides to affect all loggers rather than just the ``discord`` logger, you can pass ``root_logger=True`` inside :meth:`Client.run`:"
msgstr ""

#: ../../logging.rst:52
msgid "If you want to setup logging using the library provided configuration without using :meth:`Client.run`, you can use :func:`discord.utils.setup_logging`:"
msgstr ":meth:`Client.run` を使用せずにライブラリ提供の構成を使用して logging を設定したい場合は、 :func:`discord.utils.setup_logging` を使用できます。"

#: ../../logging.rst:63
msgid "More advanced setups are possible with the :mod:`logging` module. The example below configures a rotating file handler that outputs DEBUG output for everything the library outputs, except for HTTP requests:"
msgstr ":mod:`logging` モジュールを使用するとより高度なセットアップが行えます。以下の例では、HTTPリクエスト以外のすべてのライブラリの出力に対しDEBUG出力を使用するローテーションを行うファイルハンドラを構成します。"

#: ../../logging.rst:91
msgid "For more information, check the documentation and tutorial of the :mod:`logging` module."
msgstr "詳細は、:mod:`logging` モジュールのドキュメントを参照してください。"

#: ../../logging.rst:95
msgid "The library now provides a default logging configuration."
msgstr "ライブラリがデフォルト logging 構成を提供するようになりました。"

