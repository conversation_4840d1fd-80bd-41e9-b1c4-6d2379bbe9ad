msgid ""
msgstr ""
"Project-Id-Version: discordpy\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-21 01:17+0000\n"
"PO-Revision-Date: 2023-06-21 01:20\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: discordpy\n"
"X-Crowdin-Project-ID: 362783\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: /interactions/api.pot\n"
"X-Crowdin-File-ID: 90\n"
"Language: ja_JP\n"

#: ../../interactions/api.rst:4
msgid "Interactions API Reference"
msgstr "インタラクションAPIリファレンス"

#: ../../interactions/api.rst:6
msgid "The following section outlines the API of interactions, as implemented by the library."
msgstr "次のセクションでは、ライブラリによって実装されたインタラクションの API について概説します。"

#: ../../interactions/api.rst:8
msgid "For documentation about the rest of the library, check :doc:`/api`."
msgstr "ライブラリの残りの部分のドキュメントについては、 :doc:`/api` を参照してください。"

#: ../../interactions/api.rst:11
msgid "Models"
msgstr "モデル"

#: ../../interactions/api.rst:13
msgid "Similar to :ref:`discord_api_models`, these are not meant to be constructed by the user."
msgstr ":ref:`discord_api_models` と同様に、これらはユーザーが構築するものではありません。"

#: ../../interactions/api.rst:16
msgid "Interaction"
msgstr "Interaction"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:1
msgid "Represents a Discord interaction."
msgstr "Discordのインタラクションを表します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:3
msgid "An interaction happens when a user does an action that needs to be notified. Current examples are slash commands and components."
msgstr "インタラクションは、ユーザーに返信をする必要のあるアクションが行われた際に発生します。現在の例はスラッシュコマンドとコンポーネントです。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:10
msgid "The interaction's ID."
msgstr "インタラクションのID。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:0
msgid "type"
msgstr "型"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:12
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:38
#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:23
#: ../../../discord/components.py:docstring of discord.components.SelectMenu:36
#: ../../../discord/components.py:docstring of discord.components.SelectMenu:43
msgid ":class:`int`"
msgstr ":class:`int`"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:16
#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:27
msgid "The interaction type."
msgstr "インタラクションの種類。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:18
#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:29
msgid ":class:`InteractionType`"
msgstr ":class:`InteractionType`"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:22
msgid "The guild ID the interaction was sent from."
msgstr "インタラクションが送信されたギルドのID。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:24
#: ../../../discord/interactions.py:docstring of discord.Interaction.channel_id:3
#: ../../../discord/components.py:docstring of discord.components.TextInput:49
#: ../../../discord/components.py:docstring of discord.components.TextInput:55
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:91
msgid "Optional[:class:`int`]"
msgstr "Optional[:class:`int`]"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:28
msgid "The channel the interaction was sent from."
msgstr "インタラクションが送信されたチャンネル。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:30
msgid "Note that due to a Discord limitation, if sent from a DM channel :attr:`~DMChannel.recipient` is ``None``."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:32
msgid "Optional[Union[:class:`abc.GuildChannel`, :class:`abc.PrivateChannel`, :class:`Thread`]]"
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:36
msgid "The application ID that the interaction was for."
msgstr "インタラクションの対象となったアプリケーションのID。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:42
msgid "The user or member that sent the interaction."
msgstr "インタラクションを送信したユーザーまたはメンバー。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:44
#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:41
msgid "Union[:class:`User`, :class:`Member`]"
msgstr "Union[:class:`User`, :class:`Member`]"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:48
msgid "The message that sent this interaction."
msgstr "このインタラクションを送信したメッセージ。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:50
msgid "This is only available for :attr:`InteractionType.component` interactions."
msgstr "これは :attr:`InteractionType.component` インタラクションの場合にのみ使用できます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:52
msgid "Optional[:class:`Message`]"
msgstr "Optional[:class:`Message`]"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:56
msgid "The token to continue the interaction. These are valid for 15 minutes."
msgstr "インタラクションを続行するのに使うトークン。有効期限は15分です。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:59
#: ../../docstring of discord.InteractionMessage.clean_content:15
#: ../../../discord/interactions.py:docstring of discord.InteractionMessage.jump_url:3
#: ../../docstring of discord.InteractionMessage.system_content:8
#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:35
msgid ":class:`str`"
msgstr ":class:`str`"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:63
msgid "The raw interaction data."
msgstr "生のインタラクションデータ。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:65
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:85
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:95
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:80
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:105
msgid ":class:`dict`"
msgstr ":class:`dict`"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:69
msgid "The locale of the user invoking the interaction."
msgstr "インタラクションを呼び出したユーザーのロケール。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:71
msgid ":class:`Locale`"
msgstr ":class:`Locale`"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:75
msgid "The preferred locale of the guild the interaction was sent from, if any."
msgstr "インタラクションの送信元のギルドの優先ロケール。もし無ければ ``None`` となります。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:77
msgid "Optional[:class:`Locale`]"
msgstr "Optional[:class:`Locale`]"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:81
msgid "A dictionary that can be used to store extraneous data for use during interaction processing. The library will not touch any values or keys within this dictionary."
msgstr "インタラクションの処理中に使用する追加のデータを保管できる辞書型。ライブラリは辞書型の中のキーや値を一切操作しません。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:89
msgid "Whether the command associated with this interaction failed to execute. This includes checks and execution."
msgstr "このインタラクションに関連付けられたコマンドの実行に失敗したかどうか。これにはチェックとコマンドの実行が含まれます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction:92
#: ../../../discord/components.py:docstring of discord.components.Button:35
#: ../../../discord/components.py:docstring of discord.components.SelectMenu:55
#: ../../../discord/components.py:docstring of discord.components.TextInput:43
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:84
msgid ":class:`bool`"
msgstr ":class:`bool`"

#: ../../../discord/interactions.py:docstring of discord.Interaction.client:1
msgid "The client that is handling this interaction."
msgstr "このインタラクションを処理するクライアント。"

#: ../../../discord/interactions.py:docstring of discord.Interaction.client:3
msgid "Note that :class:`AutoShardedClient`, :class:`~.commands.Bot`, and :class:`~.commands.AutoShardedBot` are all subclasses of client."
msgstr "なお、 :class:`AutoShardedClient` 、 :class:`~.commands.Bot` 、 :class:`~.commands.AutoShardedBot` はすべてClientのサブクラスです。"

#: ../../../discord/interactions.py:docstring of discord.Interaction.client:6
msgid ":class:`Client`"
msgstr ":class:`Client`"

#: ../../../discord/interactions.py:docstring of discord.Interaction.guild:1
msgid "The guild the interaction was sent from."
msgstr "インタラクションが送信されたギルド。"

#: ../../../discord/interactions.py:docstring of discord.Interaction.guild:3
msgid "Optional[:class:`Guild`]"
msgstr "Optional[:class:`Guild`]"

#: ../../../discord/interactions.py:docstring of discord.Interaction.channel_id:1
msgid "The ID of the channel the interaction was sent from."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.Interaction.permissions:1
msgid "The resolved permissions of the member in the channel, including overwrites."
msgstr "権限の上書きを含むチャンネルでのメンバーの権限。"

#: ../../../discord/interactions.py:docstring of discord.Interaction.permissions:3
msgid "In a non-guild context where this doesn't apply, an empty permissions object is returned."
msgstr "ギルド以外の文脈では権限が適用されないため、空の権限オブジェクトが返されます。"

#: ../../../discord/interactions.py:docstring of discord.Interaction.permissions:5
#: ../../../discord/interactions.py:docstring of discord.Interaction.app_permissions:3
msgid ":class:`Permissions`"
msgstr ":class:`Permissions`"

#: ../../../discord/interactions.py:docstring of discord.Interaction.app_permissions:1
msgid "The resolved permissions of the application or the bot, including overwrites."
msgstr "権限の上書きを含む、アプリケーションまたはボットの解決された権限。"

#: ../../docstring of discord.Interaction.namespace:1
msgid "The resolved namespace for this interaction."
msgstr "このインタラクションの解決された名前空間。"

#: ../../docstring of discord.Interaction.namespace:3
msgid "If the interaction is not an application command related interaction or the client does not have a tree attached to it then this returns an empty namespace."
msgstr "アプリケーションコマンドに関連したインタラクションでない場合、またはクライアントにツリーが関連付けられていない場合は、空の名前空間を返します。"

#: ../../docstring of discord.Interaction.namespace:6
msgid ":class:`app_commands.Namespace`"
msgstr ":class:`app_commands.Namespace`"

#: ../../docstring of discord.Interaction.command:1
msgid "The command being called from this interaction."
msgstr "このインタラクションから呼び出されるコマンド。"

#: ../../docstring of discord.Interaction.command:4
msgid "If the interaction is not an application command related interaction or the command is not found in the client's attached tree then ``None`` is returned."
msgstr "アプリケーションコマンドに関連したインタラクションでない場合、またはコマンドがクライアントのアタッチされたツリーにない場合は、 ``None`` が返されます。"

#: ../../docstring of discord.Interaction.command:7
msgid "Optional[Union[:class:`app_commands.Command`, :class:`app_commands.ContextMenu`]]"
msgstr "Optional[Union[:class:`app_commands.Command`, :class:`app_commands.ContextMenu`]]"

#: ../../docstring of discord.Interaction.response:1
msgid "Returns an object responsible for handling responding to the interaction."
msgstr "インタラクションへの応答をするためのオブジェクトを返します。"

#: ../../docstring of discord.Interaction.response:3
msgid "A response can only be done once. If secondary messages need to be sent, consider using :attr:`followup` instead."
msgstr "応答は一度だけ行うことができます。複数回にわたってメッセージを送信する必要がある場合は、代わりに :attr:`followup` を使用することを検討してください。"

#: ../../docstring of discord.Interaction.response:6
msgid ":class:`InteractionResponse`"
msgstr ":class:`InteractionResponse`"

#: ../../docstring of discord.Interaction.followup:1
msgid "Returns the follow up webhook for follow up interactions."
msgstr "フォローアップのインタラクションのためのフォローアップウェブフックを返します。"

#: ../../docstring of discord.Interaction.followup:3
msgid ":class:`Webhook`"
msgstr ":class:`Webhook`"

#: ../../../discord/interactions.py:docstring of discord.Interaction.created_at:1
msgid "When the interaction was created."
msgstr "インタラクションが作成された時間を示します。"

#: ../../../discord/interactions.py:docstring of discord.Interaction.created_at:3
#: ../../../discord/interactions.py:docstring of discord.Interaction.expires_at:3
#: ../../../discord/interactions.py:docstring of discord.InteractionMessage.created_at:3
#: ../../../discord/message.py:docstring of discord.MessageInteraction.created_at:3
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandChannel.created_at:3
msgid ":class:`datetime.datetime`"
msgstr ":class:`datetime.datetime`"

#: ../../../discord/interactions.py:docstring of discord.Interaction.expires_at:1
msgid "When the interaction expires."
msgstr "インタラクションが期限切れになった時の時間を示します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.is_expired:1
msgid ":class:`bool`: Returns ``True`` if the interaction is expired."
msgstr ":class:`bool`：インタラクションが期限切れした場合には ``True`` を返します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:1
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:1
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.delete_original_response:1
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:1
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:1
msgid "|coro|"
msgstr "|coro|"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:3
msgid "Fetches the original interaction response message associated with the interaction."
msgstr "インタラクションに関連付けられた元のメッセージを取得します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:5
msgid "If the interaction response was a newly created message (i.e. through :meth:`InteractionResponse.send_message` or :meth:`InteractionResponse.defer`, where ``thinking`` is ``True``) then this returns the message that was sent using that response. Otherwise, this returns the message that triggered the interaction (i.e. through a component)."
msgstr "もしインタラクションの応答が新しく作成されたメッセージである場合（ :meth:`InteractionResponse.send_message` や ``thinking`` が ``True`` の :meth:`InteractionResponse.defer` 等）はそのメッセージを返します。それ以外の場合は、インタラクションが作成されたメッセージを返します。（コンポーネント 等）"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:10
msgid "Repeated calls to this will return a cached value."
msgstr "これを繰り返し呼び出すと、キャッシュされた値が返されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.delete_original_response:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.pong:0
msgid "Raises"
msgstr "例外"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:12
msgid "Fetching the original response message failed."
msgstr "元のインタラクションのメッセージの取得に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:13
msgid "The channel for the message could not be resolved."
msgstr "メッセージのチャンネルの解決ができなかった場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:14
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:33
msgid "The interaction response message does not exist."
msgstr "インタラクションの応答のメッセージが存在しなかった場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.add_files:0
msgid "Returns"
msgstr "戻り値"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:16
msgid "The original interaction response message."
msgstr "元のインタラクションの応答メッセージ。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.original_response:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.add_files:0
msgid "Return type"
msgstr "戻り値の型"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:3
msgid "Edits the original interaction response message."
msgstr "元のインタラクションの応答メッセージを編集します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:5
msgid "This is a lower level interface to :meth:`InteractionMessage.edit` in case you do not want to fetch the message and save an HTTP request."
msgstr "これは :meth:`InteractionMessage.edit` の下位互換のインターフェースで、メッセージを取得及び、HTTPリクエストを保存することが必要でない場合に使用します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:8
msgid "This method is also the only way to edit the original message if the message sent was ephemeral."
msgstr "また、一時的なメッセージを送った場合、この方法でのみメッセージを編集することができます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:0
#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:0
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:0
msgid "Parameters"
msgstr "パラメータ"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:11
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:5
msgid "The content to edit the message with or ``None`` to clear it."
msgstr "メッセージの内容を編集する場合はそのメッセージを、内容を削除する際は、``None`` を指定します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:13
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:8
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:7
msgid "A list of embeds to edit the message with."
msgstr "メッセージを編集するための埋め込みのリスト。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:15
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:10
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:9
msgid "The embed to edit the message with. ``None`` suppresses the embeds. This should not be mixed with the ``embeds`` parameter."
msgstr "メッセージを編集するための埋め込み。 ``None`` を渡すと埋め込みが除去されます。 ``embeds`` パラメータと同時に使用できません。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:18
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:13
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:12
msgid "A list of attachments to keep in the message as well as new files to upload. If ``[]`` is passed then all attachments are removed."
msgstr "メッセージ内で残す添付ファイルと、新規にアップロードする添付ファイルのリスト。 ``[]`` が渡された場合すべての添付ファイルが除去されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:23
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:18
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:17
msgid "New files will always appear after current attachments."
msgstr "新しいファイルは常に現在の添付ファイルのあとに表示されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:25
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:25
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:19
msgid "Controls the mentions being processed in this message. See :meth:`.abc.Messageable.send` for more information."
msgstr "このメッセージで処理されるメンションを制御します。詳細は :meth:`.abc.Messageable.send` を参照してください。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:28
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:20
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:22
msgid "The updated view to update this message with. If ``None`` is passed then the view is removed."
msgstr "このメッセージを更新するために更新されたビュー。 ``None`` が渡された場合、ビューは削除されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:32
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:33
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:32
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.add_files:10
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.remove_attachments:10
msgid "Editing the message failed."
msgstr "メッセージの編集に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:34
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:33
msgid "Edited a message that is not yours."
msgstr "自分以外のメッセージを編集しようとした場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:35
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:34
msgid "You specified both ``embed`` and ``embeds``"
msgstr "``embed`` と ``embeds`` の両方を指定した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:36
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:44
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:35
msgid "The length of ``embeds`` was invalid."
msgstr "``embeds`` の長さが無効だった場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:38
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:37
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.add_files:13
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.remove_attachments:13
msgid "The newly edited message."
msgstr "編集された新しいメッセージ。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.edit_original_response:39
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:38
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.add_files:14
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.remove_attachments:14
msgid ":class:`InteractionMessage`"
msgstr ":class:`InteractionMessage`"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.delete_original_response:3
msgid "Deletes the original interaction response message."
msgstr "元のインタラクション応答メッセージを削除します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.delete_original_response:5
msgid "This is a lower level interface to :meth:`InteractionMessage.delete` in case you do not want to fetch the message and save an HTTP request."
msgstr "これは :meth:`InteractionMessage.delete` の下位互換のインターフェースで、メッセージを取得及び、HTTPリクエストを保存することが必要でない場合に使用します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.delete_original_response:8
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.delete:11
msgid "Deleting the message failed."
msgstr "メッセージの削除に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.delete_original_response:9
msgid "The interaction response message does not exist or has already been deleted."
msgstr "インタラクションの応答のメッセージが存在しなかった場合、または既に削除されていた場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.delete_original_response:10
msgid "Deleted a message that is not yours."
msgstr "自分以外のメッセージを削除しようとした場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:3
msgid "Translates a string using the set :class:`~discord.app_commands.Translator`."
msgstr ":class:`~discord.app_commands.Translator` を使用して文字列を翻訳します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:7
msgid "The string to translate. :class:`~discord.app_commands.locale_str` can be used to add more context, information, or any metadata necessary."
msgstr "翻訳する文字列。 :class:`~discord.app_commands.locale_str` を使用して、文脈、情報、または必要なメタデータを追加できます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:11
msgid "The locale to use, this is handy if you want the translation for a specific locale. Defaults to the user's :attr:`.locale`."
msgstr "使用するロケール。特定のロケールの翻訳が必要な場合に便利です。既定値はユーザの :attr:`.locale` です。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:15
msgid "The extraneous data that is being translated. If not specified, either :attr:`.command` or :attr:`.message` will be passed, depending on which is available in the context."
msgstr "翻訳される追加のデータ。指定されない場合は、 :attr:`.command` または :attr:`.message` のうち利用できるものが渡されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:20
msgid "The translated string, or ``None`` if a translator was not set."
msgstr "翻訳された文字列、またはトランスレータが設定されていない場合 ``None`` 。"

#: ../../../discord/interactions.py:docstring of discord.interactions.Interaction.translate:21
#: ../../../discord/components.py:docstring of discord.components.Button:23
#: ../../../discord/components.py:docstring of discord.components.Button:29
#: ../../../discord/components.py:docstring of discord.components.Button:41
#: ../../../discord/components.py:docstring of discord.components.SelectMenu:23
msgid "Optional[:class:`str`]"
msgstr "Optional[:class:`str`]"

#: ../../interactions/api.rst:24
msgid "InteractionResponse"
msgstr "InteractionResponse"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse:1
msgid "Represents a Discord interaction response."
msgstr "Discordのインタラクション応答を表します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse:3
msgid "This type can be accessed through :attr:`Interaction.response`."
msgstr "この型は :attr:`Interaction.response` からアクセスできます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.is_done:1
msgid ":class:`bool`: Indicates whether an interaction response has been done before."
msgstr ":class:`bool`: インタラクションに既に応答したか。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.is_done:3
msgid "An interaction can only be responded to once."
msgstr "インタラクションは一度だけ応答できます。"

#: ../../../discord/interactions.py:docstring of discord.InteractionResponse.type:1
msgid "The type of response that was sent, ``None`` if response is not done."
msgstr "送信された応答の種類。まだ応答していない場合は ``None`` です。"

#: ../../../discord/interactions.py:docstring of discord.InteractionResponse.type:3
msgid ":class:`InteractionResponseType`"
msgstr ":class:`InteractionResponseType`"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:3
msgid "Defers the interaction response."
msgstr "インタラクションの応答を遅らせます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:5
msgid "This is typically used when the interaction is acknowledged and a secondary action will be done later."
msgstr "これは通常、インタラクションを認識した後、後で他のことを実行する場合に使われます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:8
msgid "This is only supported with the following interaction types:"
msgstr "以下のインタラクションでのみサポートされています："

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:10
msgid ":attr:`InteractionType.application_command`"
msgstr ":attr:`InteractionType.application_command`"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:11
msgid ":attr:`InteractionType.component`"
msgstr ":attr:`InteractionType.component`"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:12
msgid ":attr:`InteractionType.modal_submit`"
msgstr ":attr:`InteractionType.modal_submit`"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:14
msgid "Indicates whether the deferred message will eventually be ephemeral. This only applies to :attr:`InteractionType.application_command` interactions, or if ``thinking`` is ``True``."
msgstr "遅延メッセージが一時的かどうか。 :attr:`InteractionType.application_command` のインタラクション、または ``thinking`` が ``True`` の場合にのみ適用されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:17
msgid "Indicates whether the deferred type should be :attr:`InteractionResponseType.deferred_channel_message` instead of the default :attr:`InteractionResponseType.deferred_message_update` if both are valid. In UI terms, this is represented as if the bot is thinking of a response. It is your responsibility to eventually send a followup message via :attr:`Interaction.followup` to make this thinking state go away. Application commands (AKA Slash commands) cannot use :attr:`InteractionResponseType.deferred_message_update`."
msgstr "遅延の種類を両方が利用可能の場合にデフォルトの :attr:`InteractionResponseType.deferred_message_update` ではなく :attr:`InteractionResponseType.deferred_channel_message` にするか。Discordのアプリ上では考え中として表示されます。 :attr:`Interaction.followup` で考え中の表示を解除できます。アプリケーションコマンド（スラッシュコマンド）は :attr:`InteractionResponseType.deferred_message_update` を使えません。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:24
msgid "Deferring the interaction failed."
msgstr "インタラクションの遅延に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.defer:25
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.pong:8
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:45
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:35
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_modal:9
msgid "This interaction has already been responded to before."
msgstr "既にインタラクションに応答していた場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.pong:3
msgid "Pongs the ping interaction."
msgstr "Pingのインタラクションに応答します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.pong:5
msgid "This should rarely be used."
msgstr "ほとんどの場合使われません。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.pong:7
msgid "Ponging the interaction failed."
msgstr "インタラクションの応答に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:3
msgid "Responds to this interaction by sending a message."
msgstr "インタラクションにメッセージで応答します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:5
msgid "The content of the message to send."
msgstr "送信するメッセージの内容。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:7
msgid "A list of embeds to send with the content. Maximum of 10. This cannot be mixed with the ``embed`` parameter."
msgstr "送信するリッチな埋め込みのリスト。最大10個です。 ``embed`` パラメータと同時に使用できません。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:10
msgid "The rich embed for the content to send. This cannot be mixed with ``embeds`` parameter."
msgstr "送信するリッチな埋め込み。 ``embeds`` パラメータと同時に使用できません。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:13
msgid "The file to upload."
msgstr "アップロードするファイル。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:15
msgid "A list of files to upload. Must be a maximum of 10."
msgstr "アップロードするファイルのリスト。ファイル数は最大で10個まででなくてはいけません。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:17
msgid "Indicates if the message should be sent using text-to-speech."
msgstr "メッセージが音声合成で送信されるべきかどうかを示します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:19
msgid "The view to send with the message."
msgstr "送信するビュー。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:21
msgid "Indicates if the message should only be visible to the user who started the interaction. If a view is sent with an ephemeral message and it has no timeout set then the timeout is set to 15 minutes."
msgstr "メッセージがインタラクションを開始したユーザーだけに表示されるかどうか。もしビューが一時的なメッセージで送信されている、かつタイムアウトが設定されていない場合、タイムアウトは15分に設定されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:28
msgid "Whether to suppress embeds for the message. This sends the message without any embeds if set to ``True``."
msgstr "メッセージの埋め込みを抑制するかどうか。これが ``True`` に設定されている場合、埋め込みなしでメッセージを送信します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:30
msgid "Whether to suppress push and desktop notifications for the message. This will increment the mention counter in the UI, but will not actually send a notification."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:35
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:25
msgid "If provided, the number of seconds to wait in the background before deleting the message we just sent. If the deletion fails, then it is silently ignored."
msgstr "指定すると、これはメッセージを送信したあと削除するまでにバックグラウンドで待機する秒数となります。もし削除が失敗しても、それは静かに無視されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:42
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.reply:12
msgid "Sending the message failed."
msgstr "メッセージの送信に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_message:43
msgid "You specified both ``embed`` and ``embeds`` or ``file`` and ``files``."
msgstr "``embed`` と ``embeds`` または ``file`` と ``files`` の両方を指定した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:3
msgid "Responds to this interaction by editing the original message of a component or modal interaction."
msgstr "コンポーネントまたはモーダルのインタラクションに編集で応答します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:6
msgid "The new content to replace the message with. ``None`` removes the content."
msgstr "現在のメッセージと置き換える新しい内容。 ``None`` を指定すると内容が削除されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:23
msgid "Controls the mentions being processed in this message. See :meth:`.Message.edit` for more information."
msgstr "このメッセージで処理されるメンションを制御します。詳細は :meth:`.Message.edit` を参照してください。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:26
msgid "If provided, the number of seconds to wait in the background before deleting the message we just edited. If the deletion fails, then it is silently ignored."
msgstr "もし指定したなら、これはメッセージを編集したあと待機し削除するまでの秒数です。もし削除が失敗しても、それは静かに無視されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.edit_message:34
msgid "You specified both ``embed`` and ``embeds``."
msgstr "``embed`` と ``embeds`` の両方を指定した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_modal:3
msgid "Responds to this interaction by sending a modal."
msgstr "インタラクションにモーダルで応答します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_modal:5
msgid "The modal to send."
msgstr "送信するモーダル。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.send_modal:8
msgid "Sending the modal failed."
msgstr "モーダルの送信に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.autocomplete:3
msgid "Responds to this interaction by giving the user the choices they can use."
msgstr "ユーザーが使用できる選択肢を与えることにより、このインタラクションに応答します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.autocomplete:5
msgid "The list of new choices as the user is typing."
msgstr "応答する選択肢。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.autocomplete:8
msgid "Sending the choices failed."
msgstr "選択肢の送信に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionResponse.autocomplete:9
msgid "This interaction cannot respond with autocomplete."
msgstr "このインタラクションに選択肢で応答できない場合。"

#: ../../interactions/api.rst:32
msgid "InteractionMessage"
msgstr "InteractionMessage"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage:1
msgid "Represents the original interaction response message."
msgstr "元のインタラクション応答メッセージを表します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage:3
msgid "This allows you to edit or delete the message associated with the interaction response. To retrieve this object see :meth:`Interaction.original_response`."
msgstr "このオブジェクトでインタラクションの応答のメッセージを編集、または削除できます。このオブジェクトを生成するには :meth:`Interaction.original_response` を参照して下さい。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage:6
msgid "This inherits from :class:`discord.Message` with changes to :meth:`edit` and :meth:`delete` to work."
msgstr "これは :meth:`edit` と :meth:`delete` が機能するように変更された上で :class:`discord.Message` を継承しています。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.edit:3
msgid "Edits the message."
msgstr "メッセージを編集します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.add_files:3
msgid "Adds new files to the end of the message attachments."
msgstr "メッセージの添付ファイルの末尾に新しいファイルを追加します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.add_files:7
msgid "New files to add to the message."
msgstr "メッセージに追加する新しいファイル。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.add_files:11
#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.remove_attachments:11
msgid "Tried to edit a message that isn't yours."
msgstr "自分以外のメッセージを編集しようとした場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.remove_attachments:3
msgid "Removes attachments from the message."
msgstr "メッセージの添付ファイルを削除します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.remove_attachments:7
msgid "Attachments to remove from the message."
msgstr "メッセージから削除する添付ファイル。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.delete:3
msgid "Deletes the message."
msgstr "メッセージを削除します。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.delete:5
msgid "If provided, the number of seconds to wait before deleting the message. The waiting is done in the background and deletion failures are ignored."
msgstr "指定された場合、メッセージを削除するまでの待機秒数。待機はバックグラウンドで行われ、削除の失敗は無視されます。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.delete:9
msgid "You do not have proper permissions to delete the message."
msgstr "メッセージを削除するための適切な権限がない場合。"

#: ../../../discord/interactions.py:docstring of discord.interactions.InteractionMessage.delete:10
msgid "The message was deleted already."
msgstr "メッセージがすでに削除されている場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:3
msgid "Adds a reaction to the message."
msgstr "メッセージにリアクションを追加します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:5
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:5
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:5
msgid "The emoji may be a unicode emoji or a custom guild :class:`Emoji`."
msgstr "絵文字はユニコード絵文字かカスタムギルド絵文字の :class:`Emoji` でないといけません。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:7
msgid "You must have :attr:`~Permissions.read_message_history` to do this. If nobody else has reacted to the message using this emoji, :attr:`~Permissions.add_reactions` is required."
msgstr "これを行うためには、そのチャンネルにて :attr:`~Permissions.read_message_history` が必要です。 もし、他の人がその絵文字でリアクションしていない場合、さらに :attr:`~Permissions.add_reactions` が必要です。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:13
msgid "``emoji`` parameter is now positional-only."
msgstr "``emoji`` 引数は位置限定引数になりました。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:15
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:11
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:13
msgid "This function will now raise :exc:`TypeError` instead of ``InvalidArgument``."
msgstr "この関数は ``InvalidArgument`` の代わりに :exc:`TypeError` を送出します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:19
msgid "The emoji to react with."
msgstr "リアクションとして追加する絵文字。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:22
msgid "Adding the reaction failed."
msgstr "リアクションの追加に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:23
msgid "You do not have the proper permissions to react to the message."
msgstr "メッセージにリアクションを付けるのに必要な権限がない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:24
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:20
msgid "The emoji you specified was not found."
msgstr "指定された絵文字が見つからなかった場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.add_reaction:25
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:21
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:25
msgid "The emoji parameter is invalid."
msgstr "emojiパラメータが無効の場合。"

#: ../../docstring of discord.InteractionMessage.clean_content:1
msgid "A property that returns the content in a \"cleaned up\" manner. This basically means that mentions are transformed into the way the client shows it. e.g. ``<#id>`` will transform into ``#name``."
msgstr "クリーンアップされたメッセージ内容を返すプロパティ。基本的に、これはメンションをクライアントが表示できるようにする、という意味です。例えば、 ``<#id>`` は ``#name`` に変換されます。"

#: ../../docstring of discord.InteractionMessage.clean_content:6
msgid "This will also transform @everyone and @here mentions into non-mentions."
msgstr "また、これは @everyone メンション や @here メンションを、メンション機能の無いメッセージに変換します。"

#: ../../docstring of discord.InteractionMessage.clean_content:11
msgid "This *does not* affect markdown. If you want to escape or remove markdown then use :func:`utils.escape_markdown` or :func:`utils.remove_markdown` respectively, along with this function."
msgstr "これはマークダウンには影響 *しません* 。マークダウンをエスケープまたは削除したい場合は、この関数とともに :func:`utils.escape_markdown` か :func:`utils.remove_markdown` を使用してください。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:3
msgid "Clears a specific reaction from the message."
msgstr "メッセージから特定のリアクションを消去します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:7
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reactions:5
msgid "You must have :attr:`~Permissions.manage_messages` to do this."
msgstr "これを行うには、 :attr:`~Permissions.manage_messages` が必要です。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:15
msgid "The emoji to clear."
msgstr "消去された絵文字です。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:18
msgid "Clearing the reaction failed."
msgstr "リアクションの除去に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reaction:19
msgid "You do not have the proper permissions to clear the reaction."
msgstr "リアクションを除去するのに必要な権限がない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reactions:3
msgid "Removes all the reactions from the message."
msgstr "全てのリアクションをメッセージから消去します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reactions:7
msgid "Removing the reactions failed."
msgstr "リアクションの除去に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.clear_reactions:8
msgid "You do not have the proper permissions to remove all the reactions."
msgstr "リアクションの除去に必要な権限を持っていない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:3
msgid "Creates a public thread from this message."
msgstr "メッセージからパブリックスレッドを作成します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:5
msgid "You must have :attr:`~discord.Permissions.create_public_threads` in order to create a public thread from a message."
msgstr "メッセージから公開スレッドを作成するには、 :attr:`~discord.Permissions.create_public_threads` 権限が必要です。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:8
msgid "The channel this message belongs in must be a :class:`TextChannel`."
msgstr "このメッセージが属するチャンネルは、 :class:`TextChannel` でなければなりません。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:12
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:37
msgid "The name of the thread."
msgstr "スレッドの名前。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:14
msgid "The duration in minutes before a thread is automatically hidden from the channel list. If not provided, the channel's default auto archive duration is used.  Must be one of ``60``, ``1440``, ``4320``, or ``10080``, if provided."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:14
msgid "The duration in minutes before a thread is automatically hidden from the channel list. If not provided, the channel's default auto archive duration is used."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:17
msgid "Must be one of ``60``, ``1440``, ``4320``, or ``10080``, if provided."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:19
msgid "Specifies the slowmode rate limit for user in this channel, in seconds. The maximum value possible is ``21600``. By default no slowmode rate limit if this is ``None``."
msgstr "このチャンネルの秒単位での低速モードレート制限。 最大値は ``21600`` です。デフォルトは ``None`` でこの場合は低速モードレート制限が無しとなります。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:23
msgid "The reason for creating a new thread. Shows up on the audit log."
msgstr "スレッドを作成する理由。監査ログに表示されます。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:26
msgid "You do not have permissions to create a thread."
msgstr "スレッドを作成する権限を持っていない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:27
msgid "Creating the thread failed."
msgstr "スレッドの作成に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:28
msgid "This message does not have guild info attached."
msgstr "メッセージがギルド情報を持っていない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:30
msgid "The created thread."
msgstr "作成されたスレッド"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.create_thread:31
msgid ":class:`.Thread`"
msgstr ":class:`Thread`"

#: ../../../discord/interactions.py:docstring of discord.InteractionMessage.created_at:1
msgid "The message's creation time in UTC."
msgstr "UTCの、メッセージが作成された時刻。"

#: ../../../discord/interactions.py:docstring of discord.InteractionMessage.edited_at:1
msgid "An aware UTC datetime object containing the edited time of the message."
msgstr "メッセージの編集時刻を含む、aware UTC datetime オブジェクト。"

#: ../../../discord/interactions.py:docstring of discord.InteractionMessage.edited_at:3
msgid "Optional[:class:`datetime.datetime`]"
msgstr "Optional[:class:`datetime.datetime`]"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.fetch:3
msgid "Fetches the partial message to a full :class:`Message`."
msgstr "部分的なメッセージを完全な :class:`Message` にフェッチします。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.fetch:5
msgid "The message was not found."
msgstr "メッセージが見つからなかった場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.fetch:6
msgid "You do not have the permissions required to get a message."
msgstr "メッセージを取得するために必要な権限がありません。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.fetch:7
msgid "Retrieving the message failed."
msgstr "メッセージの取得に失敗しました。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.fetch:9
msgid "The full message."
msgstr "完全なメッセージ。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.fetch:10
msgid ":class:`Message`"
msgstr ":class:`Message`"

#: ../../../discord/interactions.py:docstring of discord.message.Message.is_system:1
msgid ":class:`bool`: Whether the message is a system message."
msgstr ":class:`bool`: メッセージがシステムメッセージであるかどうか。"

#: ../../../discord/interactions.py:docstring of discord.message.Message.is_system:3
msgid "A system message is a message that is constructed entirely by the Discord API in response to something."
msgstr "システムメッセージは、何かの応答としてDiscord APIによって構築されるメッセージです。"

#: ../../../discord/interactions.py:docstring of discord.InteractionMessage.jump_url:1
msgid "Returns a URL that allows the client to jump to this message."
msgstr "クライアントがこのメッセージにジャンプすることのできるURLを返します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.pin:3
msgid "Pins the message."
msgstr "メッセージをピン留めします。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.pin:5
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.unpin:5
msgid "You must have :attr:`~Permissions.manage_messages` to do this in a non-private channel context."
msgstr "プライベートチャンネルでない通常のチャンネルで行うには、 :attr:`~Permissions.manage_messages` が必要です。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.pin:8
msgid "The reason for pinning the message. Shows up on the audit log."
msgstr "メッセージを固定した理由。監査ログに表示されます。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.pin:13
msgid "You do not have permissions to pin the message."
msgstr "このメッセージをピン留めする権限を持っていない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.pin:14
#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.unpin:14
msgid "The message or channel was not found or deleted."
msgstr "ピン留めするメッセージやチャンネルが見つからなかったか、既に削除されている場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.pin:15
msgid "Pinning the message failed, probably due to the channel     having more than 50 pinned messages."
msgstr "チャンネルにすでに50個ピン留めされたメッセージがあるなどの理由で、メッセージのピン留めに失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.publish:3
msgid "Publishes this message to the channel's followers."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.publish:5
msgid "The message must have been sent in a news channel. You must have :attr:`~Permissions.send_messages` to do this."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.publish:8
msgid "If the message is not your own then :attr:`~Permissions.manage_messages` is also needed."
msgstr "自身のメッセージ以外の場合は :attr:`~Permissions.manage_messages` も必要です。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.publish:11
msgid "You do not have the proper permissions to publish this message     or the channel is not a news channel."
msgstr ""

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.publish:12
msgid "Publishing the message failed."
msgstr "メッセージの公開に失敗した場合。"

#: ../../docstring of discord.InteractionMessage.raw_channel_mentions:1
msgid "A property that returns an array of channel IDs matched with the syntax of ``<#channel_id>`` in the message content."
msgstr "メッセージのコンテンツにある ``<#channel_id>`` の構文にマッチするチャンネル ID の配列を返すプロパティです。"

#: ../../docstring of discord.InteractionMessage.raw_channel_mentions:4
#: ../../docstring of discord.InteractionMessage.raw_mentions:7
#: ../../docstring of discord.InteractionMessage.raw_role_mentions:4
msgid "List[:class:`int`]"
msgstr "List[:class:`int`]"

#: ../../docstring of discord.InteractionMessage.raw_mentions:1
msgid "A property that returns an array of user IDs matched with the syntax of ``<@user_id>`` in the message content."
msgstr "メッセージのコンテンツにある ``<#user_id>`` の構文にマッチするユーザ ID の配列を返すプロパティです。"

#: ../../docstring of discord.InteractionMessage.raw_mentions:4
msgid "This allows you to receive the user IDs of mentioned users even in a private message context."
msgstr "これによって、メッセージがプライベートチャンネル内のものであってもメンションされたユーザのIDを取得できます。"

#: ../../docstring of discord.InteractionMessage.raw_role_mentions:1
msgid "A property that returns an array of role IDs matched with the syntax of ``<@&role_id>`` in the message content."
msgstr "メッセージのコンテンツにある ``<#role_id>`` の構文にマッチするロール ID の配列を返すプロパティです。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:3
msgid "Remove a reaction by the member from the message."
msgstr "メッセージからメンバーによるリアクションを除去します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:7
msgid "If the reaction is not your own (i.e. ``member`` parameter is not you) then :attr:`~Permissions.manage_messages` is needed."
msgstr "もしリアクションがあなたのものではなければ(つまり、 ``member`` パラメーターがあなたでないなら)、 :attr:`~Permissions.manage_messages` も必要になります。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:10
msgid "The ``member`` parameter must represent a member and meet the :class:`abc.Snowflake` abc."
msgstr "``member`` パラメータはメンバーを示し :class:`abc.Snowflake` 抽象基底クラスを満たす必要があります。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:17
msgid "The emoji to remove."
msgstr "除去する絵文字。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:19
msgid "The member for which to remove the reaction."
msgstr "リアクションを除去する対象のメンバー。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:22
msgid "Removing the reaction failed."
msgstr "リアクションの除去に失敗した場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:23
msgid "You do not have the proper permissions to remove the reaction."
msgstr "リアクションの除去に必要な権限を持っていない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.remove_reaction:24
msgid "The member or emoji you specified was not found."
msgstr "指定されたメンバーや絵文字が見つからなかった場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.reply:3
msgid "A shortcut method to :meth:`.abc.Messageable.send` to reply to the :class:`.Message`."
msgstr ":class:`.Message` に返信するための :meth:`.abc.Messageable.send` のショートカットメソッド。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.reply:8
msgid "This function will now raise :exc:`TypeError` or :exc:`ValueError` instead of ``InvalidArgument``."
msgstr "この関数は ``InvalidArgument`` の代わりに :exc:`TypeError` または :exc:`ValueError` を送出するようになりました。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.reply:13
msgid "You do not have the proper permissions to send the message."
msgstr "メッセージを送信する適切な権限がない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.reply:14
msgid "The ``files`` list is not of the appropriate size"
msgstr "``files`` リストの大きさが適切でない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.reply:15
msgid "You specified both ``file`` and ``files``."
msgstr "``file`` と ``filess`` の両方を指定した場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.reply:17
msgid "The message that was sent."
msgstr "送信されたメッセージ。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.reply:18
msgid ":class:`.Message`"
msgstr ":class:`.Message`"

#: ../../docstring of discord.InteractionMessage.system_content:1
msgid "A property that returns the content that is rendered regardless of the :attr:`Message.type`."
msgstr ":attr:`Message.type` に関わらず、レンダリングされた際のメッセージ内容を返すプロパティ。"

#: ../../docstring of discord.InteractionMessage.system_content:4
msgid "In the case of :attr:`MessageType.default` and :attr:`MessageType.reply`\\, this just returns the regular :attr:`Message.content`. Otherwise this returns an English message denoting the contents of the system message."
msgstr ":attr:`MessageType.default` と :attr:`MessageType.reply` の場合、これは :attr:`Message.content` と同じものを返すだけです。しかしそれ以外の場合は、システムメッセージの英語版を返します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.to_reference:1
msgid "Creates a :class:`~discord.MessageReference` from the current message."
msgstr "現在のメッセージから :class:`~discord.MessageReference` を作成します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.to_reference:5
msgid "Whether replying using the message reference should raise :class:`HTTPException` if the message no longer exists or Discord could not fetch the message."
msgstr "メッセージ参照を使用して返信するとき、メッセージが存在しなくなった場合、またはDiscordがメッセージを取得できなかった場合、 :class:`HTTPException` を送出させるかどうか。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.to_reference:11
msgid "The reference to this message."
msgstr "メッセージへの参照。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.to_reference:12
msgid ":class:`~discord.MessageReference`"
msgstr ":class:`~discord.MessageReference`"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.unpin:3
msgid "Unpins the message."
msgstr "メッセージのピン留めを外します。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.unpin:8
msgid "The reason for unpinning the message. Shows up on the audit log."
msgstr "メッセージのピン留めを解除した理由。監査ログに表示されます。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.unpin:13
msgid "You do not have permissions to unpin the message."
msgstr "このメッセージのピン留めを外す権限を持っていない場合。"

#: ../../../discord/interactions.py:docstring of discord.message.PartialMessage.unpin:15
msgid "Unpinning the message failed."
msgstr "メッセージのピン留め解除に失敗した場合。"

#: ../../interactions/api.rst:41
msgid "MessageInteraction"
msgstr "MessageInteraction"

#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:1
msgid "Represents the interaction that a :class:`Message` is a response to."
msgstr ":class:`Message` が応答したインタラクションを表します。"

#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:9
msgid "Checks if two message interactions are equal."
msgstr "二つのメッセージインタラクションが等しいかを比較します。"

#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:13
msgid "Checks if two message interactions are not equal."
msgstr "二つのメッセージインタラクションが等しくないかを比較します。"

#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:17
msgid "Returns the message interaction's hash."
msgstr "メッセージインタラクションのハッシュ値を返します。"

#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:21
msgid "The interaction ID."
msgstr "インタラクションのID。"

#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:33
msgid "The name of the interaction."
msgstr "インタラクションの名前。"

#: ../../../discord/message.py:docstring of discord.message.MessageInteraction:39
msgid "The user or member that invoked the interaction."
msgstr "インタラクションを行ったユーザーまたはメンバー。"

#: ../../../discord/message.py:docstring of discord.MessageInteraction.created_at:1
msgid "The interaction's creation time in UTC."
msgstr "UTCの、インタラクションが作成された時刻。"

#: ../../interactions/api.rst:49
msgid "Component"
msgstr "Component"

#: ../../../discord/components.py:docstring of discord.components.Component:1
msgid "Represents a Discord Bot UI Kit Component."
msgstr "Discord Bot UI Kitのコンポーネント。"

#: ../../../discord/components.py:docstring of discord.components.Component:3
msgid "Currently, the only components supported by Discord are:"
msgstr "現在、Discordでサポートされているコンポーネントは次のとおりです："

#: ../../../discord/components.py:docstring of discord.components.Component:5
msgid ":class:`ActionRow`"
msgstr ":class:`ActionRow`"

#: ../../../discord/components.py:docstring of discord.components.Component:6
msgid ":class:`Button`"
msgstr ":class:`Button`"

#: ../../../discord/components.py:docstring of discord.components.Component:7
msgid ":class:`SelectMenu`"
msgstr ":class:`SelectMenu`"

#: ../../../discord/components.py:docstring of discord.components.Component:8
msgid ":class:`TextInput`"
msgstr ":class:`TextInput`"

#: ../../../discord/components.py:docstring of discord.components.Component:10
msgid "This class is abstract and cannot be instantiated."
msgstr "これは抽象クラスでインスタンス化できません。"

#: ../../../discord/components.py:docstring of discord.Component.type:1
#: ../../../discord/components.py:docstring of discord.ActionRow.type:1
#: ../../../discord/components.py:docstring of discord.Button.type:1
#: ../../../discord/components.py:docstring of discord.components.SelectMenu:15
#: ../../../discord/components.py:docstring of discord.TextInput.type:1
msgid "The type of component."
msgstr "コンポーネントの種類。"

#: ../../../discord/components.py:docstring of discord.Component.type:3
#: ../../../discord/components.py:docstring of discord.ActionRow.type:3
#: ../../../discord/components.py:docstring of discord.Button.type:3
#: ../../../discord/components.py:docstring of discord.components.SelectMenu:17
#: ../../../discord/components.py:docstring of discord.TextInput.type:3
msgid ":class:`ComponentType`"
msgstr ":class:`ComponentType`"

#: ../../interactions/api.rst:57
msgid "ActionRow"
msgstr "ActionRow"

#: ../../../discord/components.py:docstring of discord.components.ActionRow:1
msgid "Represents a Discord Bot UI Kit Action Row."
msgstr "Discord Bot UI Kitのアクション行。"

#: ../../../discord/components.py:docstring of discord.components.ActionRow:3
msgid "This is a component that holds up to 5 children components in a row."
msgstr "これは、最大5個の子コンポーネントを並べて保持するコンポーネントです。"

#: ../../../discord/components.py:docstring of discord.components.ActionRow:5
#: ../../../discord/components.py:docstring of discord.components.Button:3
msgid "This inherits from :class:`Component`."
msgstr "これは :class:`Component` から継承されます。"

#: ../../../discord/components.py:docstring of discord.components.ActionRow:11
msgid "The children components that this holds, if any."
msgstr "存在する場合、このコンポーネントの子コンポーネント。"

#: ../../../discord/components.py:docstring of discord.components.ActionRow:13
msgid "List[Union[:class:`Button`, :class:`SelectMenu`, :class:`TextInput`]]"
msgstr "List[Union[:class:`Button`, :class:`SelectMenu`, :class:`TextInput`]]"

#: ../../interactions/api.rst:65
#: ../../interactions/api.rst:447
msgid "Button"
msgstr "Button"

#: ../../../discord/components.py:docstring of discord.components.Button:1
msgid "Represents a button from the Discord Bot UI Kit."
msgstr "Discord Bot UI Kitのボタン。"

#: ../../../discord/components.py:docstring of discord.components.Button:7
msgid "The user constructible and usable type to create a button is :class:`discord.ui.Button` not this one."
msgstr "ボタンを作成するためにユーザーが構築でき利用できる型はこれではなく :class:`discord.ui.Button` です。"

#: ../../../discord/components.py:docstring of discord.components.Button:14
#: ../../../discord/ui/button.py:docstring of discord.ui.button.Button:5
#: ../../../discord/ui/button.py:docstring of discord.ui.Button.style:1
msgid "The style of the button."
msgstr "ボタンのスタイル。"

#: ../../../discord/components.py:docstring of discord.components.Button:16
msgid ":class:`.ButtonStyle`"
msgstr ":class:`.ButtonStyle`"

#: ../../../discord/components.py:docstring of discord.components.Button:20
#: ../../../discord/ui/button.py:docstring of discord.ui.button.Button:7
msgid "The ID of the button that gets received during an interaction. If this button is for a URL, it does not have a custom ID."
msgstr "インタラクション中に受け取るボタンID。これがURLボタンの場合はカスタムIDは設定できません。"

#: ../../../discord/components.py:docstring of discord.components.Button:27
#: ../../../discord/ui/button.py:docstring of discord.ui.button.Button:10
#: ../../../discord/ui/button.py:docstring of discord.ui.Button.url:1
msgid "The URL this button sends you to."
msgstr "ボタンの行き先のURL。"

#: ../../../discord/components.py:docstring of discord.components.Button:33
#: ../../../discord/ui/button.py:docstring of discord.ui.button.Button:12
#: ../../../discord/ui/button.py:docstring of discord.ui.Button.disabled:1
msgid "Whether the button is disabled or not."
msgstr "ボタンが無効化されているかどうか。"

#: ../../../discord/components.py:docstring of discord.components.Button:39
#: ../../../discord/ui/button.py:docstring of discord.ui.button.Button:14
#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:15
msgid "The label of the button, if any."
msgstr "存在する場合、ボタンのラベル。"

#: ../../../discord/components.py:docstring of discord.components.Button:45
#: ../../../discord/ui/button.py:docstring of discord.ui.button.Button:16
#: ../../../discord/ui/button.py:docstring of discord.ui.Button.emoji:1
msgid "The emoji of the button, if available."
msgstr "利用可能な場合、ボタンの絵文字。"

#: ../../../discord/components.py:docstring of discord.components.Button:47
msgid "Optional[:class:`PartialEmoji`]"
msgstr "Optional[:class:`PartialEmoji`]"

#: ../../interactions/api.rst:74
msgid "SelectMenu"
msgstr "SelectMenu"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:1
msgid "Represents a select menu from the Discord Bot UI Kit."
msgstr "Discord Bot UI Kitの選択メニュー。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:3
msgid "A select menu is functionally the same as a dropdown, however on mobile it renders a bit differently."
msgstr "選択メニューは機能的にはドロップダウンと同じですが、モバイル端末では少し違ったレンダリングがされます。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:8
msgid "The user constructible and usable type to create a select menu is :class:`discord.ui.Select` not this one."
msgstr "選択メニューを作成するためにユーザーが構築でき利用できる型はこれではなく :class:`discord.ui.Select` です。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:21
#: ../../../discord/ui/select.py:docstring of discord.ui.Select.custom_id:1
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.custom_id:1
#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.custom_id:1
#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.custom_id:1
msgid "The ID of the select menu that gets received during an interaction."
msgstr "インタラクション中に受け取る選択メニューID。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:27
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select:9
#: ../../../discord/ui/select.py:docstring of discord.ui.Select.placeholder:1
#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:12
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.placeholder:1
msgid "The placeholder text that is shown if nothing is selected, if any."
msgstr "存在する場合、何も選択されていないときに表示するプレースホルダーテキスト。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:33
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select:11
#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:14
#: ../../../discord/ui/select.py:docstring of discord.ui.select.RoleSelect:12
#: ../../../discord/ui/select.py:docstring of discord.ui.select.MentionableSelect:15
msgid "The minimum number of items that must be chosen for this select menu. Defaults to 1 and must be between 0 and 25."
msgstr "選択メニューにて選択しないといけない最小の項目数。デフォルトは1で、値は0以上25以下でないといけません。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:40
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select:14
#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:17
#: ../../../discord/ui/select.py:docstring of discord.ui.select.RoleSelect:15
#: ../../../discord/ui/select.py:docstring of discord.ui.select.MentionableSelect:18
msgid "The maximum number of items that must be chosen for this select menu. Defaults to 1 and must be between 1 and 25."
msgstr "選択メニューにて選択できる最大の項目数。デフォルトは1で、値は1以上25以下でないといけません。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:47
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select:17
#: ../../../discord/ui/select.py:docstring of discord.ui.Select.options:1
msgid "A list of options that can be selected in this menu."
msgstr "このメニューで選択できるオプションのリスト。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:49
msgid "List[:class:`SelectOption`]"
msgstr "List[:class:`SelectOption`]"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:53
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select:19
#: ../../../discord/ui/select.py:docstring of discord.ui.Select.disabled:1
#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:20
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.disabled:1
msgid "Whether the select is disabled or not."
msgstr "選択メニューが無効化されているかどうか。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:59
msgid "A list of channel types that are allowed to be chosen in this select menu."
msgstr "この選択メニューで選択できるチャンネルの種類のリスト。"

#: ../../../discord/components.py:docstring of discord.components.SelectMenu:61
msgid "List[:class:`.ChannelType`]"
msgstr "List[:class:`.ChannelType`]"

#: ../../interactions/api.rst:84
#: ../../interactions/api.rst:515
msgid "TextInput"
msgstr "TextInput"

#: ../../../discord/components.py:docstring of discord.components.TextInput:1
msgid "Represents a text input from the Discord Bot UI Kit."
msgstr "Discord Bot UI Kitのテキスト入力。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:4
msgid "The user constructible and usable type to create a text input is :class:`discord.ui.TextInput` not this one."
msgstr "テキスト入力を作成するためにユーザーが構築でき利用できる型はこれではなく :class:`discord.ui.TextInput` です。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:11
#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.custom_id:1
msgid "The ID of the text input that gets received during an interaction."
msgstr "インタラクション中に受け取るテキスト入力ID。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:17
#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:11
msgid "The label to display above the text input."
msgstr "テキスト入力の上に表示するラベル。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:23
#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:16
#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.style:1
msgid "The style of the text input."
msgstr "テキスト入力のスタイル。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:25
msgid ":class:`TextStyle`"
msgstr ":class:`TextStyle`"

#: ../../../discord/components.py:docstring of discord.components.TextInput:29
#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:18
#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.placeholder:1
msgid "The placeholder text to display when the text input is empty."
msgstr "テキスト入力が空の場合に表示されるプレースホルダーテキスト。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:35
#: ../../../discord/components.py:docstring of discord.TextInput.default:1
#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:20
#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.default:1
msgid "The default value of the text input."
msgstr "テキスト入力のデフォルト値。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:41
#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:22
#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.required:1
msgid "Whether the text input is required."
msgstr "テキスト入力が必須かどうか。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:47
#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:24
#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.min_length:1
msgid "The minimum length of the text input."
msgstr "テキスト入力の最小の長さ。"

#: ../../../discord/components.py:docstring of discord.components.TextInput:53
#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:26
#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.max_length:1
msgid "The maximum length of the text input."
msgstr "テキスト入力の最大の長さ。"

#: ../../../discord/components.py:docstring of discord.TextInput.default:3
msgid "This is an alias to :attr:`value`."
msgstr "これは :attr:`value` のエイリアスです。"

#: ../../interactions/api.rst:93
msgid "AppCommand"
msgstr "AppCommand"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:1
msgid "Represents an application command."
msgstr "アプリケーションコマンドを表します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:3
msgid "In common parlance this is referred to as a \"Slash Command\" or a \"Context Menu Command\"."
msgstr "一般的な用語では、これは「スラッシュコマンド」または「コンテキストメニューコマンド」と呼ばれます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:12
msgid "Checks if two application commands are equal."
msgstr "アプリケーションコマンドが等しいか確認します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:16
msgid "Checks if two application commands are not equal."
msgstr "アプリケーションコマンドが等しくないか確認します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:20
msgid "Returns the application command's hash."
msgstr "アプリケーションコマンドのハッシュを返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:24
msgid "Returns the application command's name."
msgstr "アプリケーションコマンドの名前を返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:28
msgid "The application command's ID."
msgstr "アプリケーションコマンドのID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:34
msgid "The application command's application's ID."
msgstr "アプリケーションコマンドのアプリケーションのID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:40
msgid "The application command's type."
msgstr "アプリケーションコマンドのタイプ。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:42
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandLimitReached:12
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandNotFound:24
msgid ":class:`~discord.AppCommandType`"
msgstr ":class:`~discord.AppCommandType`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:46
msgid "The application command's name."
msgstr "アプリケーションコマンドの名前。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:52
msgid "The application command's description."
msgstr "アプリケーションコマンドの説明。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:58
msgid "The localised names of the application command. Used for display purposes."
msgstr "アプリケーションコマンドのローカライズされた名前。表示用に使用されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:60
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:66
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:27
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:33
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:27
msgid "Dict[:class:`~discord.Locale`, :class:`str`]"
msgstr "Dict[:class:`~discord.Locale`, :class:`str`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:64
msgid "The localised descriptions of the application command. Used for display purposes."
msgstr "アプリケーションコマンドのローカライズされた説明。表示用に使用されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:70
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:37
msgid "A list of options."
msgstr "オプションのリスト。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:72
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:39
msgid "List[Union[:class:`Argument`, :class:`AppCommandGroup`]]"
msgstr "List[Union[:class:`Argument`, :class:`AppCommandGroup`]]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:76
msgid "The default member permissions that can run this command."
msgstr "このコマンドを実行できるデフォルトのメンバー権限。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:78
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:66
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:51
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:76
msgid "Optional[:class:`~discord.Permissions`]"
msgstr "Optional[:class:`~discord.Permissions`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:82
msgid "A boolean that indicates whether this command can be run in direct messages."
msgstr "このコマンドがダイレクトメッセージで実行できるかどうかを示す真偽値。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:88
msgid "The ID of the guild this command is registered in. A value of ``None`` denotes that it is a global command."
msgstr "このコマンドが登録されているギルドのID。 ``None`` の値はグローバルコマンドであることを示します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand:95
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:78
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:88
msgid "Whether the command is NSFW and should only work in NSFW channels."
msgstr "コマンドに年齢制限をかけて、年齢制限つきチャンネルのみで利用できるようにすべきか。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommand.mention:1
msgid "Returns a string that allows you to mention the given AppCommand."
msgstr "アプリケーションコマンドをメンションすることのできる文字列を返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommand.guild:1
msgid "Returns the guild this command is registered to if it exists."
msgstr "存在する場合、このコマンドが登録されたギルドを返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommand.guild:4
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandChannel.guild:3
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandThread.guild:3
msgid "Optional[:class:`~discord.Guild`]"
msgstr "Optional[:class:`~discord.Guild`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.delete:3
msgid "Deletes the application command."
msgstr "アプリケーションコマンドを削除します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.delete:5
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:17
msgid "The application command was not found."
msgstr "アプリケーションコマンドが見つからなかった場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.delete:6
msgid "You do not have permission to delete this application command."
msgstr "アプリケーションコマンドを削除する権限がない場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.delete:7
msgid "Deleting the application command failed."
msgstr "アプリケーションコマンドの削除に失敗した場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.delete:8
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:20
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.fetch_permissions:10
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:17
msgid "The client does not have an application ID."
msgstr "クライアントにアプリケーションIDがない場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:3
msgid "Edits the application command."
msgstr "アプリケーションコマンドを編集します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:5
msgid "The new name for the application command."
msgstr "アプリケーションコマンドの新しい名前。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:7
msgid "The new description for the application command."
msgstr "アプリケーションコマンドの新しい説明。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:9
msgid "The new default permissions needed to use this application command. Pass value of ``None`` to remove any permission requirements."
msgstr "このアプリケーションコマンドを使用するために必要な新しいデフォルトの権限。権限要件を削除するには ``None`` の値を渡します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:12
msgid "Indicates if the application command can be used in DMs."
msgstr "アプリケーションコマンドがDMで使用できるかどうかを示します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:14
msgid "List of new options for this application command."
msgstr "このアプリケーションコマンドの新しいオプションのリスト。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:18
msgid "You do not have permission to edit this application command."
msgstr "アプリケーションコマンドを編集する権限がない場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:19
msgid "Editing the application command failed."
msgstr "アプリケーションコマンドの編集に失敗した場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:22
msgid "The newly edited application command."
msgstr "新しく編集されたアプリケーションコマンド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.edit:23
msgid ":class:`AppCommand`"
msgstr ":class:`AppCommand`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.fetch_permissions:3
msgid "Retrieves this command's permission in the guild."
msgstr "コマンドのギルド内の権限を取得します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.fetch_permissions:5
msgid "The guild to retrieve the permissions from."
msgstr "権限を取得するギルド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.fetch_permissions:8
msgid "You do not have permission to fetch the application command's permissions."
msgstr "アプリケーションコマンドの権限を取得する権限がない場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.fetch_permissions:9
msgid "Fetching the application command's permissions failed."
msgstr "アプリケーションコマンドの権限を取得するのに失敗した場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.fetch_permissions:11
msgid "The application command's permissions could not be found.     This can also indicate that the permissions are synced with the guild     (i.e. they are unchanged from the default)."
msgstr "アプリケーションコマンドの権限が見つからなかった場合。これは、権限が同期されている(つまり、デフォルトのまま変わっていない)ことも示します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.fetch_permissions:13
msgid "An object representing the application command's permissions in the guild."
msgstr "ギルド内のアプリケーションコマンドの権限を表すオブジェクト。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommand.fetch_permissions:14
msgid ":class:`GuildAppCommandPermissions`"
msgstr ":class:`GuildAppCommandPermissions`"

#: ../../interactions/api.rst:101
msgid "AppCommandGroup"
msgstr "AppCommandGroup"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:1
msgid "Represents an application command subcommand."
msgstr "アプリケーションコマンドのサブコマンドを表します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:7
msgid "The type of subcommand."
msgstr "サブコマンドのタイプ。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:9
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:9
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:57
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.type:7
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:24
msgid ":class:`~discord.AppCommandOptionType`"
msgstr ":class:`~discord.AppCommandOptionType`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:13
msgid "The name of the subcommand."
msgstr "サブコマンドの名前。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:19
msgid "The description of the subcommand."
msgstr "サブコマンドの説明。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:25
msgid "The localised names of the subcommand. Used for display purposes."
msgstr "サブコマンドのローカライズされた名前。表示用に使用されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:31
msgid "The localised descriptions of the subcommand. Used for display purposes."
msgstr "サブコマンドのローカライズされた説明。表示用に使用されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:43
msgid "The parent application command."
msgstr "親アプリケーションコマンド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandGroup:45
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:51
msgid "Union[:class:`AppCommand`, :class:`AppCommandGroup`]"
msgstr "Union[:class:`AppCommand`, :class:`AppCommandGroup`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandGroup.qualified_name:1
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.qualified_name:1
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.ContextMenu.qualified_name:1
msgid "Returns the fully qualified command name."
msgstr "完全修飾されたコマンド名を返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandGroup.qualified_name:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.qualified_name:3
msgid "The qualified name includes the parent name as well. For example, in a command like ``/foo bar`` the qualified name is ``foo bar``."
msgstr "修飾名には親の名前も含まれています。例えば、 ``/foo bar`` のようなコマンドでは修飾名は ``foo bar`` です。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandGroup.mention:1
msgid "Returns a string that allows you to mention the given AppCommandGroup."
msgstr "アプリケーションコマンドグループをメンションすることのできる文字列を返します。"

#: ../../interactions/api.rst:109
msgid "AppCommandChannel"
msgstr "AppCommandChannel"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:1
msgid "Represents an application command partially resolved channel object."
msgstr "アプリケーションコマンドの部分的に解決されたチャンネルオブジェクトを表します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:9
msgid "Checks if two channels are equal."
msgstr "二つのチャンネルが等しいかを比較します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:13
msgid "Checks if two channels are not equal."
msgstr "二つのチャンネルが等しいものではないか比較します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:17
msgid "Returns the channel's hash."
msgstr "チャンネルのハッシュ値を返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:21
msgid "Returns the channel's name."
msgstr "チャンネルの名前を返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:25
msgid "The ID of the channel."
msgstr "チャンネルのID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:31
msgid "The type of channel."
msgstr "チャンネルの種類。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:33
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:33
msgid ":class:`~discord.ChannelType`"
msgstr ":class:`~discord.ChannelType`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:37
msgid "The name of the channel."
msgstr "チャンネルの名前。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:43
msgid "The resolved permissions of the user who invoked the application command in that channel."
msgstr "アプリケーションコマンドを呼び出したユーザーのチャンネル内の解決済み権限。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:46
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:52
msgid ":class:`~discord.Permissions`"
msgstr ":class:`~discord.Permissions`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel:50
msgid "The guild ID this channel belongs to."
msgstr "このチャンネルが存在するギルドのID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandChannel.guild:1
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandThread.guild:1
msgid "The channel's guild, from cache, if found."
msgstr "見つかった場合、キャッシュからのチャンネルのギルド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.resolve:1
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.resolve:1
msgid "Resolves the application command channel to the appropriate channel from cache if found."
msgstr "見つかった場合、アプリケーションコマンドチャンネルを適切なキャッシュからのチャンネルに解決します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.resolve:4
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.resolve:4
msgid "The resolved guild channel or ``None`` if not found in cache."
msgstr "解決したギルドチャンネル。キャッシュ内に見つからない場合は ``None`` が返されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.resolve:5
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.resolve:5
msgid "Optional[:class:`.abc.GuildChannel`]"
msgstr "Optional[:class:`.abc.GuildChannel`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.fetch:3
msgid "Fetches the partial channel to a full :class:`.abc.GuildChannel`."
msgstr "部分的なチャンネルから :class:`.abc.GuildChannel` を取得します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.fetch:5
msgid "The channel was not found."
msgstr "チャンネルが見つからなかった場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.fetch:6
msgid "You do not have the permissions required to get a channel."
msgstr "チャンネルを取得するために必要な権限がない場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.fetch:7
msgid "Retrieving the channel failed."
msgstr "チャンネルの取得に失敗した時。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.fetch:9
msgid "The full channel."
msgstr "完全なチャンネル。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandChannel.fetch:10
msgid ":class:`.abc.GuildChannel`"
msgstr ":class:`.abc.GuildChannel`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandChannel.mention:1
msgid "The string that allows you to mention the channel."
msgstr "チャンネルにメンションできる文字列。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandChannel.created_at:1
msgid "An aware timestamp of when this channel was created in UTC."
msgstr "チャンネルが作成されたときのUTC aware タイムスタンプ。"

#: ../../interactions/api.rst:117
msgid "AppCommandThread"
msgstr "AppCommandThread"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:1
msgid "Represents an application command partially resolved thread object."
msgstr "アプリケーションコマンドの部分的に解決されたスレッドオブジェクトを表します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:9
msgid "Checks if two thread are equal."
msgstr "二つのスレッドが等しいかを比較します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:13
msgid "Checks if two thread are not equal."
msgstr "二つのスレッドが等しいものではないか比較します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:17
msgid "Returns the thread's hash."
msgstr "スレッドのハッシュを返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:21
msgid "Returns the thread's name."
msgstr "スレッドの名前を返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:25
msgid "The ID of the thread."
msgstr "スレッドのID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:31
msgid "The type of thread."
msgstr "スレッドの種類。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:43
msgid "The parent text channel ID this thread belongs to."
msgstr "このスレッドが属する親テキストチャンネルのID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:49
msgid "The resolved permissions of the user who invoked the application command in that thread."
msgstr "アプリケーションコマンドを呼び出したユーザーのスレッド内の解決済み権限。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:56
msgid "The guild ID this thread belongs to."
msgstr "このスレッドが属するギルドのID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:62
msgid "Whether the thread is archived."
msgstr "スレッドがアーカイブされているかどうか。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:68
msgid "Whether the thread is locked."
msgstr "スレッドがロックされているかどうか。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:74
msgid "Whether non-moderators can add other non-moderators to this thread. This is always ``True`` for public threads."
msgstr "モデレータでないユーザーがこのスレッドに他のモデレータでないユーザーを追加できるかどうか。これは公開スレッドでは常に ``True`` です。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:81
msgid "The user's ID that archived this thread."
msgstr "このスレッドをアーカイブしたユーザーのID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:87
msgid "The duration in minutes until the thread is automatically hidden from the channel list. Usually a value of 60, 1440, 4320 and 10080."
msgstr ""

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread:94
msgid "An aware timestamp of when the thread's archived status was last updated in UTC."
msgstr "スレッドのアーカイブ状態が最後に更新されたときのawareなタイムスタンプ。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandThread.parent:1
msgid "The parent channel this thread belongs to."
msgstr "このスレッドが属する親チャンネル。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandThread.parent:3
msgid "Optional[:class:`~discord.TextChannel`]"
msgstr "Optional[:class:`~discord.TextChannel`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandThread.mention:1
msgid "The string that allows you to mention the thread."
msgstr "スレッドに言及できるようにするための文字列。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandThread.created_at:1
msgid "An aware timestamp of when the thread was created in UTC."
msgstr "UTCで表されたスレッドが作成されたときのタイムスタンプ。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AppCommandThread.created_at:5
msgid "This timestamp only exists for threads created after 9 January 2022, otherwise returns ``None``."
msgstr "このタイムスタンプは、2022 年1 月 9 日以降に作成されたスレッドにのみ存在します。それ以外の場合は ``None`` を返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.fetch:3
msgid "Fetches the partial channel to a full :class:`~discord.Thread`."
msgstr "部分的なチャンネルから :class:`~discord.Thread` を取得します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.fetch:5
msgid "The thread was not found."
msgstr "スレッドが見つからなかった場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.fetch:6
msgid "You do not have the permissions required to get a thread."
msgstr "スレッドの取得に必要な権限がない場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.fetch:7
msgid "Retrieving the thread failed."
msgstr "スレッドの取得に失敗した場合。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.fetch:9
msgid "The full thread."
msgstr "完全なスレッド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandThread.fetch:10
msgid ":class:`~discord.Thread`"
msgstr ":class:`~discord.Thread`"

#: ../../interactions/api.rst:125
msgid "AppCommandPermissions"
msgstr "AppCommandPermissions"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:1
msgid "Represents the permissions for an application command."
msgstr "アプリケーションコマンドの権限を表します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:7
msgid "The guild associated with this permission."
msgstr "この権限に紐づけられたギルド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:9
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.GuildAppCommandPermissions.guild:3
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AllChannels:9
msgid ":class:`~discord.Guild`"
msgstr ":class:`~discord.Guild`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:13
msgid "The ID of the permission target, such as a role, channel, or guild. The special ``guild_id - 1`` sentinel is used to represent \"all channels\"."
msgstr "ロール、チャンネル、ギルドなど、権限の対象のID。すべてのチャンネルを示す ``guild_id - 1`` センチネルもあります。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:20
msgid "The role, user, or channel associated with this permission. This could also be the :class:`AllChannels` sentinel type. Falls back to :class:`~discord.Object` if the target could not be found in the cache."
msgstr "この権限に関連付けられたロール、ユーザー、またはチャンネル。これは :class:`AllChannels` センチネル型である可能性もあります。これは対象がキャッシュ内に見つからない場合は :class:`~discord.Object` となります。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:23
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:82
#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.TranslationContext:16
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:18
msgid "Any"
msgstr "Any"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:27
msgid "The type of permission."
msgstr "権限の種類。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:29
msgid ":class:`.AppCommandPermissionType`"
msgstr ":class:`.AppCommandPermissionType`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AppCommandPermissions:33
msgid "The permission value. ``True`` for allow, ``False`` for deny."
msgstr "権限の値。 ``True`` は許可を、 ``False`` は拒否を示します。"

#: ../../interactions/api.rst:133
msgid "GuildAppCommandPermissions"
msgstr "GuildAppCommandPermissions"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.GuildAppCommandPermissions:1
msgid "Represents the permissions for an application command in a guild."
msgstr "ギルド内のアプリケーションコマンドの権限を示します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.GuildAppCommandPermissions:7
msgid "The application ID."
msgstr "アプリケーションID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.GuildAppCommandPermissions:13
msgid "The application command associated with the permissions."
msgstr "権限に紐づけられたアプリケーションコマンド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.GuildAppCommandPermissions:15
msgid ":class:`.AppCommand`"
msgstr ":class:`.AppCommand`"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.GuildAppCommandPermissions:19
msgid "ID of the command or the application ID. When this is the application ID instead of a command ID, the permissions apply to all commands that do not contain explicit overwrites."
msgstr "コマンドIDまたはアプリケーションID。これがコマンドIDではなくアプリケーションIDの場合、権限は明示的に上書きされていないすべてのコマンドに適用されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.GuildAppCommandPermissions:27
msgid "The guild ID associated with the permissions."
msgstr "この権限に関連付けられたギルドID。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.GuildAppCommandPermissions:33
msgid "The permissions, this is a max of 100."
msgstr "権限ら。最大で100個の制限があります。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.GuildAppCommandPermissions:35
msgid "List[:class:`AppCommandPermissions`]"
msgstr "List[:class:`AppCommandPermissions`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.GuildAppCommandPermissions.guild:1
msgid "The guild associated with the permissions."
msgstr "権限に紐づけられたギルド。"

#: ../../interactions/api.rst:141
msgid "Argument"
msgstr "Argument"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:1
msgid "Represents an application command argument."
msgstr "アプリケーションコマンドの引数を表します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:7
msgid "The type of argument."
msgstr "引数のタイプ。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:13
msgid "The name of the argument."
msgstr "引数の名前。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:19
msgid "The description of the argument."
msgstr "引数の説明。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:25
msgid "The localised names of the argument. Used for display purposes."
msgstr "引数のローカライズされた名前。表示用に使用されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:31
msgid "The localised descriptions of the argument. Used for display purposes."
msgstr "引数のローカライズされた説明。表示用に使用されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:37
msgid "Whether the argument is required."
msgstr "引数が入力必須かどうか。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:43
msgid "A list of choices for the command to choose from for this argument."
msgstr "この引数の選択肢のリスト。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:45
msgid "List[:class:`Choice`]"
msgstr "List[:class:`Choice`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:49
msgid "The parent application command that has this argument."
msgstr "この引数を持つ親アプリケーションコマンド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:55
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:61
msgid "The channel types that are allowed for this parameter."
msgstr "このパラメータにて利用できるチャンネルの種類。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:57
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.channel_types:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:63
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.channel_types:9
msgid "List[:class:`~discord.ChannelType`]"
msgstr "List[:class:`~discord.ChannelType`]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:61
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:67
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.min_value:1
msgid "The minimum supported value for this parameter."
msgstr "このパラメータがサポートする最小の値。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:63
#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:69
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:69
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:75
msgid "Optional[Union[:class:`int`, :class:`float`]]"
msgstr "Optional[Union[:class:`int`, :class:`float`]]"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:67
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:73
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.max_value:1
msgid "The maximum supported value for this parameter."
msgstr "このパラメータがサポートする最大の値。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:73
msgid "The minimum allowed length for this parameter."
msgstr "このパラメータが許容する最小の長さ。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:79
msgid "The maximum allowed length for this parameter."
msgstr "このパラメータが許容する最大の長さ。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Argument:85
msgid "Whether the argument has autocomplete."
msgstr "この引数がオートコンプリートを有するか。"

#: ../../interactions/api.rst:149
msgid "AllChannels"
msgstr "AllChannels"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AllChannels:1
msgid "Represents all channels for application command permissions."
msgstr "アプリケーションコマンド権限のすべてのチャンネルを表します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.AllChannels:7
msgid "The guild the application command permission is for."
msgstr "アプリケーションコマンド権限の対象ギルド。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.AllChannels.id:1
msgid "The ID sentinel used to represent all channels. Equivalent to the guild's ID minus 1."
msgstr "すべてのチャンネルを表すために使用されるIDセンチネル。ギルドのIDから1を引いたものに相当します。"

#: ../../interactions/api.rst:157
msgid "Data Classes"
msgstr "データクラス"

#: ../../interactions/api.rst:159
msgid "Similar to :ref:`discord_api_data`, these can be received and constructed by users."
msgstr ":ref:`discord_api_data` と同様に、これらは受け取ることもあれば、ユーザーによって構築することもできます。"

#: ../../interactions/api.rst:162
msgid "SelectOption"
msgstr "SelectOption"

#: ../../../discord/components.py:docstring of discord.components.SelectOption:1
msgid "Represents a select menu's option."
msgstr "選択メニューのオプションを表します。"

#: ../../../discord/components.py:docstring of discord.components.SelectOption:3
msgid "These can be created by users."
msgstr "これらはユーザーによって作成することができます。"

#: ../../../discord/components.py:docstring of discord.components.SelectOption:7
#: ../../../discord/components.py:docstring of discord.components.SelectOption:24
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.add_option:6
msgid "The label of the option. This is displayed to users. Can only be up to 100 characters."
msgstr "オプションのラベル。これはユーザーに表示されます。最大 100 文字まで可能です。"

#: ../../../discord/components.py:docstring of discord.components.SelectOption:10
#: ../../../discord/components.py:docstring of discord.components.SelectOption:31
msgid "The value of the option. This is not displayed to users. If not provided when constructed then it defaults to the label. Can only be up to 100 characters."
msgstr "オプションの値。これはユーザーには表示されません。 構築時に指定されていない場合は、ラベル値が既定で使用されます。最大 100 文字まで可能です。"

#: ../../../discord/components.py:docstring of discord.components.SelectOption:14
#: ../../../discord/components.py:docstring of discord.components.SelectOption:39
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.add_option:12
msgid "An additional description of the option, if any. Can only be up to 100 characters."
msgstr "存在する場合、オプションの追加の説明。最大 100 文字まで可能です。"

#: ../../../discord/components.py:docstring of discord.components.SelectOption:17
#: ../../../discord/components.py:docstring of discord.SelectOption.emoji:1
msgid "The emoji of the option, if available."
msgstr "利用可能な場合、オプションの絵文字。"

#: ../../../discord/components.py:docstring of discord.components.SelectOption:19
#: ../../../discord/components.py:docstring of discord.components.SelectOption:46
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.add_option:18
msgid "Whether this option is selected by default."
msgstr "このオプションがデフォルトで選択されているかどうか。"

#: ../../../discord/components.py:docstring of discord.SelectOption.emoji:3
#: ../../../discord/ui/button.py:docstring of discord.ui.Button.emoji:3
msgid "Optional[:class:`.PartialEmoji`]"
msgstr "Optional[:class:`.PartialEmoji`]"

#: ../../interactions/api.rst:170
msgid "Choice"
msgstr "Choice"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Choice:1
msgid "Represents an application command argument choice."
msgstr "アプリケーションコマンドの引数の選択肢を表します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Choice:9
msgid "Checks if two choices are equal."
msgstr "二つの選択肢が等しいかを比較します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Choice:13
msgid "Checks if two choices are not equal."
msgstr "二つの選択肢が等しいものではないか比較します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Choice:17
msgid "Returns the choice's hash."
msgstr "選択肢のハッシュ値を返します。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Choice:19
msgid "The name of the choice. Used for display purposes. Can only be up to 100 characters."
msgstr "選択肢の名前。表示用に使用されます。最大100文字まで可能です。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Choice:22
msgid "The localised names of the choice. Used for display purposes."
msgstr "選択肢のローカライズされた名前。表示用に使用されます。"

#: ../../../discord/app_commands/models.py:docstring of discord.app_commands.models.Choice:24
msgid "The value of the choice. If it's a string, it can only be up to 100 characters long."
msgstr "選択肢の値。文字列の場合は、最大100文字まで使用できます。"

#: ../../interactions/api.rst:179
msgid "Enumerations"
msgstr "列挙型"

#: ../../interactions/api.rst:183
msgid "Specifies the type of :class:`Interaction`."
msgstr ":class:`Interaction` のタイプを指定します。"

#: ../../interactions/api.rst:189
msgid "Represents Discord pinging to see if the interaction response server is alive."
msgstr "インタラクション応答サーバーが利用可能かを確認するためのDiscordのping。"

#: ../../interactions/api.rst:192
msgid "Represents a slash command interaction."
msgstr "スラッシュコマンドのインタラクション。"

#: ../../interactions/api.rst:195
msgid "Represents a component based interaction, i.e. using the Discord Bot UI Kit."
msgstr "コンポーネントベースのインタラクション、つまり Discord Bot UI Kitの使用。"

#: ../../interactions/api.rst:198
msgid "Represents an auto complete interaction."
msgstr "オートコンプリートのインタラクション。"

#: ../../interactions/api.rst:201
msgid "Represents submission of a modal interaction."
msgstr "モーダル送信のインタラクション。"

#: ../../interactions/api.rst:205
msgid "Specifies the response type for the interaction."
msgstr "インタラクションの応答タイプを示します。"

#: ../../interactions/api.rst:211
msgid "Pongs the interaction when given a ping."
msgstr "pingインタラクションにpongします。"

#: ../../interactions/api.rst:213
msgid "See also :meth:`InteractionResponse.pong`"
msgstr ":meth:`InteractionResponse.pong` も参照してください。"

#: ../../interactions/api.rst:216
msgid "Respond to the interaction with a message."
msgstr "インタラクションにメッセージで応答します。"

#: ../../interactions/api.rst:218
msgid "See also :meth:`InteractionResponse.send_message`"
msgstr ":meth:`InteractionResponse.send_message` も参照してください。"

#: ../../interactions/api.rst:221
msgid "Responds to the interaction with a message at a later time."
msgstr "後でインタラクションにメッセージで応答します。"

#: ../../interactions/api.rst:223
#: ../../interactions/api.rst:229
msgid "See also :meth:`InteractionResponse.defer`"
msgstr ":meth:`InteractionResponse.defer` も参照してください。"

#: ../../interactions/api.rst:226
msgid "Acknowledges the component interaction with a promise that the message will update later (though there is no need to actually update the message)."
msgstr "メッセージが後で更新されると約束してコンポーネントインタラクションを確認します。（実際には、メッセージを更新する必要はありません。）"

#: ../../interactions/api.rst:232
msgid "Responds to the interaction by editing the message."
msgstr "インタラクションにメッセージを編集して応答します。"

#: ../../interactions/api.rst:234
msgid "See also :meth:`InteractionResponse.edit_message`"
msgstr ":meth:`InteractionResponse.edit_message` も参照してください。"

#: ../../interactions/api.rst:237
msgid "Responds to the autocomplete interaction with suggested choices."
msgstr "オートコンプリートインタラクションに対し提案された選択肢で応答します。"

#: ../../interactions/api.rst:239
msgid "See also :meth:`InteractionResponse.autocomplete`"
msgstr ":meth:`InteractionResponse.autocomplete` も参照してください。"

#: ../../interactions/api.rst:242
msgid "Responds to the interaction with a modal."
msgstr "インタラクションにモーダルで応答します。"

#: ../../interactions/api.rst:244
msgid "See also :meth:`InteractionResponse.send_modal`"
msgstr ":meth:`InteractionResponse.send_modal` も参照してください。"

#: ../../interactions/api.rst:248
msgid "Represents the component type of a component."
msgstr "コンポーネントのコンポーネントタイプを表します。"

#: ../../interactions/api.rst:254
msgid "Represents the group component which holds different components in a row."
msgstr "行内に異なるコンポーネントを保持するグループコンポーネント。"

#: ../../interactions/api.rst:258
msgid "Represents a button component."
msgstr "ボタンコンポーネント。"

#: ../../interactions/api.rst:262
msgid "Represents a text box component."
msgstr "テキストボックスコンポーネント。"

#: ../../interactions/api.rst:266
msgid "Represents a select component."
msgstr "選択メニューコンポーネント。"

#: ../../interactions/api.rst:270
msgid "An alias to :attr:`select`. Represents a default select component."
msgstr ":attr:`select` のエイリアス。デフォルトの選択コンポーネントを表します。"

#: ../../interactions/api.rst:274
msgid "Represents a user select component."
msgstr "ユーザー選択メニューコンポーネント。"

#: ../../interactions/api.rst:278
msgid "Represents a role select component."
msgstr "ロール選択メニューコンポーネント。"

#: ../../interactions/api.rst:282
msgid "Represents a select in which both users and roles can be selected."
msgstr "ユーザとロールの両方を選択できる選択メニュー。"

#: ../../interactions/api.rst:286
msgid "Represents the style of the button component."
msgstr "ボタンコンポーネントのスタイルを表します。"

#: ../../interactions/api.rst:292
msgid "Represents a blurple button for the primary action."
msgstr "主なアクションのためのブループル色のボタンを表します。"

#: ../../interactions/api.rst:295
msgid "Represents a grey button for the secondary action."
msgstr "主でないアクションのための灰色のボタンを表します。"

#: ../../interactions/api.rst:298
msgid "Represents a green button for a successful action."
msgstr "成功したアクションを意味する緑色のボタンを表します。"

#: ../../interactions/api.rst:301
msgid "Represents a red button for a dangerous action."
msgstr "危険な操作を意味する赤いボタンを表します。"

#: ../../interactions/api.rst:304
msgid "Represents a link button."
msgstr "リンクボタンを表します。"

#: ../../interactions/api.rst:308
msgid "An alias for :attr:`primary`."
msgstr ":attr:`primary` のエイリアス。"

#: ../../interactions/api.rst:311
#: ../../interactions/api.rst:314
msgid "An alias for :attr:`secondary`."
msgstr ":attr:`secondary` のエイリアス。"

#: ../../interactions/api.rst:317
msgid "An alias for :attr:`success`."
msgstr ":attr:`success` のエイリアス。"

#: ../../interactions/api.rst:320
msgid "An alias for :attr:`danger`."
msgstr ":attr:`danger` のエイリアス。"

#: ../../interactions/api.rst:323
msgid "An alias for :attr:`link`."
msgstr ":attr:`link` のエイリアス。"

#: ../../interactions/api.rst:327
msgid "Represents the style of the text box component."
msgstr "テキストボックスコンポーネントのスタイルを表します。"

#: ../../interactions/api.rst:333
msgid "Represents a short text box."
msgstr "短いテキストボックスを表します。"

#: ../../interactions/api.rst:336
msgid "Represents a long form text box."
msgstr "長いフォームのテキストボックスを表します。"

#: ../../interactions/api.rst:339
msgid "An alias for :attr:`paragraph`."
msgstr ":attr:`paragraph` のエイリアス。"

#: ../../interactions/api.rst:343
msgid "The application command's option type. This is usually the type of parameter an application command takes."
msgstr "アプリケーションコマンドのオプションタイプ。これは通常アプリケーションコマンドがとるパラメータの型です。"

#: ../../interactions/api.rst:349
msgid "A subcommand."
msgstr "サブコマンド。"

#: ../../interactions/api.rst:352
msgid "A subcommand group."
msgstr "サブコマンドグループ。"

#: ../../interactions/api.rst:355
msgid "A string parameter."
msgstr "文字列パラメータ。"

#: ../../interactions/api.rst:358
msgid "A integer parameter."
msgstr "整数パラメータ。"

#: ../../interactions/api.rst:361
msgid "A boolean parameter."
msgstr "真偽値パラメータ。"

#: ../../interactions/api.rst:364
msgid "A user parameter."
msgstr "ユーザーパラメータ。"

#: ../../interactions/api.rst:367
msgid "A channel parameter."
msgstr "チャンネルパラメータ。"

#: ../../interactions/api.rst:370
msgid "A role parameter."
msgstr "ロールパラメータ。"

#: ../../interactions/api.rst:373
msgid "A mentionable parameter."
msgstr "メンション可能なもののパラメータ。"

#: ../../interactions/api.rst:376
msgid "A number parameter."
msgstr "数値のパラメータ。"

#: ../../interactions/api.rst:379
msgid "An attachment parameter."
msgstr "添付ファイルのパラメータ。"

#: ../../interactions/api.rst:383
msgid "The type of application command."
msgstr "アプリケーションコマンドの種類。"

#: ../../interactions/api.rst:389
msgid "A slash command."
msgstr "スラッシュコマンド。"

#: ../../interactions/api.rst:392
msgid "A user context menu command."
msgstr "ユーザーコンテキストメニューコマンド。"

#: ../../interactions/api.rst:395
msgid "A message context menu command."
msgstr "メッセージコンテキストメニューコマンド。"

#: ../../interactions/api.rst:399
msgid "The application command's permission type."
msgstr "アプリケーションコマンドの権限タイプ。"

#: ../../interactions/api.rst:405
msgid "The permission is for a role."
msgstr "権限はロール用です。"

#: ../../interactions/api.rst:408
msgid "The permission is for one or all channels."
msgstr "権限は一つ、またはすべてのチャンネル用です。"

#: ../../interactions/api.rst:411
msgid "The permission is for a user."
msgstr "権限はユーザー用です。"

#: ../../interactions/api.rst:416
msgid "Bot UI Kit"
msgstr "Bot UIキット"

#: ../../interactions/api.rst:418
msgid "The library has helpers to aid in creating component-based UIs. These are all in the ``discord.ui`` package."
msgstr "ライブラリにはコンポーネントベースの UI の作成を支援するヘルパーがあります。これらはすべて ``discord.ui`` パッケージにあります。"

#: ../../interactions/api.rst:422
msgid "View"
msgstr "View"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View:1
msgid "Represents a UI view."
msgstr "UIビューを表します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View:3
msgid "This object must be inherited to create a UI within Discord."
msgstr "Discord内でUIを作成するには、このオブジェクトを継承する必要があります。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View:7
#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal:23
msgid "Timeout in seconds from last interaction with the UI before no longer accepting input. If ``None`` then there is no timeout."
msgstr "UIの最後のインタラクションから起算した、入力を受け付けなくなるまでの秒単位のタイムアウト。 ``None`` の場合タイムアウトはありません。"

#: ../../../discord/ui/view.py:docstring of discord.ui.View.timeout:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.Modal.timeout:1
msgid "The timeout in seconds from last interaction with the UI before no longer accepting input. If ``None`` then there is no timeout."
msgstr "UIの最後のインタラクションから起算した、入力を受け付けなくなるまでの秒単位のタイムアウト。 ``None`` の場合タイムアウトはありません。"

#: ../../../discord/ui/view.py:docstring of discord.ui.View.timeout:4
#: ../../../discord/ui/modal.py:docstring of discord.ui.Modal.timeout:4
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.update_rate_limit:10
msgid "Optional[:class:`float`]"
msgstr "Optional[:class:`float`]"

#: ../../../discord/ui/view.py:docstring of discord.ui.View.children:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.Modal.children:1
msgid "The list of children attached to this view."
msgstr "このビューに添付された子のリスト。"

#: ../../../discord/ui/view.py:docstring of discord.ui.View.children:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.Modal.children:3
msgid "List[:class:`Item`]"
msgstr "List[:class:`Item`]"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.from_message:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.from_message:1
msgid "Converts a message's components into a :class:`View`."
msgstr "メッセージのコンポーネントを :class:`View` に変換します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.from_message:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.from_message:3
msgid "The :attr:`.Message.components` of a message are read-only and separate types from those in the ``discord.ui`` namespace. In order to modify and edit message components they must be converted into a :class:`View` first."
msgstr "メッセージの :attr:`.Message.components` は読み取り専用で ``discord.ui`` 名前空間のものと異なる型を使用しています。メッセージコンポーネントを編集するためには最初に :class:`View` に変換しないといけません。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.from_message:8
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.from_message:8
msgid "The message with components to convert into a view."
msgstr "ビューに変換するコンポーネントを含むメッセージ。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.from_message:10
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.from_message:10
msgid "The timeout of the converted view."
msgstr "変換されたビューのタイムアウト。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.from_message:13
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.from_message:13
msgid "The converted view. This always returns a :class:`View` and not one of its subclasses."
msgstr "変換されたビュー。サブクラスではなく常に :class:`View` を返します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.from_message:15
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.from_message:15
msgid ":class:`View`"
msgstr ":class:`View`"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.add_item:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.add_item:1
msgid "Adds an item to the view."
msgstr "ビューに項目を追加します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.add_item:3
#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.remove_item:3
#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.clear_items:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.add_item:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.clear_items:3
msgid "This function returns the class instance to allow for fluent-style chaining."
msgstr "この関数は、流暢なスタイルのチェーンを可能にするため、クラスインスタンスを返します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.add_item:6
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.add_item:6
msgid "The item to add to the view."
msgstr "ビューに追加する項目。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.add_item:9
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.add_item:9
msgid "An :class:`Item` was not passed."
msgstr ":class:`Item` が渡されなかった場合。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.add_item:10
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.add_item:10
msgid "Maximum number of children has been exceeded (25)     or the row the item is trying to be added to is full."
msgstr "子の最大数 (25) を超過したか、項目を追加しようとした行がいっぱいの場合。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.remove_item:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.remove_item:1
msgid "Removes an item from the view."
msgstr "ビューから項目を除去します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.remove_item:6
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.remove_item:6
msgid "The item to remove from the view."
msgstr "ビューから除去する項目。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.clear_items:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.clear_items:1
msgid "Removes all items from the view."
msgstr "ビューから項目をすべて除去します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.interaction_check:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.interaction_check:3
msgid "A callback that is called when an interaction happens within the view that checks whether the view should process item callbacks for the interaction."
msgstr "ビュー内でインタラクションが発生したときに呼び出される、インタラクションのコールバックを処理すべきかを確認するコールバック。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.interaction_check:6
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.interaction_check:6
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.interaction_check:6
msgid "This is useful to override if, for example, you want to ensure that the interaction author is a given user."
msgstr "これは、インタラクションが特定のユーザーからかを確かめたい場合などに上書きすると便利です。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.interaction_check:9
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.interaction_check:9
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.interaction_check:9
msgid "The default implementation of this returns ``True``."
msgstr "デフォルトの実装は ``True`` を返します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.interaction_check:13
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.interaction_check:13
msgid "If an exception occurs within the body then the check is considered a failure and :meth:`on_error` is called."
msgstr "この中で例外が発生した場合はチェックは失敗したとみなされ :meth:`on_error` が呼び出されます。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.interaction_check:16
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.interaction_check:16
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.interaction_check:18
msgid "The interaction that occurred."
msgstr "発生したインタラクション。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.interaction_check:19
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.interaction_check:19
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.interaction_check:21
msgid "Whether the view children's callbacks should be called."
msgstr "ビューの子のコールバックを呼び出すべきか。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.on_timeout:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.on_timeout:3
msgid "A callback that is called when a view's timeout elapses without being explicitly stopped."
msgstr "ビューが明示的に停止されずにそのタイムアウトが経過したときに呼び出されるコールバック。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.on_error:3
msgid "A callback that is called when an item's callback or :meth:`interaction_check` fails with an error."
msgstr "項目のコールバックや :meth:`interaction_check` がエラーで失敗したときに呼び出されるコールバック。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.on_error:6
#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal.on_error:6
msgid "The default implementation logs to the library logger."
msgstr "デフォルトの実装はライブラリロガーに記録します。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.on_error:8
#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal.on_error:8
msgid "The interaction that led to the failure."
msgstr "失敗したインタラクション。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.on_error:10
#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal.on_error:10
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.on_error:13
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.on_error:11
msgid "The exception that was raised."
msgstr "発生した例外。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.on_error:12
msgid "The item that failed the dispatch."
msgstr "実行に失敗した項目。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.stop:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.stop:1
msgid "Stops listening to interaction events from this view."
msgstr "このビューのインタラクションイベントを受け取るのを止めます。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.stop:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.stop:3
msgid "This operation cannot be undone."
msgstr "この操作を元に戻すことはできません。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.is_finished:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.is_finished:1
msgid ":class:`bool`: Whether the view has finished interacting."
msgstr ":class:`bool`: ビューのインタラクションが終了したかどうか。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.is_dispatching:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.is_dispatching:1
msgid ":class:`bool`: Whether the view has been added for dispatching purposes."
msgstr ":class:`bool`: ビューがイベント受け取り用に追加されたか。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.is_persistent:1
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.is_persistent:1
msgid ":class:`bool`: Whether the view is set up as persistent."
msgstr ":class:`bool`: ビューが永続的と設定されているかどうか。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.is_persistent:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.is_persistent:3
msgid "A persistent view has all their components with a set ``custom_id`` and a :attr:`timeout` set to ``None``."
msgstr "永続的なビューは、すべてのコンポーネントに ``custom_id`` が設定されており、 :attr:`timeout` が ``None`` に設定されています。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.wait:3
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.wait:3
msgid "Waits until the view has finished interacting."
msgstr "ビューのインタラクションが終了するまで待ちます。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.wait:5
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.wait:5
msgid "A view is considered finished when :meth:`stop` is called or it times out."
msgstr "ビューは :meth:`stop` が呼び出されるかタイムアウトしたときに終了したとされます。"

#: ../../../discord/ui/view.py:docstring of discord.ui.view.View.wait:8
#: ../../../discord/ui/modal.py:docstring of discord.ui.view.View.wait:8
msgid "If ``True``, then the view timed out. If ``False`` then the view finished normally."
msgstr "``True`` の場合、ビューがタイムアウトしました。 ``False`` の場合、ビューは正常に終了しました。"

#: ../../interactions/api.rst:430
msgid "Modal"
msgstr "Modal"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal:1
msgid "Represents a UI modal."
msgstr "UIモーダルを表します。"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal:3
msgid "This object must be inherited to create a modal popup window within discord."
msgstr "Discord内でモーダルポップアップウィンドウを作成するには、このオブジェクトを継承する必要があります。"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal:8
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:8
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.context_menu:8
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guild_only:11
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.check:11
msgid "Examples"
msgstr "例"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal:21
msgid "The title of the modal. Can only be up to 45 characters."
msgstr "モーダルのタイトル。最大45文字までです。"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal:26
msgid "The ID of the modal that gets received during an interaction. If not given then one is generated for you. Can only be up to 100 characters."
msgstr "インタラクション中に受け取るモーダルID。 指定されていない場合は、自動で生成されます。最大 100 文字までしか使用できません。"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal:33
msgid "The title of the modal."
msgstr "モーダルのタイトル。"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal:39
msgid "The ID of the modal that gets received during an interaction."
msgstr "インタラクション中に受け取るモーダルID。"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal.on_submit:3
msgid "Called when the modal is submitted."
msgstr "モーダルが送信されたときに呼び出されます。"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal.on_submit:5
msgid "The interaction that submitted this modal."
msgstr "このモーダルを送信したインタラクション。"

#: ../../../discord/ui/modal.py:docstring of discord.ui.modal.Modal.on_error:3
msgid "A callback that is called when :meth:`on_submit` fails with an error."
msgstr ":meth:`on_submit` がエラーで失敗したときに呼び出されるコールバック。"

#: ../../interactions/api.rst:439
msgid "Item"
msgstr "Item"

#: ../../../discord/ui/item.py:docstring of discord.ui.item.Item:1
msgid "Represents the base UI item that all UI components inherit from."
msgstr "すべての UI コンポーネントが継承する基本の UI 項目を表します。"

#: ../../../discord/ui/item.py:docstring of discord.ui.item.Item:3
msgid "The current UI items supported are:"
msgstr "現在サポートされているUI項目は次のとおりです。"

#: ../../../discord/ui/item.py:docstring of discord.ui.item.Item:5
msgid ":class:`discord.ui.Button`"
msgstr ":class:`discord.ui.Button`"

#: ../../../discord/ui/item.py:docstring of discord.ui.item.Item:6
#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:13
msgid ":class:`discord.ui.Select`"
msgstr ":class:`discord.ui.Select`"

#: ../../../discord/ui/item.py:docstring of discord.ui.item.Item:7
msgid ":class:`discord.ui.TextInput`"
msgstr ":class:`discord.ui.TextInput`"

#: ../../../discord/ui/item.py:docstring of discord.ui.Item.view:1
#: ../../../discord/ui/button.py:docstring of discord.ui.Button.view:1
#: ../../../discord/ui/select.py:docstring of discord.ui.Select.view:1
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.view:1
#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.view:1
msgid "The underlying view for this item."
msgstr "この項目が属するビュー。"

#: ../../../discord/ui/item.py:docstring of discord.ui.Item.view:3
#: ../../../discord/ui/button.py:docstring of discord.ui.Button.view:3
#: ../../../discord/ui/select.py:docstring of discord.ui.Select.view:3
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.view:3
#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.view:3
msgid "Optional[:class:`View`]"
msgstr "Optional[:class:`View`]"

#: ../../../discord/ui/item.py:docstring of discord.ui.item.Item.callback:3
#: ../../../discord/ui/button.py:docstring of discord.ui.item.Item.callback:3
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:3
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:3
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:3
msgid "The callback associated with this UI item."
msgstr "このUI 項目に関連付けられたコールバック。"

#: ../../../discord/ui/item.py:docstring of discord.ui.item.Item.callback:5
#: ../../../discord/ui/button.py:docstring of discord.ui.item.Item.callback:5
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:5
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:5
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:5
msgid "This can be overridden by subclasses."
msgstr "これはサブクラスによって上書きできます。"

#: ../../../discord/ui/item.py:docstring of discord.ui.item.Item.callback:7
#: ../../../discord/ui/button.py:docstring of discord.ui.item.Item.callback:7
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:7
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:7
#: ../../../discord/ui/select.py:docstring of discord.ui.item.Item.callback:7
msgid "The interaction that triggered this UI item."
msgstr "このUI項目を呼び出したインタラクション。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.Button:1
msgid "Represents a UI button."
msgstr "UIボタンを表します。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.Button:18
#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:27
msgid "The relative row this button belongs to. A Discord component can only have 5 rows. By default, items are arranged automatically into those 5 rows. If you'd like to control the relative positioning of the row then passing an index is advised. For example, row=1 will show up before row=2. Defaults to ``None``, which is automatic ordering. The row number must be between 0 and 4 (i.e. zero indexed)."
msgstr "このボタンが属する相対的な行。Discordコンポーネントは5行しか持てません。デフォルトでは、項目は自動的にこの5行に配置されます。 行の相対位置を制御したい場合は、インデックスを渡すことをお勧めします。 例えば、row=1 は row=2 の前に表示されます。デフォルトは ``None`` です。これは自動順序です。 行番号は 0 から 4 の間（つまり、0始まり）でなければなりません。"

#: ../../../discord/ui/button.py:docstring of discord.ui.Button.style:3
msgid ":class:`discord.ButtonStyle`"
msgstr ":class:`discord.ButtonStyle`"

#: ../../../discord/ui/button.py:docstring of discord.ui.Button.custom_id:1
msgid "The ID of the button that gets received during an interaction."
msgstr "インタラクション中に受け取るボタンID。"

#: ../../../discord/ui/button.py:docstring of discord.ui.Button.custom_id:3
msgid "If this button is for a URL, it does not have a custom ID."
msgstr "このボタンが URL 用の場合、カスタム ID はありません。"

#: ../../../discord/ui/button.py:docstring of discord.ui.Button.label:1
msgid "The label of the button, if available."
msgstr "利用可能な場合、ボタンのラベル。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:1
msgid "A decorator that attaches a button to a component."
msgstr "コンポーネントにボタンを付属させるデコレータ。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:3
msgid "The function being decorated should have three parameters, ``self`` representing the :class:`discord.ui.View`, the :class:`discord.Interaction` you receive and the :class:`discord.ui.Button` being pressed."
msgstr "デコレートされる関数には、 :class:`discord.ui.View` を表す ``self`` 、受け取った :class:`discord.Interaction` と押された :class:`discord.ui.Button` の3つのパラメータが必要です。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:9
msgid "Buttons with a URL cannot be created with this function. Consider creating a :class:`Button` manually instead. This is because buttons with a URL do not have a callback associated with them since Discord does not do any processing with it."
msgstr "この関数ではURL付きのボタンは作成できません。代わりに :class:`Button` を手動で作成することを検討してください。 これは、Discordが処理を行わないため、URLのボタンに関連付けられたコールバックがないためです。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:17
msgid "The ID of the button that gets received during an interaction. It is recommended not to set this parameter to prevent conflicts."
msgstr "インタラクション中に受け取るボタンID。衝突を防ぐためこのパラメータを設定しないことをおすすめします。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:20
msgid "The style of the button. Defaults to :attr:`.ButtonStyle.grey`."
msgstr "ボタンのスタイル。デフォルトは :attr:`.ButtonStyle.grey` です。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:22
msgid "Whether the button is disabled or not. Defaults to ``False``."
msgstr "ボタンを無効にするかどうか。デフォルトは ``False`` です。"

#: ../../../discord/ui/button.py:docstring of discord.ui.button.button:24
msgid "The emoji of the button. This can be in string form or a :class:`.PartialEmoji` or a full :class:`.Emoji`."
msgstr "ボタンの絵文字。文字列形式または :class:`.PartialEmoji` または完全な :class:`.Emoji` が渡せます。"

#: ../../interactions/api.rst:459
msgid "Select Menus"
msgstr "選択メニュー"

#: ../../interactions/api.rst:461
msgid "The library provides classes to help create the different types of select menus."
msgstr "ライブラリには、さまざまな種類の選択メニューを作成するのに役立つクラスが用意されています。"

#: ../../interactions/api.rst:464
msgid "Select"
msgstr "Select"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select:1
msgid "Represents a UI select menu with a list of custom options. This is represented to the user as a dropdown menu."
msgstr "カスタムのオプションから選択するUI選択メニューを表します。ユーザー側ではドロップダウンメニューとして表示されます。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select:6
#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:7
#: ../../../discord/ui/select.py:docstring of discord.ui.select.RoleSelect:7
#: ../../../discord/ui/select.py:docstring of discord.ui.select.MentionableSelect:10
#: ../../../discord/ui/select.py:docstring of discord.ui.select.UserSelect:9
msgid "The ID of the select menu that gets received during an interaction. If not given then one is generated for you."
msgstr "インタラクション中に受け取る選択メニューID。指定されていない場合は、自動で生成されます。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select:21
#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:22
#: ../../../discord/ui/select.py:docstring of discord.ui.select.RoleSelect:20
#: ../../../discord/ui/select.py:docstring of discord.ui.select.MentionableSelect:23
#: ../../../discord/ui/select.py:docstring of discord.ui.select.UserSelect:22
msgid "The relative row this select menu belongs to. A Discord component can only have 5 rows. By default, items are arranged automatically into those 5 rows. If you'd like to control the relative positioning of the row then passing an index is advised. For example, row=1 will show up before row=2. Defaults to ``None``, which is automatic ordering. The row number must be between 0 and 4 (i.e. zero indexed)."
msgstr "この選択メニューが属する相対的な行。Discordコンポーネントは5行しか持てません。デフォルトでは、項目は自動的にこの5行に配置されます。 行の相対位置を制御したい場合は、インデックスを渡すことをお勧めします。 例えば、row=1 は row=2 の前に表示されます。デフォルトは ``None`` です。これは自動順序です。 行番号は 0 から 4 の間（つまり、0始まり）でなければなりません。"

#: ../../../discord/ui/select.py:docstring of discord.ui.Select.values:1
msgid "A list of values that have been selected by the user."
msgstr "ユーザーが選択した値のリスト。"

#: ../../../discord/ui/select.py:docstring of discord.ui.Select.values:3
#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:13
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingPermissions:12
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.BotMissingPermissions:12
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandNotFound:18
msgid "List[:class:`str`]"
msgstr "List[:class:`str`]"

#: ../../../discord/ui/select.py:docstring of discord.ui.Select.type:1
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.type:1
#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.type:1
#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.type:1
#: ../../../discord/ui/select.py:docstring of discord.ui.UserSelect.type:1
msgid "The type of this component."
msgstr "このコンポーネントの種類。"

#: ../../../discord/ui/select.py:docstring of discord.ui.Select.type:3
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.type:3
#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.type:3
#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.type:3
#: ../../../discord/ui/select.py:docstring of discord.ui.UserSelect.type:3
msgid ":class:`.ComponentType`"
msgstr ":class:`.ComponentType`"

#: ../../../discord/ui/select.py:docstring of discord.ui.Select.options:3
msgid "List[:class:`discord.SelectOption`]"
msgstr "List[:class:`discord.SelectOption`]"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.add_option:1
msgid "Adds an option to the select menu."
msgstr "選択メニューにオプションを追加します。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.add_option:3
msgid "To append a pre-existing :class:`discord.SelectOption` use the :meth:`append_option` method instead."
msgstr "既存の :class:`discord.SelectOption` を追加するには、代わりに :meth:`append_option` メソッドを使用します。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.add_option:9
msgid "The value of the option. This is not displayed to users. If not given, defaults to the label. Can only be up to 100 characters."
msgstr "オプションの値。これはユーザーには表示されません。渡されない場合は、ラベル値が既定で使用されます。最大 100 文字まで可能です。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.add_option:15
msgid "The emoji of the option, if available. This can either be a string representing the custom or unicode emoji or an instance of :class:`.PartialEmoji` or :class:`.Emoji`."
msgstr "利用可能な場合、オプションの絵文字。これはカスタムまたはユニコード絵文字を表す文字列か :class:`.PartialEmoji` や :class:`.Emoji` のインスタンスのいずれかです。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.add_option:21
#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.append_option:6
msgid "The number of options exceeds 25."
msgstr "オプションの数が25を超えている場合。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.append_option:1
msgid "Appends an option to the select menu."
msgstr "選択メニューにオプションを追加します。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.Select.append_option:3
msgid "The option to append to the select menu."
msgstr "選択メニューに追加するオプション。"

#: ../../../discord/ui/select.py:docstring of discord.ui.Select.max_values:1
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.max_values:1
#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.max_values:1
#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.max_values:1
#: ../../../discord/ui/select.py:docstring of discord.ui.UserSelect.max_values:1
msgid "The maximum number of items that can be chosen for this select menu."
msgstr "選択メニューにて選択できる最大の項目数。"

#: ../../../discord/ui/select.py:docstring of discord.ui.Select.min_values:1
#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.min_values:1
#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.min_values:1
#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.min_values:1
#: ../../../discord/ui/select.py:docstring of discord.ui.UserSelect.min_values:1
msgid "The minimum number of items that must be chosen for this select menu."
msgstr "選択メニューにて選択しないといけない最小の項目数。"

#: ../../interactions/api.rst:473
msgid "ChannelSelect"
msgstr "ChannelSelect"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:1
msgid "Represents a UI select menu with a list of predefined options with the current channels in the guild."
msgstr "事前に指定されたギルド内のチャンネルから選択するUI選択メニューを表します。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:3
msgid "Please note that if you use this in a private message with a user, no channels will be displayed to the user."
msgstr "これをプライベートメッセージで使用すると、チャンネルの選択肢は表示されません。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.ChannelSelect:10
msgid "The types of channels to show in the select menu. Defaults to all channels."
msgstr "選択メニューに表示するチャンネルの種類。デフォルトはすべてのチャンネルです。"

#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.channel_types:1
msgid "A list of channel types that can be selected."
msgstr "選択可能なチャンネルの種類のリスト。"

#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.values:1
msgid "A list of channels selected by the user."
msgstr "ユーザーによって選択されたチャンネルのリスト。"

#: ../../../discord/ui/select.py:docstring of discord.ui.ChannelSelect.values:3
#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:21
msgid "List[Union[:class:`~discord.app_commands.AppCommandChannel`, :class:`~discord.app_commands.AppCommandThread`]]"
msgstr "List[Union[:class:`~discord.app_commands.AppCommandChannel`, :class:`~discord.app_commands.AppCommandThread`]]"

#: ../../interactions/api.rst:482
msgid "RoleSelect"
msgstr "RoleSelect"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.RoleSelect:1
msgid "Represents a UI select menu with a list of predefined options with the current roles of the guild."
msgstr "事前に指定されたギルド内のロールから選択するUI選択メニューを表します。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.RoleSelect:3
msgid "Please note that if you use this in a private message with a user, no roles will be displayed to the user."
msgstr "これをプライベートメッセージで使用すると、ロールの選択肢は表示されません。"

#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.values:1
msgid "A list of roles that have been selected by the user."
msgstr "ユーザーが選択したロールのリスト。"

#: ../../../discord/ui/select.py:docstring of discord.ui.RoleSelect.values:3
#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:17
msgid "List[:class:`discord.Role`]"
msgstr "List[:class:`discord.Role`]"

#: ../../interactions/api.rst:491
msgid "MentionableSelect"
msgstr "MentionableSelect"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.MentionableSelect:1
msgid "Represents a UI select menu with a list of predefined options with the current members and roles in the guild."
msgstr "事前に指定されたギルド内のメンバーとロールから選択するUI選択メニューを表します。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.MentionableSelect:3
msgid "If this is sent in a private message, it will only allow the user to select the client or themselves. Every selected option in a private message will resolve to a :class:`discord.User`. It will not give the user any roles to select."
msgstr "これがプライベートメッセージで送信された場合、ユーザーはこのクライアントまたは自分自身のみ選択できます。 プライベートメッセージ内で選択されたオプションは、すべて :class:`discord.User` に解決されます。ロールは選択できません。"

#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.values:1
msgid "A list of roles, members, and users that have been selected by the user."
msgstr "ユーザーが選択したロール、メンバー、またはユーザーのリスト。"

#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.values:4
#: ../../../discord/ui/select.py:docstring of discord.ui.select.UserSelect:3
#: ../../../discord/ui/select.py:docstring of discord.ui.UserSelect.values:4
msgid "If this is sent a private message, it will only allow the user to select the client or themselves. Every selected option in a private message will resolve to a :class:`discord.User`."
msgstr "これがプライベートメッセージで送信された場合、ユーザーはこのクライアントまたは自分自身のみ選択できます。 プライベートメッセージ内で選択されたオプションは、すべて :class:`discord.User` に解決されます。"

#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.values:8
#: ../../../discord/ui/select.py:docstring of discord.ui.UserSelect.values:8
msgid "If invoked in a guild, the values will always resolve to :class:`discord.Member`."
msgstr "ギルドで呼び出された場合、値は常に :class:`discord.Member` に解決されます。"

#: ../../../discord/ui/select.py:docstring of discord.ui.MentionableSelect.values:10
#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:19
msgid "List[Union[:class:`discord.Role`, :class:`discord.Member`, :class:`discord.User`]]"
msgstr "List[Union[:class:`discord.Role`, :class:`discord.Member`, :class:`discord.User`]]"

#: ../../interactions/api.rst:500
msgid "UserSelect"
msgstr "UserSelect"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.UserSelect:1
msgid "Represents a UI select menu with a list of predefined options with the current members of the guild."
msgstr "事前に指定されたギルド内のメンバーから選択するUI選択メニューを表します。"

#: ../../../discord/ui/select.py:docstring of discord.ui.UserSelect.values:1
msgid "A list of members and users that have been selected by the user."
msgstr "ユーザーが選択したメンバーまたはユーザーのリスト。"

#: ../../../discord/ui/select.py:docstring of discord.ui.UserSelect.values:10
#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:15
msgid "List[Union[:class:`discord.Member`, :class:`discord.User`]]"
msgstr "List[Union[:class:`discord.Member`, :class:`discord.User`]]"

#: ../../interactions/api.rst:509
msgid "select"
msgstr "select"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:1
msgid "A decorator that attaches a select menu to a component."
msgstr "コンポーネントに選択メニューを付属させるデコレータ。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:3
msgid "The function being decorated should have three parameters, ``self`` representing the :class:`discord.ui.View`, the :class:`discord.Interaction` you receive and the chosen select class."
msgstr "デコレートされる関数には、 :class:`discord.ui.View` を表す ``self`` 、受け取った :class:`discord.Interaction` と選択クラスの3つのパラメータが必要です。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:7
msgid "To obtain the selected values inside the callback, you can use the ``values`` attribute of the chosen class in the callback. The list of values will depend on the type of select menu used. View the table below for more information."
msgstr "コールバック内で選択した値を取得するには、コールバック内で選択したクラスの ``values`` 属性を使用します。 値のリストは、使用される選択メニューの種類によって異なります。詳細については、以下の表を参照してください。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:11
msgid "Select Type"
msgstr "選択種類"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:11
msgid "Resolved Values"
msgstr "解決された値"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:15
msgid ":class:`discord.ui.UserSelect`"
msgstr ":class:`discord.ui.UserSelect`"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:17
msgid ":class:`discord.ui.RoleSelect`"
msgstr ":class:`discord.ui.RoleSelect`"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:19
msgid ":class:`discord.ui.MentionableSelect`"
msgstr ":class:`discord.ui.MentionableSelect`"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:21
msgid ":class:`discord.ui.ChannelSelect`"
msgstr ":class:`discord.ui.ChannelSelect`"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:24
msgid "Added the following keyword-arguments: ``cls``, ``channel_types``"
msgstr "次のキーワード引数を追加しました: ``cls``, ``channel_types``"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:28
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.default_permissions:24
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_any_role:23
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_permissions:25
msgid "Example"
msgstr "例"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:37
msgid "The class to use for the select menu. Defaults to :class:`discord.ui.Select`. You can use other select types to display different select menus to the user. See the table above for the different values you can get from each select type. Subclasses work as well, however the callback in the subclass will get overridden."
msgstr "選択メニューに使用するクラス。デフォルトは :class:`discord.ui.Select` です。 他の選択メニューを使用して、ユーザーに対し異なる選択メニューを表示できます。 それぞれの選択メニューから得られる値については、上の表を参照してください。 サブクラスも同様に機能しますが、サブクラスのコールバックはオーバーライドされます。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:44
msgid "The ID of the select menu that gets received during an interaction. It is recommended not to set this parameter to prevent conflicts."
msgstr "インタラクション中に受け取る選択メニューID。衝突を防ぐためこのパラメータを設定しないことをおすすめします。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:59
msgid "A list of options that can be selected in this menu. This can only be used with :class:`Select` instances."
msgstr "このメニューで選択できるオプションのリスト。 :class:`Select` インスタンスでのみ使用できます。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:62
msgid "The types of channels to show in the select menu. Defaults to all channels. This can only be used with :class:`ChannelSelect` instances."
msgstr "選択メニューに表示するチャンネルの種類。デフォルトはすべてのチャンネルです。これは :class:`ChannelSelect` インスタンスでのみ使用できます。"

#: ../../../discord/ui/select.py:docstring of discord.ui.select.select:65
msgid "Whether the select is disabled or not. Defaults to ``False``."
msgstr "選択メニューを無効にするかどうか。デフォルトは ``False`` です。"

#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:1
msgid "Represents a UI text input."
msgstr "UI のテキスト入力を表します。"

#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:7
msgid "Returns the value of the text input or an empty string if the value is ``None``."
msgstr "テキスト入力の値、または ``None`` の場合空文字列を返します。"

#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:13
msgid "The ID of the text input that gets received during an interaction. If not given then one is generated for you."
msgstr "インタラクション中に受け取るテキスト入力ID。指定されていない場合は、自動で生成されます。"

#: ../../../discord/ui/text_input.py:docstring of discord.ui.text_input.TextInput:28
msgid "The relative row this text input belongs to. A Discord component can only have 5 rows. By default, items are arranged automatically into those 5 rows. If you'd like to control the relative positioning of the row then passing an index is advised. For example, row=1 will show up before row=2. Defaults to ``None``, which is automatic ordering. The row number must be between 0 and 4 (i.e. zero indexed)."
msgstr "このテキスト入力が属する相対的な行。Discordコンポーネントは5行しか持てません。デフォルトでは、項目は自動的にこの5行に配置されます。 行の相対位置を制御したい場合は、インデックスを渡すことをお勧めします。 例えば、row=1 は row=2 の前に表示されます。デフォルトは ``None`` です。これは自動順序です。 行番号は 0 から 4 の間（つまり、0始まり）でなければなりません。"

#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.value:1
msgid "The value of the text input."
msgstr "テキスト入力の値。"

#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.label:1
msgid "The label of the text input."
msgstr "テキスト入力のラベル。"

#: ../../../discord/ui/text_input.py:docstring of discord.ui.TextInput.style:3
msgid ":class:`discord.TextStyle`"
msgstr ":class:`discord.TextStyle`"

#: ../../interactions/api.rst:526
msgid "Application Commands"
msgstr "アプリケーションコマンド"

#: ../../interactions/api.rst:528
msgid "The library has helpers to aid in creation of application commands. These are all in the ``discord.app_commands`` package."
msgstr "ライブラリにはアプリケーションコマンドの作成を支援するヘルパーがあります。これらはすべて ``discord.app_commands`` パッケージにあります。"

#: ../../interactions/api.rst:531
msgid "CommandTree"
msgstr "CommandTree"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree:1
msgid "Represents a container that holds application command information."
msgstr "アプリケーションコマンド情報を持つコンテナ。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree:3
msgid "The client instance to get application command information from."
msgstr "アプリケーションコマンドの情報を取得するためのクライアントインスタンス。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree:5
msgid "If a guild-specific command is not found when invoked, then try falling back into a global command in the tree. For example, if the tree locally has a ``/ping`` command under the global namespace but the guild has a guild-specific ``/ping``, instead of failing to find the guild-specific ``/ping`` command it will fall back to the global ``/ping`` command. This has the potential to raise more :exc:`~discord.app_commands.CommandSignatureMismatch` errors than usual. Defaults to ``True``."
msgstr "ギルド固有のコマンドが呼び出されたときに見つからない場合は、ツリー内のグローバルコマンドを呼び出します。 例えば、ローカル環境でグローバル名前空間の下に ``/ping`` コマンドがあるが、ギルド本体ではギルド固有の ``/ping`` がある場合、ギルド固有の ``/ping`` コマンドを見つけるのに失敗せずに、グローバルの ``/ping`` コマンドをフォールバックとして使用します。 これは :exc:`~discord.app_commands.CommandSignatureMismatch` エラーを通常より多く引き起こす可能性があります。デフォルトは ``True`` です。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:1
msgid "A decorator that creates an application command from a regular function directly under this tree."
msgstr "通常の関数からアプリケーションコマンドをこのツリーの下に直接作成するデコレータ。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.command:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.command:3
msgid "The name of the application command. If not given, it defaults to a lower-case version of the callback name."
msgstr "アプリケーションコマンドの名前。指定しない場合は、コールバック名を小文字化したものになります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:6
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.command:6
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.command:6
msgid "The description of the application command. This shows up in the UI to describe the application command. If not given, it defaults to the first line of the docstring of the callback shortened to 100 characters."
msgstr "アプリケーションコマンドの説明。これは、アプリケーションコマンドを説明するためにUIに表示されます。 指定されていない場合は、コールバックの docstring の最初の行を 100 文字以内に短縮したものが使用されます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:10
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:23
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:25
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:43
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.command:10
msgid "Whether the command is NSFW and should only work in NSFW channels. Defaults to ``False``.  Due to a Discord limitation, this does not work on subcommands."
msgstr "コマンドに年齢制限をかけて、年齢制限つきチャンネルでのみ利用できるようにすべきか。デフォルトでは ``False`` です。Discordの制限により、サブコマンドでは利用できません。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:10
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:23
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:25
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:24
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:62
msgid "Whether the command is NSFW and should only work in NSFW channels. Defaults to ``False``."
msgstr "コマンドに年齢制限をかけて、年齢制限つきチャンネルでのみ利用できるようにすべきか。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:12
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:25
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:28
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:64
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:72
msgid "Due to a Discord limitation, this does not work on subcommands."
msgstr "Discord側の制限のため、サブコマンドでは動作しません。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:14
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:27
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:10
msgid "The guild to add the command to. If not given or ``None`` then it becomes a global command instead."
msgstr "コマンドを追加するギルド。指定されていない場合や ``None`` の場合これはグローバルコマンドになります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:17
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:30
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:13
msgid "The list of guilds to add the command to. This cannot be mixed with the ``guild`` parameter. If no guilds are given at all then it becomes a global command instead."
msgstr "コマンドを追加するギルドのリスト。 ``guild`` パラメータと併用できません。指定されていない場合これは代わりにグローバルコマンドになります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:21
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:34
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:19
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:18
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:25
msgid "If this is set to ``True``, then all translatable strings will implicitly be wrapped into :class:`locale_str` rather than :class:`str`. This could avoid some repetition and be more ergonomic for certain defaults such as default command names, command descriptions, and parameter names. Defaults to ``True``."
msgstr "これが ``True`` に設定されている場合、すべての翻訳可能な文字列が :class:`str` でなく:class:`locale_str` にラップされます。これを用いると、デフォルトのコマンド名、コマンド説明、パラメータ名などを繰り返し記述するのを避け、より使いやすくなるかもしれません。デフォルトは ``True`` です。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.command:27
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:40
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:32
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:92
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:27
msgid "A dictionary that can be used to store extraneous data. The library will not touch any values or keys within this dictionary."
msgstr "追加のデータを保管できる辞書型。ライブラリは辞書型の中のキーや値を一切操作しません。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:1
msgid "A decorator that creates an application command context menu from a regular function directly under this tree."
msgstr "通常の関数からアプリケーションコマンドコンテキストメニューをこのツリーの下に直接作成するデコレータ。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.context_menu:3
msgid "This function must have a signature of :class:`~discord.Interaction` as its first parameter and taking either a :class:`~discord.Member`, :class:`~discord.User`, or :class:`~discord.Message`, or a :obj:`typing.Union` of ``Member`` and ``User`` as its second parameter."
msgstr "関数は第一パラメータとして :class:`~discord.Interaction` を取り、第二パラメータとして :class:`~discord.Member` 、 :class:`~discord.User` 、 :class:`~discord.Message` 、または ``Member`` と ``User`` の :obj:`typing.Union` を取らないといけません。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.context_menu:19
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.context_menu:19
msgid "The name of the context menu command. If not given, it defaults to a title-case version of the callback name. Note that unlike regular slash commands this can have spaces and upper case characters in the name."
msgstr "コンテキストメニューコマンドの名前。指定しない場合はデフォルトでコールバック名をタイトルケース化したものになります。 通常のスラッシュコマンドとは異なり、名前にスペースと大文字を含めることができます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.error:1
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.error:1
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.error:1
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.error:1
msgid "A decorator that registers a coroutine as a local error handler."
msgstr "コルーチンをローカルエラーハンドラとして登録するデコレータ。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.error:3
msgid "This must match the signature of the :meth:`on_error` callback."
msgstr ":meth:`on_error` コールバックのシグネチャと一致する必要があります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.error:5
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.error:7
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.error:7
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.error:6
msgid "The error passed will be derived from :exc:`AppCommandError`."
msgstr "渡されたエラーは :exc:`AppCommandError` を継承しています。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.error:7
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.error:9
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.error:9
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.error:8
msgid "The coroutine to register as the local error handler."
msgstr "ローカルエラーハンドラとして登録するコルーチン。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.error:10
msgid "The coroutine passed is not actually a coroutine or does     not match the signature."
msgstr "渡されたコルーチンが実際にはコルーチンでないか、シグネチャが一致しない場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_command:3
msgid "Fetches an application command from the application."
msgstr "アプリケーションからアプリケーションコマンドを取得します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_command:5
msgid "The ID of the command to fetch."
msgstr "取得するコマンドのID。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_command:7
msgid "The guild to fetch the command from. If not passed then the global command is fetched instead."
msgstr "コマンドを取得するギルド。渡されない場合は、代わりにグローバルコマンドが取得されます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_command:11
msgid "Fetching the command failed."
msgstr "コマンドの取得に失敗した場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_command:12
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_commands:17
msgid "The application ID could not be found."
msgstr "アプリケーションコマンドが見つからなかった場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_command:13
msgid "The application command was not found.     This could also be because the command is a guild command     and the guild was not specified and vice versa."
msgstr "アプリケーションコマンドが見つからなかった場合。 これは、コマンドがギルドコマンドであるのにギルドが指定されていない場合またはその逆を含みます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_command:15
msgid "The application command."
msgstr "アプリケーションコマンド。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_command:16
msgid ":class:`~discord.app_commands.AppCommand`"
msgstr ":class:`~discord.app_commands.AppCommand`"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_commands:3
msgid "Fetches the application's current commands."
msgstr "アプリケーションの現在のコマンドを取得します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_commands:5
msgid "If no guild is passed then global commands are fetched, otherwise the guild's commands are fetched instead."
msgstr "ギルドが渡されない場合、グローバルコマンドが取得されます。そうでなければ、代わりにギルドのコマンドが取得されます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_commands:10
msgid "This includes context menu commands."
msgstr "これにはコンテキストメニューコマンドが含まれます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_commands:12
msgid "The guild to fetch the commands from. If not passed then global commands are fetched instead."
msgstr "コマンドを取得するギルド。渡されない場合は、代わりにグローバルコマンドが取得されます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_commands:16
msgid "Fetching the commands failed."
msgstr "コマンドの取得に失敗した場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_commands:19
msgid "The application's commands."
msgstr "アプリケーションコマンド。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.fetch_commands:20
msgid "List[:class:`~discord.app_commands.AppCommand`]"
msgstr "List[:class:`~discord.app_commands.AppCommand`]"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.copy_global_to:1
msgid "Copies all global commands to the specified guild."
msgstr "指定したギルドにグローバルコマンドをすべてコピーします。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.copy_global_to:3
msgid "This method is mainly available for development purposes, as it allows you to copy your global commands over to a testing guild easily."
msgstr "これは、グローバルコマンドをテスト用のギルドにコピーできるので、主に開発のために提供されています。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.copy_global_to:6
msgid "Note that this method will *override* pre-existing guild commands that would conflict."
msgstr "このメソッドは競合する既存のギルドコマンドを *上書き* することに注意してください。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.copy_global_to:8
msgid "The guild to copy the commands to."
msgstr "コマンドをコピーするギルド。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.copy_global_to:11
msgid "The maximum number of commands was reached for that guild.     This is currently 100 for slash commands and 5 for context menu commands."
msgstr "ギルドの最大コマンド数に達した場合。これはスラッシュコマンドでは100個で、コンテキストメニューコマンドでは5個です。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:1
msgid "Adds an application command to the tree."
msgstr "ツリーにアプリケーションコマンドを追加します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:3
msgid "This only adds the command locally -- in order to sync the commands and enable them in the client, :meth:`sync` must be called."
msgstr "これはコマンドをローカルに追加するだけです -- コマンドを同期してクライアントで有効にするには、 :meth:`sync` を呼び出す必要があります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:6
msgid "The root parent of the command is added regardless of the type passed."
msgstr "渡された種類に関係なく、コマンドの親も追加されます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:8
msgid "The application command or group to add."
msgstr "追加するアプリケーションコマンドまたはグループ。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:17
msgid "Whether to override a command with the same name. If ``False`` an exception is raised. Default is ``False``."
msgstr "同じ名前のコマンドを上書きするかどうか。 ``False`` の場合例外が発生します。デフォルトは ``False`` です。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:21
msgid "The command was already registered and no override was specified."
msgstr "コマンドが既に登録されていて、上書きすると指定されていない場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:22
msgid "The application command passed is not a valid application command.     Or, ``guild`` and ``guilds`` were both given."
msgstr "渡されたアプリケーションコマンドが有効でない場合、または ``guild`` と ``guilds`` の両方が与えられた場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.add_command:23
msgid "The maximum number of commands was reached globally or for that guild.     This is currently 100 for slash commands and 5 for context menu commands."
msgstr "グローバルでの、またはギルドの最大コマンド数に達した場合。これはスラッシュコマンドでは100個で、コンテキストメニューコマンドでは5個です。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.remove_command:1
msgid "Removes an application command from the tree."
msgstr "ツリーからアプリケーションコマンドを除去します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.remove_command:3
msgid "This only removes the command locally -- in order to sync the commands and remove them in the client, :meth:`sync` must be called."
msgstr "これはコマンドをローカルで除去するだけです -- コマンドを同期してクライアントで除去するには、 :meth:`sync` を呼び出す必要があります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.remove_command:6
msgid "The name of the root command to remove."
msgstr "除去するルートコマンドの名前。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.remove_command:8
msgid "The guild to remove the command from. If not given or ``None`` then it removes a global command instead."
msgstr "コマンドを除去するギルド。指定されていない場合や ``None`` の場合グローバルコマンドを除去します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.remove_command:11
msgid "The type of command to remove. Defaults to :attr:`~discord.AppCommandType.chat_input`, i.e. slash commands."
msgstr "除去するコマンドの種類。デフォルトは :attr:`~discord.AppCommandType.chat_input` 、すなわちスラッシュコマンドです。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.remove_command:15
msgid "The application command that got removed. If nothing was removed then ``None`` is returned instead."
msgstr "除去されたアプリケーションコマンド。何も除去されなかった場合は ``None`` が返ります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.remove_command:17
#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_command:14
msgid "Optional[Union[:class:`Command`, :class:`ContextMenu`, :class:`Group`]]"
msgstr "Optional[Union[:class:`Command`, :class:`ContextMenu`, :class:`Group`]]"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.clear_commands:1
msgid "Clears all application commands from the tree."
msgstr "ツリーからアプリケーションコマンドをすべて除去します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.clear_commands:3
msgid "This only removes the commands locally -- in order to sync the commands and remove them in the client, :meth:`sync` must be called."
msgstr "これはコマンドをローカルで除去するだけです -- コマンドを同期してクライアントで除去するには、 :meth:`sync` を呼び出す必要があります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.clear_commands:6
msgid "The guild to remove the commands from. If ``None`` then it removes all global commands instead."
msgstr "コマンドを除去するギルド。指定されていない場合や ``None`` の場合グローバルコマンドを除去します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.clear_commands:9
msgid "The type of command to clear. If not given or ``None`` then it removes all commands regardless of the type."
msgstr "除去するコマンドの種類。指定されていない場合や ``None`` の場合種類にかかわらず除去します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_command:1
msgid "Gets an application command from the tree."
msgstr "ツリーからアプリケーションコマンドを取得します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_command:3
msgid "The name of the root command to get."
msgstr "取得するルートコマンドの名前。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_command:5
msgid "The guild to get the command from. If not given or ``None`` then it gets a global command instead."
msgstr "コマンドを取得するギルド。指定されていない場合や ``None`` の場合グローバルコマンドを取得します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_command:8
msgid "The type of command to get. Defaults to :attr:`~discord.AppCommandType.chat_input`, i.e. slash commands."
msgstr "取得するコマンドの種類。デフォルトは :attr:`~discord.AppCommandType.chat_input` 、すなわちスラッシュコマンドです。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_command:12
msgid "The application command that was found. If nothing was found then ``None`` is returned instead."
msgstr "見つかったアプリケーションコマンド。存在しない場合は ``None`` が返ります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_commands:1
msgid "Gets all application commands from the tree."
msgstr "ツリーからアプリケーションコマンドをすべて取得します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_commands:3
msgid "The guild to get the commands from, not including global commands. If not given or ``None`` then only global commands are returned."
msgstr "グローバルコマンドを含まないコマンドを取得するギルド。指定されていない場合や ``None`` の場合はグローバルコマンドが返ります。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_commands:6
msgid "The type of commands to get. When not given or ``None``, then all command types are returned."
msgstr "取得するコマンドの種類。指定されていない場合や ``None`` の場合はすべてのコマンドが返されます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_commands:10
msgid "The application commands from the tree."
msgstr "ツリーのアプリケーションコマンド。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.get_commands:11
msgid "List[Union[:class:`ContextMenu`, :class:`Command`, :class:`Group`]]"
msgstr "List[Union[:class:`ContextMenu`, :class:`Command`, :class:`Group`]]"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.walk_commands:1
msgid "An iterator that recursively walks through all application commands and child commands from the tree."
msgstr "ツリーのすべてのアプリケーションコマンドと子コマンドを再帰的に網羅するイテレータ。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.walk_commands:3
msgid "The guild to iterate the commands from, not including global commands. If not given or ``None`` then only global commands are iterated."
msgstr "グローバルコマンドを含まないコマンドをイテレートするギルド。指定されていない場合や ``None`` の場合はグローバルコマンドがイテレートされます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.walk_commands:6
msgid "The type of commands to iterate over. Defaults to :attr:`~discord.AppCommandType.chat_input`, i.e. slash commands."
msgstr "イテレートするコマンドの種類。デフォルトは :attr:`~discord.AppCommandType.chat_input` 、すなわちスラッシュコマンドです。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.walk_commands:0
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.walk_commands:0
msgid "Yields"
msgstr "列挙"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.walk_commands:10
msgid "Union[:class:`ContextMenu`, :class:`Command`, :class:`Group`] -- The application commands from the tree."
msgstr "Union[:class:`ContextMenu`, :class:`Command`, :class:`Group`] -- ツリーのアプリケーションコマンド。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.on_error:3
msgid "A callback that is called when any command raises an :exc:`AppCommandError`."
msgstr "どれかのコマンドが :exc:`AppCommandError` を送出したときに呼び出されるコールバック。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.on_error:5
msgid "The default implementation logs the exception using the library logger if the command does not have any error handlers attached to it."
msgstr "デフォルトの実装ではコマンドにエラーハンドラーが付属したいない場合のみ例外をライブラリロガーを用いて記録します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.on_error:8
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.on_error:5
msgid "To get the command that failed, :attr:`discord.Interaction.command` should be used."
msgstr "失敗したコマンドを取得するには、 :attr:`discord.Interaction.command` を使用してください。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.on_error:11
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.on_error:9
msgid "The interaction that is being handled."
msgstr "処理中のインタラクション。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.CommandTree.translator:1
msgid "The translator, if any, responsible for handling translation of commands."
msgstr "存在する場合、コマンドの翻訳を担当するトランスレータ。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.CommandTree.translator:3
msgid "To change the translator, use :meth:`set_translator`."
msgstr "トランスレータを変更するには、 :meth:`set_translator` を使用してください。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.CommandTree.translator:5
msgid "Optional[:class:`Translator`]"
msgstr "Optional[:class:`Translator`]"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.set_translator:3
msgid "Sets the translator to use for translating commands."
msgstr "コマンドの翻訳に使用するトランスレータを設定します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.set_translator:5
msgid "If a translator was previously set, it will be unloaded using its :meth:`Translator.unload` method."
msgstr "以前にトランスレータが設定されていた場合は、 :meth:`Translator.unload` メソッドを使用してアンロードされます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.set_translator:8
msgid "When a translator is set, it will be loaded using its :meth:`Translator.load` method."
msgstr "トランスレータが設定されると、 :meth:`Translator.load` メソッドを使用して読み込まれます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.set_translator:10
msgid "The translator to use. If ``None`` then the translator is just removed and unloaded."
msgstr "使用するトランスレータ。 ``None`` の場合、トランスレータは単に除去され、アンロードされます。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.set_translator:13
msgid "The translator was not ``None`` or a :class:`Translator` instance."
msgstr "トランスレータが ``None`` または :class:`Translator` インスタンスでない場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:3
msgid "Syncs the application commands to Discord."
msgstr "アプリケーションコマンドをDiscordに同期します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:5
msgid "This also runs the translator to get the translated strings necessary for feeding back into Discord."
msgstr "また、これは、トランスレータを実行し、Discordに提供する翻訳された文字列を取得します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:8
msgid "This must be called for the application commands to show up."
msgstr "アプリケーションコマンドを表示するためには、これを呼び出さないといけません。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:10
msgid "The guild to sync the commands to. If ``None`` then it syncs all global commands instead."
msgstr "コマンドを同期するギルド。指定されていない場合や ``None`` の場合グローバルコマンドを同期します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:14
msgid "Syncing the commands failed."
msgstr "コマンドの同期に失敗した場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:15
msgid "Syncing the commands failed due to a user related error, typically because     the command has invalid data. This is equivalent to an HTTP status code of     400."
msgstr "コマンドに不正なデータがあるなど、ユーザーによる誤りのためコマンドの同期が失敗した場合。これはHTTPステータスコード 400に相当します。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:16
msgid "The client does not have the ``applications.commands`` scope in the guild."
msgstr "クライアントがギルドで ``applications.commands`` スコープを有さない場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:18
msgid "An error occurred while translating the commands."
msgstr "コマンドの翻訳中にエラーが発生した場合。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:20
msgid "The application's commands that got synced."
msgstr "同期されたアプリケーションコマンド。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.sync:21
msgid "List[:class:`AppCommand`]"
msgstr "List[:class:`AppCommand`]"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.interaction_check:3
msgid "A global check to determine if an :class:`~discord.Interaction` should be processed by the tree."
msgstr ":class:`~discord.Interaction` がツリーによって処理されるかどうかを判断するグローバルチェック。"

#: ../../../discord/app_commands/tree.py:docstring of discord.app_commands.tree.CommandTree.interaction_check:6
msgid "The default implementation returns True (all interactions are processed), but can be overridden if custom behaviour is desired."
msgstr "デフォルトの実装では、True が返されます （すべてのインタラクションが処理されます）が、カスタム動作が必要な場合は上書きできます。"

#: ../../interactions/api.rst:549
msgid "Commands"
msgstr "コマンド"

#: ../../interactions/api.rst:552
msgid "Command"
msgstr "Command"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:1
msgid "A class that implements an application command."
msgstr "アプリケーションコマンドを実装するクラス。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:3
msgid "These are usually not created manually, instead they are created using one of the following decorators:"
msgstr "これは通常手動で作成されず、代わりに以下のデコレータを使用して作成されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:6
msgid ":func:`~discord.app_commands.command`"
msgstr ":func:`~discord.app_commands.command`"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:7
msgid ":meth:`Group.command <discord.app_commands.Group.command>`"
msgstr ":meth:`Group.command <discord.app_commands.Group.command>`"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:8
msgid ":meth:`CommandTree.command <discord.app_commands.CommandTree.command>`"
msgstr ":meth:`CommandTree.command <discord.app_commands.CommandTree.command>`"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:12
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:38
msgid "The name of the application command."
msgstr "アプリケーションコマンドの名前。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:14
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:44
msgid "The description of the application command. This shows up in the UI to describe the application command."
msgstr "アプリケーションコマンドの説明。これは、アプリケーションコマンドを説明するためのUIに表示されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:17
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.callback:1
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:13
msgid "The coroutine that is executed when the command is called."
msgstr "コマンドが呼び出されたときに実行されるコルーチン。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:30
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:86
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:48
msgid "The parent application command. ``None`` if there isn't one."
msgstr "親アプリケーションコマンド。存在しない場合は ``None`` 。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:51
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:69
msgid "A list of predicates that take a :class:`~discord.Interaction` parameter to indicate whether the command callback should be executed. If an exception is necessary to be thrown to signal failure, then one inherited from :exc:`AppCommandError` should be used. If all the checks fail without propagating an exception, :exc:`CheckFailure` is raised."
msgstr "コマンドコールバックが実行されるべきかを示す :class:`~discord.Interaction` パラメータを取るチェック関数の一覧。もし失敗を示すために例外を送出しないといけない場合は、 :exc:`AppCommandError` を継承するものを使用すべきです。もしすべてのチェックが例外を送出せずに失敗した場合は :exc:`CheckFailure` が発生します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:59
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:46
msgid "The default permissions that can execute this command on Discord. Note that server administrators can override this value in the client. Setting an empty permissions field will disallow anyone except server administrators from using the command in a guild."
msgstr "Discordでこのコマンドを実行できるデフォルトの権限。サーバー管理者はクライアントでこの値を上書きすることができます。 空の権限フィールドを設定すると、サーバー管理者以外はギルド内でそのコマンドを使用できなくなります。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:70
msgid "Whether the command should only be usable in guild contexts."
msgstr "コマンドをギルド内でのみ使用できるようにするかどうか。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command:88
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.root_parent:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:98
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Group.root_parent:3
msgid "Optional[:class:`Group`]"
msgstr "Optional[:class:`Group`]"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:1
msgid "A decorator that registers a coroutine as an autocomplete prompt for a parameter."
msgstr "パラメータのオートコンプリートに使用されるコルーチンを登録するデコレータ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:3
msgid "The coroutine callback must have 2 parameters, the :class:`~discord.Interaction`, and the current value by the user (the string currently being typed by the user)."
msgstr "コルーチンのコールバックは、 :class:`~discord.Interaction` とユーザーが現在入力している値 (入力された文字列) の 2 つのパラメータを受け取らないといけません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:6
msgid "To get the values from other parameters that may be filled in, accessing :attr:`.Interaction.namespace` will give a :class:`Namespace` object with those values."
msgstr "入力される可能性のある他のパラメータの値を取得するには、 :attr:`.Interaction.namespace` にアクセスして、これらの値を持つ :class:`Namespace` オブジェクトを取得できます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:10
msgid "Parent :func:`checks <check>` are ignored within an autocomplete. However, checks can be added to the autocomplete callback and the ones added will be called. If the checks fail for any reason then an empty list is sent as the interaction response."
msgstr "親の :func:`チェック <check>` はオートコンプリート内で無視されます。 ただし、チェックはautocomplete コールバックに追加することができ、追加したものが呼び出されます。 何らかの理由でチェックに失敗した場合、インタラクションの応答として空のリストが送信されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:14
msgid "The coroutine decorator **must** return a list of :class:`~discord.app_commands.Choice` objects. Only up to 25 objects are supported."
msgstr "コルーチンのデコレータは :class:`~discord.app_commands.Choice` オブジェクトのリストを **返さないといけません** 。オブジェクトは25個まで返すことができます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:18
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.autocomplete:13
msgid "The choices returned from this coroutine are suggestions. The user may ignore them and input their own value."
msgstr "このコルーチンから返される選択肢はあくまで提案であって、ユーザーはそれらを無視して自分の値を入力することができます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:20
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.describe:4
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.describe:15
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.rename:7
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.choices:3
msgid "Example:"
msgstr "例:"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:40
msgid "The parameter name to register as autocomplete."
msgstr "オートコンプリートに登録するパラメータ名。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.autocomplete:43
msgid "The coroutine passed is not actually a coroutine or     the parameter is not found or of an invalid type."
msgstr "渡されたコルーチンが実際にはコルーチンでない場合や、パラメータが見つからず、または無効な型であった場合。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.error:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.error:3
msgid "The local error handler is called whenever an exception is raised in the body of the command or during handling of the command. The error handler must take 2 parameters, the interaction and the error."
msgstr "ローカルのエラーハンドラは、コマンドの本体やコマンドの処理中に例外が発生するたびに呼び出されます。 エラーハンドラは、インタラクションとエラーの2つのパラメータを取らないといけません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.error:12
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.error:12
msgid "The coroutine passed is not actually a coroutine."
msgstr "渡された関数がコルーチンではない場合。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.callback:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.ContextMenu.callback:3
msgid ":ref:`coroutine <coroutine>`"
msgstr ":ref:`coroutine <coroutine>`"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.parameters:1
msgid "Returns a list of parameters for this command."
msgstr "このコマンドのパラメータのリストを返します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.parameters:3
msgid "This does not include the ``self`` or ``interaction`` parameters."
msgstr "これには ``self`` と ``interaction`` パラメータは含まれていません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.parameters:5
msgid "The parameters of this command."
msgstr "このコマンドのパラメータ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.parameters:6
msgid "List[:class:`Parameter`]"
msgstr "List[:class:`Parameter`]"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.get_parameter:1
msgid "Retrieves a parameter by its name."
msgstr "名前からパラメータを取得します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.get_parameter:3
msgid "The name must be the Python identifier rather than the renamed one for display on Discord."
msgstr "名前は Discordで表示される改名後のものではなく、Pythonの識別子でないといけません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.get_parameter:6
msgid "The parameter name in the callback function."
msgstr "コールバック関数のパラメータ名。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.get_parameter:9
msgid "The parameter or ``None`` if not found."
msgstr "パラメータ、または該当するものが見つからない場合 ``None`` が返ります。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.get_parameter:10
msgid "Optional[:class:`Parameter`]"
msgstr "Optional[:class:`Parameter`]"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Command.root_parent:1
msgid "The root parent of this command."
msgstr "コマンドの大元の親。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.add_check:1
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.add_check:1
msgid "Adds a check to the command."
msgstr "コマンドにチェックを追加します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.add_check:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.add_check:3
msgid "This is the non-decorator interface to :func:`check`."
msgstr "これは :func:`check` に対する非デコレーターインターフェイスです。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.add_check:5
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.add_check:5
msgid "The function that will be used as a check."
msgstr "チェックとして使用される関数。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.remove_check:1
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.remove_check:1
msgid "Removes a check from the command."
msgstr "コマンドからチェックを除去します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.remove_check:3
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.remove_check:3
msgid "This function is idempotent and will not raise an exception if the function is not in the command's checks."
msgstr "この関数は冪等性を保持しており、関数がコマンドのチェックに含まれていない場合でも例外が発生しません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Command.remove_check:6
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu.remove_check:6
msgid "The function to remove from the checks."
msgstr "チェックから除去する関数。"

#: ../../interactions/api.rst:567
msgid "Parameter"
msgstr "Parameter"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:1
msgid "A class that contains the parameter information of a :class:`Command` callback."
msgstr ":class:`Command` コールバックのパラメータ情報を含むクラス。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:7
msgid "The name of the parameter. This is the Python identifier for the parameter."
msgstr "パラメータの名前。これはパラメータのPython識別子です。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:13
msgid "The displayed name of the parameter on Discord."
msgstr "Discordに表示されるパラメータの名前。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:19
msgid "The description of the parameter."
msgstr "パラメータの説明。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:25
msgid "Whether the parameter has an autocomplete handler."
msgstr "パラメータにオートコンプリートハンドラがあるかどうか。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:31
msgid "The display name's locale string, if available."
msgstr "利用可能な場合、表示名のローカライズ可能な文字列。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:33
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:39
msgid "Optional[:class:`locale_str`]"
msgstr "Optional[:class:`locale_str`]"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:37
msgid "The description's locale string, if available."
msgstr "利用可能な場合、説明のローカライズ可能な文字列。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:43
msgid "Whether the parameter is required"
msgstr "パラメータが必須かどうか。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:49
msgid "A list of choices this parameter takes, if any."
msgstr "存在する場合、このパラメータが取る選択肢のリスト。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:51
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.autocomplete:17
msgid "List[:class:`~discord.app_commands.Choice`]"
msgstr "List[:class:`~discord.app_commands.Choice`]"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:55
msgid "The underlying type of this parameter."
msgstr "このパラメータの種類。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:79
msgid "The default value of the parameter, if given. If not given then this is :data:`~discord.utils.MISSING`."
msgstr "指定されている場合、パラメータの既定値。指定されていない場合は、 :data:`~discord.utils.MISSING` です。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:86
msgid "The command this parameter is attached to."
msgstr "このパラメータが属するコマンド。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Parameter:88
msgid ":class:`Command`"
msgstr ":class:`Command`"

#: ../../interactions/api.rst:575
msgid "ContextMenu"
msgstr "ContextMenu"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:1
msgid "A class that implements a context menu application command."
msgstr "コンテキストメニューアプリケーションコマンドを実装するクラス。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:6
msgid ":func:`~discord.app_commands.context_menu`"
msgstr ":func:`~discord.app_commands.context_menu`"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:7
msgid ":meth:`CommandTree.context_menu <discord.app_commands.CommandTree.context_menu>`"
msgstr ":meth:`CommandTree.context_menu <discord.app_commands.CommandTree.context_menu>`"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:11
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:33
msgid "The name of the context menu."
msgstr "コンテキストメニューの名前。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:15
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:39
msgid "The type of context menu application command. By default, this is inferred by the parameter of the callback."
msgstr "コンテキストメニューアプリケーションコマンドの種類。デフォルトでは、コールバックのパラメータによって推定されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:42
msgid ":class:`.AppCommandType`"
msgstr ":class:`.AppCommandType`"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.ContextMenu:55
msgid "Whether the command should only be usable in guild contexts. Defaults to ``False``."
msgstr "コマンドをギルド内でのみ使用できるようにするかどうか。デフォルトは ``False`` です。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.ContextMenu.callback:1
msgid "The coroutine that is executed when the context menu is called."
msgstr "コンテキストメニューが呼び出されたときに実行されるコルーチン。"

#: ../../interactions/api.rst:587
msgid "Group"
msgstr "Group"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:1
msgid "A class that implements an application command group."
msgstr "アプリケーションコマンドグループを実装するクラス。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:3
msgid "These are usually inherited rather than created manually."
msgstr "これらは通常、手動で作成するのではなく継承されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:5
msgid "Decorators such as :func:`guild_only`, :func:`guilds`, and :func:`default_permissions` will apply to the group if used on top of a subclass. For example:"
msgstr ":func:`guild_only` 、 :func:`guilds` 、 :func:`default_permissions` などのデコレータは、サブクラスの上に置かれている場合、グループに適用されます。例えば："

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:18
msgid "The name of the group. If not given, it defaults to a lower-case kebab-case version of the class name."
msgstr "グループ名。指定しない場合は、デフォルトでは小文字のケバブケース化したクラス名です。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:21
msgid "The description of the group. This shows up in the UI to describe the group. If not given, it defaults to the docstring of the class shortened to 100 characters."
msgstr "グループの説明。これは、グループを説明するためにUIに表示されます。 指定されていない場合は、クラスの docstring を 100 文字以内に短縮したものが使用されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:31
msgid "The default permissions that can execute this group on Discord. Note that server administrators can override this value in the client. Setting an empty permissions field will disallow anyone except server administrators from using the command in a guild.  Due to a Discord limitation, this does not work on subcommands."
msgstr "Discordでこのグループを実行できるデフォルトの権限。サーバー管理者はクライアントでこの値を上書きすることができます。 空の権限フィールドを設定すると、サーバー管理者以外はギルド内でそのコマンドを使用できなくなります。Discordの制限により、サブコマンドでは利用できません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:31
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:69
msgid "The default permissions that can execute this group on Discord. Note that server administrators can override this value in the client. Setting an empty permissions field will disallow anyone except server administrators from using the command in a guild."
msgstr "Discordでこのグループを実行できるデフォルトの権限。サーバー管理者はクライアントでこの値を上書きすることができます。 空の権限フィールドを設定すると、サーバー管理者以外はギルド内でそのコマンドを使用できなくなります。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:38
msgid "Whether the group should only be usable in guild contexts. Defaults to ``False``.  Due to a Discord limitation, this does not work on subcommands."
msgstr "グループをギルド内でのみ使用できるようにするかどうか。デフォルトは ``False`` です。Discordの制限により、サブコマンドでは利用できません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:38
msgid "Whether the group should only be usable in guild contexts. Defaults to ``False``."
msgstr "グループをギルド内でのみ使用できるようにするかどうか。デフォルトは ``False`` です。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:56
msgid "The name of the group."
msgstr "グループの名前。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:62
msgid "The description of the group. This shows up in the UI to describe the group."
msgstr "グループの説明。グループを説明するため、UIに表示されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:80
msgid "Whether the group should only be usable in guild contexts."
msgstr "グループをギルド内でのみ使用できるようにするかどうか。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group:96
msgid "The parent group. ``None`` if there isn't one."
msgstr "親グループ。存在しない場合は ``None`` 。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.command:1
msgid "A decorator that creates an application command from a regular function under this group."
msgstr "通常の関数からアプリケーションコマンドをこのグループの下に作成するデコレータ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.error:3
msgid "The local error handler is called whenever an exception is raised in a child command. The error handler must take 2 parameters, the interaction and the error."
msgstr "ローカルのエラーハンドラは、子コマンドで例外が発生するたびに呼び出されます。 エラーハンドラは、インタラクションとエラーの2つのパラメータを取らないといけません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.error:11
msgid "The coroutine passed is not actually a coroutine, or is an invalid coroutine."
msgstr "渡されたコルーチンが実際にはコルーチンでないか、無効なコルーチンである場合。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Group.root_parent:1
msgid "The parent of this group."
msgstr "このグループの親。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Group.qualified_name:1
msgid "Returns the fully qualified group name."
msgstr "完全修飾されたグループ名を返します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Group.qualified_name:3
msgid "The qualified name includes the parent name as well. For example, in a group like ``/foo bar`` the qualified name is ``foo bar``."
msgstr "修飾名には親の名前も含まれています。例えば、 ``/foo bar`` のようなグループでは修飾名は ``foo bar`` です。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Group.commands:1
msgid "The commands that this group contains."
msgstr "このグループに含まれるコマンド。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.Group.commands:3
msgid "List[Union[:class:`Command`, :class:`Group`]]"
msgstr "List[Union[:class:`Command`, :class:`Group`]]"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.walk_commands:1
msgid "An iterator that recursively walks through all commands that this group contains."
msgstr "グループが含むすべてのコマンドを、再帰的に網羅するイテレータ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.walk_commands:3
msgid "Union[:class:`Command`, :class:`Group`] -- The commands in this group."
msgstr "Union[:class:`Command`, :class:`Group`] -- グループ内のコマンド。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.on_error:3
msgid "A callback that is called when a child's command raises an :exc:`AppCommandError`."
msgstr "子コマンドが :exc:`AppCommandError` を送出したときに呼び出されるコールバック。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.on_error:7
#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.load:5
#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.unload:5
msgid "The default implementation does nothing."
msgstr "デフォルトの実装では何もしません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.interaction_check:3
msgid "A callback that is called when an interaction happens within the group that checks whether a command inside the group should be executed."
msgstr "グループ内でインタラクションが発生したときに、グループ内のコマンドを実行すべきかを確認するコールバック。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.interaction_check:13
msgid "If an exception occurs within the body then the check is considered a failure and error handlers such as :meth:`on_error` is called. See :exc:`AppCommandError` for more information."
msgstr "この中で例外が発生した場合チェックが失敗したとみなされ :meth:`on_error` のようなエラーハンドラが呼び出されます。詳細は :exc:`AppCommandError` を参照してください。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.add_command:1
msgid "Adds a command or group to this group's internal list of commands."
msgstr "このグループの内部コマンドリストにコマンドまたはグループを追加します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.add_command:3
msgid "The command or group to add."
msgstr "追加するコマンドまたはグループ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.add_command:5
msgid "Whether to override a pre-existing command or group with the same name. If ``False`` then an exception is raised."
msgstr "既存の同名のコマンドまたはグループを上書きするかどうか。 ``False`` の場合は例外が発生します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.add_command:9
msgid "The command or group is already registered. Note that the :attr:`CommandAlreadyRegistered.guild_id`     attribute will always be ``None`` in this case."
msgstr "コマンドやグループがすでに登録されている場合。なお、この場合 :attr:`CommandAlreadyRegistered.guild_id` は常に ``None`` になります。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.add_command:10
msgid "There are too many commands already registered or the group is too     deeply nested."
msgstr "登録されたコマンド数が多すぎるか、グループが深くネストされすぎている場合。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.add_command:11
msgid "The wrong command type was passed."
msgstr "間違った種類のコマンドが渡された場合。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.remove_command:1
msgid "Removes a command or group from the internal list of commands."
msgstr "内部のコマンドリストから該当するコマンドまたはグループを検索し、それを除去します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.remove_command:3
msgid "The name of the command or group to remove."
msgstr "除去するコマンドまたはグループの名前。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.remove_command:6
msgid "The command that was removed. If nothing was removed then ``None`` is returned instead."
msgstr "除去されたコマンド。何も除去されなかった場合は ``None`` が返ります。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.remove_command:8
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.get_command:8
msgid "Optional[Union[:class:`~discord.app_commands.Command`, :class:`~discord.app_commands.Group`]]"
msgstr "Optional[Union[:class:`~discord.app_commands.Command`, :class:`~discord.app_commands.Group`]]"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.get_command:1
msgid "Retrieves a command or group from its name."
msgstr "名前からコマンドまたはグループを取得します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.get_command:3
msgid "The name of the command or group to retrieve."
msgstr "取得するコマンドまたはグループの名前。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.Group.get_command:6
msgid "The command or group that was retrieved. If nothing was found then ``None`` is returned instead."
msgstr "取得されたコマンドまたはグループ。何も見つからなかった場合は ``None`` が返ります。"

#: ../../interactions/api.rst:602
msgid "Decorators"
msgstr "デコレータ"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.command:1
msgid "Creates an application command from a regular function."
msgstr "通常の関数からアプリケーションコマンドを作成します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.context_menu:1
msgid "Creates an application command context menu from a regular function."
msgstr "通常の関数からアプリケーションコマンドコンテキストメニューを作成します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.describe:1
msgid "Describes the given parameters by their name using the key of the keyword argument as the name."
msgstr "キーワード引数のキーを名前として使用して与えられたパラメータを説明します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.describe:13
msgid "Alternatively, you can describe parameters using Google, Sphinx, or Numpy style docstrings."
msgstr "または、Google、Sphinx、Numpy スタイルのドキュメント文字列を使用してパラメータを記述することもできます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.describe:30
msgid "The description of the parameters."
msgstr "パラメータの説明。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.describe:33
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.rename:20
msgid "The parameter name is not found."
msgstr "パラメータ名が見つからない場合。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.rename:1
msgid "Renames the given parameters by their name using the key of the keyword argument as the name."
msgstr "キーワード引数のキーを名前として使用して与えられたパラメータを改名します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.rename:4
msgid "This renames the parameter within the Discord UI. When referring to the parameter in other decorators, the parameter name used in the function is used instead of the renamed one."
msgstr "これはDiscord UI内のパラメータの名前を変更します。 他のデコレータでパラメータを参照する場合、改名先のものではなく関数内で使用されるパラメータ名を使用しないといけません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.rename:16
msgid "The name of the parameters."
msgstr "パラメータの名前。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.rename:19
msgid "The parameter name is already used by another parameter."
msgstr "パラメータ名がすでに他のパラメータによって使用されている場合。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.choices:1
msgid "Instructs the given parameters by their name to use the given choices for their choices."
msgstr "与えられたパラメータに、与えられた選択肢を使用するように名前で指示します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.choices:19
msgid "This is not the only way to provide choices to a command. There are two more ergonomic ways of doing this. The first one is to use a :obj:`typing.Literal` annotation:"
msgstr "これはコマンドに選択肢を提供する唯一の方法ではありません。より使いやすい方法が2つあります。 一つ目は、 :obj:`typing.Literal` アノテーションです。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.choices:29
msgid "The second way is to use an :class:`enum.Enum`:"
msgstr "二つ目の方法は、 :class:`enum.Enum` を使用することです。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.choices:44
msgid "The choices of the parameters."
msgstr "パラメータの選択肢。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.choices:46
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.autocomplete:36
msgid "The parameter name is not found or the parameter type was incorrect."
msgstr "パラメータ名が見つからないか、パラメータの型が正しくない場合。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.autocomplete:1
msgid "Associates the given parameters with the given autocomplete callback."
msgstr "与えられたパラメータと与えられたオートコンプリートコールバックを関連付けます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.autocomplete:3
msgid "Autocomplete is only supported on types that have :class:`str`, :class:`int`, or :class:`float` values."
msgstr "オートコンプリートは、 :class:`str` 、 :class:`int` 、または :class:`float` の値を持つ型でのみサポートされています。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.autocomplete:6
msgid ":func:`Checks <check>` are supported, however they must be attached to the autocomplete callback in order to work. Checks attached to the command are ignored when invoking the autocomplete callback."
msgstr ":func:`チェック <check>` はサポートされていますが、動作するためにはオートコンプリートコールバックに付属させる必要があります。 オートコンプリートコールバックを呼び出すと、コマンドに付属したチェックは無視されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.autocomplete:10
msgid "For more information, see the :meth:`Command.autocomplete` documentation."
msgstr "詳細については、 :meth:`Command.autocomplete` ドキュメントを参照してください。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.autocomplete:34
msgid "The parameters to mark as autocomplete."
msgstr "オートコンプリートに登録するパラメータ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guilds:1
msgid "Associates the given guilds with the command."
msgstr "与えられたギルドをコマンドに関連付けます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guilds:3
msgid "When the command instance is added to a :class:`CommandTree`, the guilds that are specified by this decorator become the default guilds that it's added to rather than being a global command."
msgstr ":class:`CommandTree` にコマンドインスタンスが追加されたとき、 コマンドはデフォルトでグローバルコマンドではなく、このデコレータで指定されたギルドのコマンドとなります。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guilds:9
msgid "Due to an implementation quirk and Python limitation, if this is used in conjunction with the :meth:`CommandTree.command` or :meth:`CommandTree.context_menu` decorator then this must go below that decorator."
msgstr "実装上の都合とPythonの制限のため、これを :meth:`CommandTree.command` や :meth:`CommandTree.context_menu` デコレータと同時に使用する場合はこれはそのデコレータの下に置かないといけません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guilds:24
msgid "The guilds to associate this command with. The command tree will use this as the default when added rather than adding it as a global command."
msgstr "このコマンドを関連付けるギルド。 コマンドツリーは、グローバルコマンドではなく、このギルドにデフォルトで追加します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guild_only:1
msgid "A decorator that indicates this command can only be used in a guild context."
msgstr "コマンドがギルド内でのみ利用できることを示すデコレータ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guild_only:3
msgid "This is **not** implemented as a :func:`check`, and is instead verified by Discord server side. Therefore, there is no error handler called when a command is used within a private message."
msgstr "これは :func:`check` として実装されて *おらず* 、代わりにDiscordサーバー側で検証されます。 そのため、プライベートメッセージ内でコマンドが使用されたときに呼び出されるエラーハンドラはありません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guild_only:6
msgid "This decorator can be called with or without parentheses."
msgstr "このデコレータは括弧の有無にかかわらず呼び出すことができます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.guild_only:8
#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.default_permissions:12
msgid "Due to a Discord limitation, this decorator does nothing in subcommands and is ignored."
msgstr "Discordの制限のため、このデコレータはサブコマンドでは動作せず無視されます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.default_permissions:1
msgid "A decorator that sets the default permissions needed to execute this command."
msgstr "このコマンドを実行するために必要なデフォルトの権限を設定するデコレータ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.default_permissions:3
msgid "When this decorator is used, by default users must have these permissions to execute the command. However, an administrator can change the permissions needed to execute this command using the official client. Therefore, this only serves as a hint."
msgstr "このデコレータを使用すると、デフォルトではユーザーはコマンドを実行するためにこれらの権限を持っている必要があります。 ただし、管理者は公式クライアントを使用してこのコマンドを実行するために必要な権限を変更することができます。したがって、これはヒントとしてのみ機能します。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.default_permissions:7
msgid "Setting an empty permissions field, including via calling this with no arguments, will disallow anyone except server administrators from using the command in a guild."
msgstr "引数なしで呼び出すなどして空の権限フィールドを設定した場合は、このコマンドはギルド内ではサーバー管理者以外使用できなくなります。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.default_permissions:10
msgid "This is sent to Discord server side, and is not a :func:`check`. Therefore, error handlers are not called."
msgstr "これはDiscordサーバー側に送信され、 :func:`check` ではないため、エラーハンドラは呼び出されません。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.default_permissions:16
msgid "This serves as a *hint* and members are *not* required to have the permissions given to actually execute this command. If you want to ensure that members have the permissions needed, consider using :func:`~discord.app_commands.checks.has_permissions` instead."
msgstr "これは *ヒント* として機能し、メンバーはこれらのコマンドを実際に実行するのに権限を持つ必要は *ありません* 。メンバーが必要な権限を有することを確かめたい場合は、代わりに :func:`~discord.app_commands.checks.has_permissions` を使用してみてください。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.default_permissions:20
msgid "Keyword arguments denoting the permissions to set as the default."
msgstr "デフォルトとして設定する権限を示すキーワード引数。"

#: ../../interactions/api.rst:632
msgid "Checks"
msgstr "チェック"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.check:1
msgid "A decorator that adds a check to an application command."
msgstr "アプリケーションコマンドにチェックを追加するデコレータ。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.check:3
msgid "These checks should be predicates that take in a single parameter taking a :class:`~discord.Interaction`. If the check returns a ``False``\\-like value then during invocation a :exc:`CheckFailure` exception is raised and sent to the appropriate error handlers."
msgstr "これらのチェックは唯一の引数 :class:`~discord.Interaction` を取る関数であるべきです。もしチェックが ``False`` のような値を返せば、呼び出し中に :exc:`.CheckFailure` が発生され適切なエラーハンドラに送られます。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.check:8
msgid "These checks can be either a coroutine or not."
msgstr "これらのチェックはコルーチンであってもなくてもよいです。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.check:12
msgid "Creating a basic check to see if the command invoker is you."
msgstr "コマンドを呼び出したのがあなたであるかどうかを確認するための基本的なチェックの作成。"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.check:24
msgid "Transforming common checks into its own decorator:"
msgstr "一般的なチェックを独自のデコレータに変換します:"

#: ../../../discord/app_commands/commands.py:docstring of discord.app_commands.commands.check:38
msgid "The predicate to check if the command should be invoked."
msgstr "コマンドが呼び出されるかどうかをチェックする関数。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_role:1
msgid "A :func:`~discord.app_commands.check` that is added that checks if the member invoking the command has the role specified via the name or ID specified."
msgstr "コマンドを呼び出したメンバーが、指定された名前またはIDのロールを持っているかどうかのチェックを追加する :func:`~discord.app_commands.check` 。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_role:4
msgid "If a string is specified, you must give the exact name of the role, including caps and spelling."
msgstr "文字列が指定された場合は、大文字やつづりを含めロールの名前を正確に指定する必要があります。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_role:7
msgid "If an integer is specified, you must give the exact snowflake ID of the role."
msgstr "整数が指定されている場合は、ロールの正確なsnowflake IDを指定する必要があります。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_role:9
msgid "This check raises one of two special exceptions, :exc:`~discord.app_commands.MissingRole` if the user is missing a role, or :exc:`~discord.app_commands.NoPrivateMessage` if it is used in a private message. Both inherit from :exc:`~discord.app_commands.CheckFailure`."
msgstr "このチェックは２つの特別な例外のうち１つ、もしユーザがロールを持っていないなら :exc:`~discord.app_commands.MissingRole` を、プライベートメッセージで使用されたなら :exc:`~discord.app_commands.NoPrivateMessage` を発生します。どちらも :exc:`~discord.app_commands.CheckFailure` を継承します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_role:17
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_any_role:15
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_permissions:17
msgid "This is different from the permission system that Discord provides for application commands. This is done entirely locally in the program rather than being handled by Discord."
msgstr "これはDiscordがアプリケーションコマンドに提供する権限システムとは異なります。これはDiscord側で処理されるものではなく、完全にローカルのプログラム内で行われます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_role:21
msgid "The name or ID of the role to check."
msgstr "チェックするロールの名前またはID。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_any_role:1
msgid "A :func:`~discord.app_commands.check` that is added that checks if the member invoking the command has **any** of the roles specified. This means that if they have one out of the three roles specified, then this check will return ``True``."
msgstr "コマンドを呼び出したメンバーが、指定された名前またはIDのロールのうちの **どれか** を持っているかをチェックを追加する :func:`~discord.app_commands.check` 。 これは、指定された3つのうちのどのロールが指定されていても、このチェックが ``True`` を返すことを意味します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_any_role:5
msgid "Similar to :func:`has_role`\\, the names or IDs passed in must be exact."
msgstr ":func:`has_role` と同様に、渡された名前やIDは正確でなければなりません。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_any_role:7
msgid "This check raises one of two special exceptions, :exc:`~discord.app_commands.MissingAnyRole` if the user is missing all roles, or :exc:`~discord.app_commands.NoPrivateMessage` if it is used in a private message. Both inherit from :exc:`~discord.app_commands.CheckFailure`."
msgstr "このチェックは２つの特別な例外のうち１つ、もしユーザがロールをすべて持っていないなら :exc:`~discord.app_commands.MissingAnyRole` を、プライベートメッセージで使用されたなら :exc:`~discord.app_commands.NoPrivateMessage` を発生します。どちらも :exc:`~discord.app_commands.CheckFailure` を継承します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_any_role:19
msgid "An argument list of names or IDs to check that the member has roles wise."
msgstr "メンバーが割り当てられたロールを持っているかどうかをチェックする名前またはIDの引数リスト。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_permissions:1
msgid "A :func:`~discord.app_commands.check` that is added that checks if the member has all of the permissions necessary."
msgstr "メンバーが必要なすべての権限を持っているかどうかのチェックを追加する :func:`~discord.app_commands.check` 。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_permissions:4
msgid "Note that this check operates on the permissions given by :attr:`discord.Interaction.permissions`."
msgstr "このチェックは :attr:`discord.Interaction.permissions` によって与えられた権限で動作することに注意してください。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_permissions:7
msgid "The permissions passed in must be exactly like the properties shown under :class:`discord.Permissions`."
msgstr "渡された権限は、 :class:`discord.Permissions` のプロパティとまったく同じでなければなりません。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_permissions:10
msgid "This check raises a special exception, :exc:`~discord.app_commands.MissingPermissions` that is inherited from :exc:`~discord.app_commands.CheckFailure`."
msgstr "このチェックは特別な例外であり、 :exc:`~discord.app_commands.CheckFailure` から継承されている :exc:`~discord.app_commands.MissingPermissions` を発生します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.has_permissions:21
msgid "Keyword arguments denoting the permissions to check for."
msgstr "確認する権限を示すキーワード引数。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.bot_has_permissions:1
msgid "Similar to :func:`has_permissions` except checks if the bot itself has the permissions listed. This relies on :attr:`discord.Interaction.app_permissions`."
msgstr ":func:`has_permissions` と似ていますが、ボット自体に列挙された権限が存在するかを確認します。これは :attr:`discord.Interaction.app_permissions` を使用します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.bot_has_permissions:4
msgid "This check raises a special exception, :exc:`~discord.app_commands.BotMissingPermissions` that is inherited from :exc:`~discord.app_commands.CheckFailure`."
msgstr "このチェックは特別な例外であり、 :exc:`~discord.app_commands.CheckFailure` から継承されている :exc:`~discord.app_commands.BotMissingPermissions` を発生します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.cooldown:1
msgid "A decorator that adds a cooldown to a command."
msgstr "コマンドにクールダウンを追加するデコレータ。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.cooldown:3
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.dynamic_cooldown:3
msgid "A cooldown allows a command to only be used a specific amount of times in a specific time frame. These cooldowns are based off of the ``key`` function provided. If a ``key`` is not provided then it defaults to a user-level cooldown. The ``key`` function must take a single parameter, the :class:`discord.Interaction` and return a value that is used as a key to the internal cooldown mapping."
msgstr "クールダウンを使用すると、特定の時間枠の中でコマンドが使用できる回数を制限できます。このクールダウンは渡された ``key`` 関数に基づきます。もし ``key`` 関数が渡されない場合はユーザーレベルのクールダウンとなります。 ``key`` 関数は単一のパラメータとして :class:`discord.Interaction` を取り、内部のクールダウンマッピングのキーとして使用される値を返さないといけません。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.cooldown:10
msgid "The ``key`` function can optionally be a coroutine."
msgstr "``key`` 関数は必要に応じてコルーチンにすることができます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.cooldown:12
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.dynamic_cooldown:17
msgid "If a cooldown is triggered, then :exc:`~discord.app_commands.CommandOnCooldown` is raised to the error handlers."
msgstr "クールダウンが発生した場合は、 :exc:`~discord.app_commands.CommandOnCooldown` がエラーハンドラに送出されます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.cooldown:17
msgid "Setting a one per 5 seconds per member cooldown on a command:"
msgstr "コマンドに1メンバーにつき5秒あたり1回のクールダウンを設定します:"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.cooldown:31
msgid "The number of times a command can be used before triggering a cooldown."
msgstr "クールダウンを発生させる前にコマンドを使用できる回数。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.cooldown:33
msgid "The amount of seconds to wait for a cooldown when it's been triggered."
msgstr "トリガーされたときにクールダウンを待つ秒数。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.cooldown:35
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.dynamic_cooldown:44
msgid "A function that returns a key to the mapping denoting the type of cooldown. Can optionally be a coroutine. If not given then defaults to a user-level cooldown. If ``None`` is passed then it is interpreted as a \"global\" cooldown."
msgstr "クールダウンの種類を示すキーを返す関数。これはコルーチンにすることもできます。もし渡されない場合既定でユーザーレベルのクールダウンとなります。 ``None`` が渡された場合「グローバル」クールダウンと解釈されます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.dynamic_cooldown:1
msgid "A decorator that adds a dynamic cooldown to a command."
msgstr "コマンドに動的なクールダウンを追加するデコレータ。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.dynamic_cooldown:10
msgid "If a ``factory`` function is given, it must be a function that accepts a single parameter of type :class:`discord.Interaction` and must return a :class:`~discord.app_commands.Cooldown` or ``None``. If ``None`` is returned then that cooldown is effectively bypassed."
msgstr "``factory`` 関数が与えられた場合、それは単一のパラメータとして :class:`discord.Interaction` を取り、 :class:`~discord.app_commands.Cooldown` または ``None`` を返す関数でないといけません。もし ``None`` を返せば、クールダウンは事実上回避されます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.dynamic_cooldown:15
msgid "Both ``key`` and ``factory`` can optionally be coroutines."
msgstr "必要に応じて、 ``key`` と ``factory`` の両方をコルーチンにできます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.dynamic_cooldown:22
msgid "Setting a cooldown for everyone but the owner."
msgstr "所有者以外の全員にクールダウンを設定します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.dynamic_cooldown:41
msgid "A function that takes an interaction and returns a cooldown that will apply to that interaction or ``None`` if the interaction should not have a cooldown."
msgstr "インタラクションを受け取り、このインタラクションに適用されるクールダウンまたはクールダウンを回避する場合 ``None`` を返す関数。"

#: ../../interactions/api.rst:656
msgid "Cooldown"
msgstr "Cooldown"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown:1
msgid "Represents a cooldown for a command."
msgstr "コマンドのクールダウンを表します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown:7
msgid "The total number of tokens available per :attr:`per` seconds."
msgstr ":attr:`per` 秒あたりのトークンの総数。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown:9
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown:15
#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.get_retry_after:8
#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:47
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandOnCooldown:17
msgid ":class:`float`"
msgstr ":class:`float`"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown:13
msgid "The length of the cooldown period in seconds."
msgstr "秒単位のクールダウン期間の長さ。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.get_tokens:1
msgid "Returns the number of available tokens before rate limiting is applied."
msgstr "レート制限が適用される前に利用可能なトークンの数を返します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.get_tokens:3
msgid "The time in seconds since Unix epoch to calculate tokens at. If not supplied then :func:`time.time()` is used."
msgstr "トークンを計算するためのUnixエポックからの秒数。指定されていない場合は :func:`time.time()` が使用されます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.get_tokens:7
msgid "The number of tokens available before the cooldown is to be applied."
msgstr "クールダウン前に利用可能なトークンの数。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.get_retry_after:1
msgid "Returns the time in seconds until the cooldown will be reset."
msgstr "クールダウンがリセットされるまでの時間を秒単位で返します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.get_retry_after:3
msgid "The current time in seconds since Unix epoch. If not supplied, then :func:`time.time()` is used."
msgstr "現在のUnixエポックからの秒数。指定されていない場合は :func:`time.time()` が使用されます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.get_retry_after:7
msgid "The number of seconds to wait before this cooldown will be reset."
msgstr "クールダウンがリセットされるまで待たないといけない秒数。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.update_rate_limit:1
msgid "Updates the cooldown rate limit."
msgstr "クールダウンのレート制限を更新します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.update_rate_limit:3
msgid "The time in seconds since Unix epoch to update the rate limit at. If not supplied, then :func:`time.time()` is used."
msgstr "レート制限を更新するためのUnixエポックからの秒数。指定されていない場合は :func:`time.time()` が使用されます。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.update_rate_limit:6
msgid "The amount of tokens to deduct from the rate limit."
msgstr "レート制限から差し引くトークンの量。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.update_rate_limit:9
msgid "The retry-after time in seconds if rate limited."
msgstr "レート制限中の場合の秒単位の再試行時間。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.reset:1
msgid "Reset the cooldown to its initial state."
msgstr "クールダウンを初期状態にリセットします。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.copy:1
msgid "Creates a copy of this cooldown."
msgstr "クールダウンのコピーを作成します。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.copy:3
msgid "A new instance of this cooldown."
msgstr "このクールダウンの新しいインスタンス。"

#: ../../../discord/app_commands/checks.py:docstring of discord.app_commands.checks.Cooldown.copy:4
msgid ":class:`Cooldown`"
msgstr ":class:`Cooldown`"

#: ../../interactions/api.rst:665
msgid "Namespace"
msgstr "Namespace"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:1
msgid "An object that holds the parameters being passed to a command in a mostly raw state."
msgstr "ほとんど生の状態でコマンドに渡されるパラメータを保持するオブジェクト。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:3
msgid "This class is deliberately simple and just holds the option name and resolved value as a simple key-pair mapping. These attributes can be accessed using dot notation. For example, an option with the name of ``example`` can be accessed using ``ns.example``. If an attribute is not found, then ``None`` is returned rather than an attribute error."
msgstr "このクラスは意図的にシンプルで、オプション名と解決された値を単純なキーペアマッピングとして保持します。 これらの属性はドット表記を使用してアクセスできます。 例えば、 ``example`` という名前のオプションは ``ns.example`` を使ってアクセスできます。 属性が見つからない場合は、属性エラーではなく ``None`` が返されます。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:10
msgid "The key names come from the raw Discord data, which means that if a parameter was renamed then the renamed key is used instead of the function parameter name."
msgstr "キー名はDiscordの生データから得られたものなので、パラメータの名前が変更された場合、関数のパラメータ名ではなく改名後のキーが使用されます。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:19
msgid "Checks if two namespaces are equal by checking if all attributes are equal."
msgstr "二つの名前空間が等しいかを、すべての属性が等しいかによって確認します。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:22
msgid "Checks if two namespaces are not equal."
msgstr "二つの名前空間が等しくないかを比較します。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:25
msgid "Returns an attribute if it is found, otherwise raises a :exc:`KeyError`."
msgstr "見つかった場合は属性を返し、そうでなければ、 :exc:`KeyError` を送出します。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:29
msgid "Checks if the attribute is in the namespace."
msgstr "属性が名前空間にあるかどうかを確認します。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:32
msgid "Returns an iterator of ``(name, value)`` pairs. This allows it to be, for example, constructed as a dict or a list of pairs."
msgstr "``(name, value)`` ペアのイテレータを返します。これにより、例えば、辞書型やペアのリストに変換できます。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:35
msgid "This namespace object converts resolved objects into their appropriate form depending on their type. Consult the table below for conversion information."
msgstr "この名前空間オブジェクトは、種類により解決されたオブジェクトを適切な形式に変換します。変換情報については、以下の表を参照してください。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:39
msgid "Option Type"
msgstr "オプションの種類"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:39
msgid "Resolved Type"
msgstr "解決される型"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:41
msgid ":attr:`.AppCommandOptionType.string`"
msgstr ":attr:`.AppCommandOptionType.string`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:43
msgid ":attr:`.AppCommandOptionType.integer`"
msgstr ":attr:`.AppCommandOptionType.integer`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:45
msgid ":attr:`.AppCommandOptionType.boolean`"
msgstr ":attr:`.AppCommandOptionType.boolean`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:47
msgid ":attr:`.AppCommandOptionType.number`"
msgstr ":attr:`.AppCommandOptionType.number`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:49
msgid ":attr:`.AppCommandOptionType.user`"
msgstr ":attr:`.AppCommandOptionType.user`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:49
msgid ":class:`~discord.User` or :class:`~discord.Member`"
msgstr ":class:`~discord.User` または :class:`~discord.Member`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:51
msgid ":attr:`.AppCommandOptionType.channel`"
msgstr ":attr:`.AppCommandOptionType.channel`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:51
msgid ":class:`.AppCommandChannel` or :class:`.AppCommandThread`"
msgstr ":class:`.AppCommandChannel` または :class:`.AppCommandThread`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:53
msgid ":attr:`.AppCommandOptionType.role`"
msgstr ":attr:`.AppCommandOptionType.role`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:53
msgid ":class:`~discord.Role`"
msgstr ":class:`~discord.Role`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:55
msgid ":attr:`.AppCommandOptionType.mentionable`"
msgstr ":attr:`.AppCommandOptionType.mentionable`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:55
msgid ":class:`~discord.User` or :class:`~discord.Member`, or :class:`~discord.Role`"
msgstr ":class:`~discord.User` または :class:`~discord.Member` または :class:`~discord.Role`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:57
msgid ":attr:`.AppCommandOptionType.attachment`"
msgstr ":attr:`.AppCommandOptionType.attachment`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:57
msgid ":class:`~discord.Attachment`"
msgstr ":class:`~discord.Attachment`"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:62
msgid "In autocomplete interactions, the namespace might not be validated or filled in. Discord does not send the resolved data as well, so this means that certain fields end up just as IDs rather than the resolved data. In these cases, a :class:`discord.Object` is returned instead."
msgstr "オートコンプリートのインタラクションでは、名前空間が検証されていないか、入力されていない可能性があります。 Discordは解決されたデータも送信しません。このため、特定のフィールドには解決されたデータではなくIDが存在することがあります。 この場合、代わりに :class:`discord.Object` が返されます。"

#: ../../../discord/app_commands/namespace.py:docstring of discord.app_commands.namespace.Namespace:66
msgid "This is a Discord limitation."
msgstr "これはDiscordの制限です。"

#: ../../interactions/api.rst:673
msgid "Transformers"
msgstr "トランスフォーマー"

#: ../../interactions/api.rst:676
msgid "Transformer"
msgstr "Transformer"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer:1
msgid "The base class that allows a type annotation in an application command parameter to map into a :class:`~discord.AppCommandOptionType` and transform the raw value into one from this type."
msgstr "アプリケーションコマンドのパラメータの型アノテーションを :class:`~discord.AppCommandOptionType` に対応させ、生の値をその型の値に変換する基底クラス。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer:5
msgid "This class is customisable through the overriding of methods and properties in the class and by using it as the second type parameter of the :class:`~discord.app_commands.Transform` class. For example, to convert a string into a custom pair type:"
msgstr "このクラスは、クラス内のメソッドとプロパティを上書きして :class:`~discord.app_commands.Transform` クラスの第二型パラメータとして渡すことによってカスタマイズできます。たとえば、文字列をカスタムのペア型に変換するには:"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer:27
msgid "If a class is passed instead of an instance to the second type parameter, then it is constructed with no arguments passed to the ``__init__`` method."
msgstr "二番目の型パラメータにインスタンスの代わりにクラスが渡された場合、それが ``__init__`` メソッドに何も渡さずに構築されます。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.type:1
msgid "The option type associated with this transformer."
msgstr "このトランスフォーマーに関連付けられたオプションタイプ。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.type:3
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.channel_types:5
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.min_value:6
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.max_value:6
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.choices:6
msgid "This must be a :obj:`property`."
msgstr "これは :obj:`property` でなければなりません。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.type:5
msgid "Defaults to :attr:`~discord.AppCommandOptionType.string`."
msgstr "デフォルトは :attr:`~discord.AppCommandOptionType.string` です。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.channel_types:1
msgid "A list of channel types that are allowed to this parameter."
msgstr "このパラメータにて利用できるチャンネルの種類のリスト。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.channel_types:3
msgid "Only valid if the :meth:`type` returns :attr:`~discord.AppCommandOptionType.channel`."
msgstr ":meth:`type` が :attr:`~discord.AppCommandOptionType.channel` を返す場合にのみ有効です。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.channel_types:7
msgid "Defaults to an empty list."
msgstr "既定値は空のリストです。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.min_value:3
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.max_value:3
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.choices:3
msgid "Only valid if the :meth:`type` returns :attr:`~discord.AppCommandOptionType.number` :attr:`~discord.AppCommandOptionType.integer`, or :attr:`~discord.AppCommandOptionType.string`."
msgstr ":meth:`type` が :attr:`~discord.AppCommandOptionType.number` 、 :attr:`~discord.AppCommandOptionType.integer` または :attr:`~discord.AppCommandOptionType.string` を返す場合にのみ有効です。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.min_value:8
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.max_value:8
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.choices:8
msgid "Defaults to ``None``."
msgstr "既定値は ``None`` です。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.choices:1
msgid "A list of up to 25 choices that are allowed to this parameter."
msgstr "このパラメータにて利用できる最大25個の選択肢のリスト。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.Transformer.choices:10
msgid "Optional[List[:class:`~discord.app_commands.Choice`]]"
msgstr "Optional[List[:class:`~discord.app_commands.Choice`]]"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.transform:1
msgid "|maybecoro|"
msgstr "|maybecoro|"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.transform:3
msgid "Transforms the converted option value into another value."
msgstr "解決されたオプション値を別の値に変換します。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.transform:5
msgid "The value passed into this transform function is the same as the one in the :class:`conversion table <discord.app_commands.Namespace>`."
msgstr "この変換関数に渡される値は、 :class:`変換対応表 <discord.app_commands.Namespace>` の値と同じです。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.transform:8
msgid "The interaction being handled."
msgstr "処理中のインタラクション。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.transform:10
msgid "The value of the given argument after being resolved. See the :class:`conversion table <discord.app_commands.Namespace>` for how certain option types correspond to certain values."
msgstr "解決された引数値。どのオプションの種類がどの値に対応するかについては :class:`変換対応表 <discord.app_commands.Namespace>` を参照してください。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.autocomplete:3
msgid "An autocomplete prompt handler to be automatically used by options using this transformer."
msgstr "このトランスフォーマーを使用するオプションによって自動的に使用されるオートコンプリートプロンプトのハンドラ。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.autocomplete:7
msgid "Autocomplete is only supported for options with a :meth:`~discord.app_commands.Transformer.type` of :attr:`~discord.AppCommandOptionType.string`, :attr:`~discord.AppCommandOptionType.integer`, or :attr:`~discord.AppCommandOptionType.number`."
msgstr "オートコンプリートは :meth:`~discord.app_commands.Transformer.type` が :attr:`~discord.AppCommandOptionType.string` 、 :attr:`~discord.AppCommandOptionType.integer` または :attr:`~discord.AppCommandOptionType.number` であるオプションでのみサポートされています。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.autocomplete:11
msgid "The autocomplete interaction being handled."
msgstr "処理中のオートコンプリートのインタラクション。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.autocomplete:13
msgid "The current value entered by the user."
msgstr "ユーザーが入力した現在の値。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transformer.autocomplete:16
msgid "A list of choices to be displayed to the user, a maximum of 25."
msgstr "ユーザーに表示される選択肢のリスト、最大25個です。"

#: ../../interactions/api.rst:684
msgid "Transform"
msgstr "Transform"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transform:1
msgid "A type annotation that can be applied to a parameter to customise the behaviour of an option type by transforming with the given :class:`Transformer`. This requires the usage of two generic parameters, the first one is the type you're converting to and the second one is the type of the :class:`Transformer` actually doing the transformation."
msgstr "パラメータに適用して与えられた特定の種類のオプションを :class:`Transformer` で変換するようその動作を変更できる型アノテーション。これを使用するときには二つのジェネリックパラメータが必要で、一つ目には変換先の型を、二つ目には実際に変換を行う :class:`Transformer` の型を指定しないといけません。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transform:6
#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Range:4
msgid "During type checking time this is equivalent to :obj:`typing.Annotated` so type checkers understand the intent of the code."
msgstr "型チェック時には型チェッカがコードの意図を理解できるよう :obj:`typing.Annotated` と同様になります。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Transform:9
msgid "For example usage, check :class:`Transformer`."
msgstr "使用例は、 :class:`Transformer` を確認してください。"

#: ../../interactions/api.rst:692
msgid "Range"
msgstr "Range"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Range:1
msgid "A type annotation that can be applied to a parameter to require a numeric or string type to fit within the range provided."
msgstr "与えられた範囲内に収まる数値または文字列型を必要とするパラメータに適用できる型アノテーション。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Range:7
msgid "Some example ranges:"
msgstr "範囲の例:"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Range:9
msgid "``Range[int, 10]`` means the minimum is 10 with no maximum."
msgstr "``Range[int, 10]`` は最小値10、最大値なしを意味します。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Range:10
msgid "``Range[int, None, 10]`` means the maximum is 10 with no minimum."
msgstr "``Range[int, None, 10]`` は最小値なし、最大値10を意味します。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Range:11
msgid "``Range[int, 1, 10]`` means the minimum is 1 and the maximum is 10."
msgstr "``Range[int, 1, 10]`` は最小値1、最大値10を意味します。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Range:12
msgid "``Range[float, 1.0, 5.0]`` means the minimum is 1.0 and the maximum is 5.0."
msgstr "``Range[float, 1.0, 5.0]`` は最小値1.0、最大値5.0を意味します。"

#: ../../../discord/app_commands/transformers.py:docstring of discord.app_commands.transformers.Range:13
msgid "``Range[str, 1, 10]`` means the minimum length is 1 and the maximum length is 10."
msgstr "``Range[str, 1, 10]`` は最小1文字、最大10文字を意味します。"

#: ../../interactions/api.rst:700
msgid "Translations"
msgstr "翻訳"

#: ../../interactions/api.rst:703
msgid "Translator"
msgstr "Translator"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:1
msgid "A class that handles translations for commands, parameters, and choices."
msgstr "コマンド、パラメータ、および選択肢の翻訳を処理するクラス。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:3
msgid "Translations are done lazily in order to allow for async enabled translations as well as supporting a wide array of translation systems such as :mod:`gettext` and `Project Fluent <https://projectfluent.org>`_."
msgstr "非同期対応の翻訳を可能にし、また :mod:`gettext` や `Project Fluent <https://projectfluent.org>`_ のような幅広い翻訳システムをサポートするために、翻訳は遅延して行われます。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:7
msgid "In order for a translator to be used, it must be set using the :meth:`CommandTree.set_translator` method. The translation flow for a string is as follows:"
msgstr "トランスレータを使用するには、 :meth:`CommandTree.set_translator` メソッドを使用して設定する必要があります。文字列の翻訳フローは次のとおりです。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:11
msgid "Use :class:`locale_str` instead of :class:`str` in areas of a command you want to be translated."
msgstr "翻訳したいコマンドで :class:`str` の代わりに :class:`locale_str` を使用します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:11
msgid "Currently, these are command names, command descriptions, parameter names, parameter descriptions, and choice names."
msgstr "現在、これらはコマンド名、コマンド説明、パラメータ名、パラメータ説明、選択肢名です。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:12
msgid "This can also be used inside the :func:`~discord.app_commands.describe` decorator."
msgstr "これは :func:`~discord.app_commands.describe` デコレータ内でも使用できます。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:13
msgid "Call :meth:`CommandTree.set_translator` to the translator instance that will handle the translations."
msgstr "翻訳を処理するトランスレータインスタンスに対し :meth:`CommandTree.set_translator` を呼び出します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:14
msgid "Call :meth:`CommandTree.sync`"
msgstr ":meth:`CommandTree.sync` を呼び出します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator:15
msgid "The library will call :meth:`Translator.translate` on all the relevant strings being translated."
msgstr "ライブラリは翻訳可能なすべての対応する文字列に対し :meth:`Translator.translate` を呼び出します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.load:3
msgid "An asynchronous setup function for loading the translation system."
msgstr "翻訳システムを読み込むための非同期セットアップ関数。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.load:7
msgid "This is invoked when :meth:`CommandTree.set_translator` is called."
msgstr ":meth:`CommandTree.set_translator` が呼び出されたときに呼び出されます。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.unload:3
msgid "An asynchronous teardown function for unloading the translation system."
msgstr "翻訳システムをアンロードするための非同期関数。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.unload:7
msgid "This is invoked when :meth:`CommandTree.set_translator` is called if a tree already has a translator or when :meth:`discord.Client.close` is called."
msgstr "これは、すでにツリーにトランスレータが存在する状態で :meth:`CommandTree.set_translator` を呼び出すか、 :meth:`discord.Client.close` が呼び出されたときに呼び出されます。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.translate:3
msgid "Translates the given string to the specified locale."
msgstr "指定した文字列を指定したロケールに翻訳します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.translate:5
msgid "If the string cannot be translated, ``None`` should be returned."
msgstr "文字列を翻訳できない場合は、 ``None`` を返すべきです。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.translate:7
msgid "The default implementation returns ``None``."
msgstr "デフォルトの実装では ``None`` を返します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.translate:9
msgid "If an exception is raised in this method, it should inherit from :exc:`TranslationError`. If it doesn't, then when this is called the exception will be chained with it instead."
msgstr "このメソッドで送出される例外は、 :exc:`TranslationError` から継承すべきです。 そうでない場合は、これが呼び出されたときに例外が代わりにチェーンされます。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.translate:12
msgid "The string being translated."
msgstr "翻訳すべき文字列。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.translate:14
msgid "The locale being requested for translation."
msgstr "翻訳先のローケル。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.Translator.translate:16
msgid "The translation context where the string originated from. For better type checking ergonomics, the ``TranslationContextTypes`` type can be used instead to aid with type narrowing. It is functionally equivalent to :class:`TranslationContext`."
msgstr "文字列を翻訳するときの文脈。より良い型チェックのために、 ``TranslationContextTypes`` 型を使用して型の絞り込みを行うことができます。これは :class:`TranslationContext` と機能的に同じです。"

#: ../../interactions/api.rst:711
msgid "locale_str"
msgstr "locale_str"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:1
msgid "Marks a string as ready for translation."
msgstr "文字列を翻訳できるものとマークします。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:3
msgid "This is done lazily and is not actually translated until :meth:`CommandTree.sync` is called."
msgstr "これは遅延して行われ、実際には :meth:`CommandTree.sync` が呼び出されるまで翻訳されません。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:5
msgid "The sync method then ultimately defers the responsibility of translating to the :class:`Translator` instance used by the :class:`CommandTree`. For more information on the translation flow, see the :class:`Translator` documentation."
msgstr "syncメソッドは、翻訳を :class:`CommandTree` が使用する :class:`Translator` インスタンスに委任します。翻訳フローに関する詳細に関しては、 :class:`Translator` のドキュメントを参照してください。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:13
msgid "Returns the message passed to the string."
msgstr "文字列に渡されたメッセージを返します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:17
msgid "Checks if the string is equal to another string."
msgstr "文字列が他の文字列と等しいか確認します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:21
msgid "Checks if the string is not equal to another string."
msgstr "文字列が他の文字列と等しくないか確認します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:25
msgid "Returns the hash of the string."
msgstr "文字列の ハッシュを返します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:31
msgid "The message being translated. Once set, this cannot be changed."
msgstr "翻訳すべきメッセージ。一度設定したら変更できません。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:35
msgid "This must be the default \"message\" that you send to Discord. Discord sends this message back to the library and the library uses it to access the data in order to dispatch commands."
msgstr "これはDiscordに送信するデフォルトの「メッセージ」でなければなりません。 Discordはこのメッセージをライブラリに送信し、ライブラリはコマンドを実行するためのデータにアクセスするために使用します。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:39
msgid "For example, in a command name context, if the command name is ``foo`` then the message *must* also be ``foo``. For other translation systems that require a message ID such as Fluent, consider using a keyword argument to pass it in."
msgstr "例えば、コマンド名の文脈において、コマンド名が ``foo`` の場合、メッセージは ``foo`` でないと **いけません** 。 FluentのようなメッセージIDが必要な他の翻訳システムでは、キーワード引数を使用して渡すことを検討してください。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:48
msgid "A dict of user provided extras to attach to the translated string. This can be used to add more context, information, or any metadata necessary to aid in actually translating the string."
msgstr "翻訳する文字列についての追加のデータを保管できる辞書型。これは、文字列の実際の翻訳に必要な文脈、情報、その他メタデータを追加するのに利用できます。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.locale_str:52
msgid "Since these are passed via keyword arguments, the keys are strings."
msgstr "これらはキーワード引数で渡されるので、キーは文字列です。"

#: ../../interactions/api.rst:719
msgid "TranslationContext"
msgstr "TranslationContext"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.TranslationContext:1
msgid "A class that provides context for the :class:`locale_str` being translated."
msgstr "翻訳される :class:`locale_str` の文脈を提供するクラス。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.TranslationContext:3
msgid "This is useful to determine where exactly the string is located and aid in looking up the actual translation."
msgstr "これは、文字列がどこにあるかを正確に特定し、実際の翻訳を取得するのに役立ちます。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.TranslationContext:8
msgid "The location where this string is located."
msgstr "この文字列が存在する場所。"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.TranslationContext:10
msgid ":class:`TranslationContextLocation`"
msgstr ":class:`TranslationContextLocation`"

#: ../../../discord/app_commands/translator.py:docstring of discord.app_commands.translator.TranslationContext:14
msgid "The extraneous data that is being translated."
msgstr "翻訳されている追加のデータ。"

#: ../../interactions/api.rst:727
msgid "TranslationContextLocation"
msgstr "TranslationContextLocation"

#: ../../interactions/api.rst:732
msgid "An enum representing the location context that the translation occurs in when requested for translation."
msgstr "翻訳が要求されたときに、翻訳すべき場所を示す列挙型。"

#: ../../interactions/api.rst:738
msgid "The translation involved a command name."
msgstr "コマンド名の翻訳。"

#: ../../interactions/api.rst:741
msgid "The translation involved a command description."
msgstr "コマンドの説明の翻訳。"

#: ../../interactions/api.rst:745
msgid "The translation involved a group name."
msgstr "グループ名の翻訳。"

#: ../../interactions/api.rst:748
msgid "The translation involved a group description."
msgstr "グループの説明の翻訳。"

#: ../../interactions/api.rst:751
msgid "The translation involved a parameter name."
msgstr "パラメータ名の翻訳。"

#: ../../interactions/api.rst:754
msgid "The translation involved a parameter description."
msgstr "パラメータの説明の翻訳。"

#: ../../interactions/api.rst:757
msgid "The translation involved a choice name."
msgstr "選択肢名の翻訳。"

#: ../../interactions/api.rst:760
msgid "The translation involved something else entirely. This is useful for running :meth:`Translator.translate` for custom usage."
msgstr "その他の翻訳。これは他の目的で :meth:`Translator.translate` を実行するときに役立ちます。"

#: ../../interactions/api.rst:764
msgid "Exceptions"
msgstr "例外"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.AppCommandError:1
msgid "The base exception type for all application command related errors."
msgstr "アプリケーションコマンドに関連するエラーすべての基礎となる例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.AppCommandError:3
msgid "This inherits from :exc:`discord.DiscordException`."
msgstr "これは :exc:`discord.DiscordException` を継承しています。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.AppCommandError:5
msgid "This exception and exceptions inherited from it are handled in a special way as they are caught and passed into various error handlers in this order:"
msgstr "この例外及び、ここから継承された例外は、以下の順で捕捉され様々なエラーハンドラに渡されるなど、特別な方法で処理されます。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.AppCommandError:9
msgid ":meth:`Command.error <discord.app_commands.Command.error>`"
msgstr ":meth:`Command.error <discord.app_commands.Command.error>`"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.AppCommandError:10
msgid ":meth:`Group.on_error <discord.app_commands.Group.on_error>`"
msgstr ":meth:`Group.on_error <discord.app_commands.Group.on_error>`"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.AppCommandError:11
msgid ":meth:`CommandTree.on_error <discord.app_commands.CommandTree.on_error>`"
msgstr ":meth:`CommandTree.on_error <discord.app_commands.CommandTree.on_error>`"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandInvokeError:1
msgid "An exception raised when the command being invoked raised an exception."
msgstr "呼び出そうとしたコマンドが例外を送出した場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandInvokeError:3
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:4
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:3
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CheckFailure:3
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandLimitReached:4
msgid "This inherits from :exc:`~discord.app_commands.AppCommandError`."
msgstr ":exc:`~discord.app_commands.AppCommandError` を継承します。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandInvokeError:9
msgid "The original exception that was raised. You can also get this via the ``__cause__`` attribute."
msgstr "送出された元の例外。 ``__cause__`` 属性からも取得できます。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandInvokeError:12
msgid ":exc:`Exception`"
msgstr ":exc:`Exception`"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandInvokeError:16
msgid "The command that failed."
msgstr "失敗したコマンド。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandInvokeError:18
msgid "Union[:class:`Command`, :class:`ContextMenu`]"
msgstr "Union[:class:`Command`, :class:`ContextMenu`]"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:1
msgid "An exception raised when a :class:`Transformer` or type annotation fails to convert to its target type."
msgstr ":class:`Transformer` または型アノテーションがターゲット型に変換できない場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:6
msgid "If an exception occurs while converting that does not subclass :exc:`AppCommandError` then the exception is wrapped into this exception. The original exception can be retrieved using the ``__cause__`` attribute. Otherwise if the exception derives from :exc:`AppCommandError` then it will be propagated as-is."
msgstr "もし :exc:`AppCommandError` を継承しない例外が変換中に送出された場合その例外はこれに包まれます。元の例外は ``__cause__`` 属性から取得できます。もし例外が :exc:`AppCommandError` から派生したものであればそのまま伝播されます。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:16
msgid "The value that failed to convert."
msgstr "変換に失敗した値。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:22
msgid "The type of argument that failed to convert."
msgstr "変換に失敗した引数の型。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:28
msgid "The transformer that failed the conversion."
msgstr "変換に失敗したトランスフォーマー。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TransformerError:30
msgid ":class:`Transformer`"
msgstr ":class:`Transformer`"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:1
msgid "An exception raised when the library fails to translate a string."
msgstr "ライブラリが文字列の翻訳に失敗したときに発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:5
msgid "If an exception occurs while calling :meth:`Translator.translate` that does not subclass this then the exception is wrapped into this exception. The original exception can be retrieved using the ``__cause__`` attribute. Otherwise it will be propagated as-is."
msgstr "もしこれを継承しない例外が :meth:`Translator.translate` の実行中に送出された場合その例外はこれに包まれます。元の例外は ``__cause__`` 属性から取得できます。そうでなければ、例外はそのまま伝播されます。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:14
msgid "The string that caused the error, if any."
msgstr "存在する場合、エラーの原因となった文字列。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:16
msgid "Optional[Union[:class:`str`, :class:`locale_str`]]"
msgstr "Optional[Union[:class:`str`, :class:`locale_str`]]"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:20
msgid "The locale that caused the error, if any."
msgstr "存在する場合、エラーの原因となったローケル。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:22
msgid "Optional[:class:`~discord.Locale`]"
msgstr "Optional[:class:`~discord.Locale`]"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:26
msgid "The context of the translation that triggered the error."
msgstr "エラーを引き起こした翻訳の文脈。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.TranslationError:28
msgid ":class:`~discord.app_commands.TranslationContext`"
msgstr ":class:`~discord.app_commands.TranslationContext`"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CheckFailure:1
msgid "An exception raised when check predicates in a command have failed."
msgstr "コマンドのチェック関数が失敗した場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.NoPrivateMessage:1
msgid "An exception raised when a command does not work in a direct message."
msgstr "コマンドがダイレクトメッセージで動作しない場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.NoPrivateMessage:3
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingRole:3
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingAnyRole:4
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingPermissions:4
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.BotMissingPermissions:4
msgid "This inherits from :exc:`~discord.app_commands.CheckFailure`."
msgstr ":exc:`~discord.app_commands.CheckFailure` を継承します。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingRole:1
msgid "An exception raised when the command invoker lacks a role to run a command."
msgstr "コマンド実行者がコマンドを実行するためのロールを持っていない場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingRole:9
msgid "The required role that is missing. This is the parameter passed to :func:`~discord.app_commands.checks.has_role`."
msgstr "見つからなかった必須のロール。これは :func:`~discord.app_commands.checks.has_role` に渡されたパラメータと同一です。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingRole:12
msgid "Union[:class:`str`, :class:`int`]"
msgstr "Union[:class:`str`, :class:`int`]"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingAnyRole:1
msgid "An exception raised when the command invoker lacks any of the roles specified to run a command."
msgstr "コマンド実行者がコマンドを実行するためのロールをどれも持っていない場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingAnyRole:10
msgid "The roles that the invoker is missing. These are the parameters passed to :func:`~discord.app_commands.checks.has_any_role`."
msgstr "見つからなかったロール。これは :func:`~discord.app_commands.checks.has_any_role` に渡されたパラメータと同一です。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingAnyRole:13
msgid "List[Union[:class:`str`, :class:`int`]]"
msgstr "List[Union[:class:`str`, :class:`int`]]"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingPermissions:1
msgid "An exception raised when the command invoker lacks permissions to run a command."
msgstr "コマンド実行者がコマンドを実行する権限を持っていない場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingPermissions:10
#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.BotMissingPermissions:10
msgid "The required permissions that are missing."
msgstr "有していない必要な権限。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.BotMissingPermissions:1
msgid "An exception raised when the bot's member lacks permissions to run a command."
msgstr "ボットのメンバーがコマンドを実行する権限を持っていない場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandOnCooldown:1
msgid "An exception raised when the command being invoked is on cooldown."
msgstr "呼び出そうとしたコマンドがクールダウン中の場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandOnCooldown:9
msgid "The cooldown that was triggered."
msgstr "トリガーされたクールダウン。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandOnCooldown:11
msgid ":class:`~discord.app_commands.Cooldown`"
msgstr ":class:`~discord.app_commands.Cooldown`"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandOnCooldown:15
msgid "The amount of seconds to wait before you can retry again."
msgstr "再試行する前に待たないといけない秒数。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandLimitReached:1
msgid "An exception raised when the maximum number of application commands was reached either globally or in a guild."
msgstr "グローバルまたはギルド内でアプリケーションコマンドの最大登録数に達したときに発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandLimitReached:10
msgid "The type of command that reached the limit."
msgstr "制限に達したコマンドの種類。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandLimitReached:16
msgid "The guild ID that reached the limit or ``None`` if it was global."
msgstr "制限に達したギルドのIDか、グローバルの場合 ``None`` 。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandLimitReached:22
msgid "The limit that was hit."
msgstr "達した制限。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandAlreadyRegistered:1
msgid "An exception raised when a command is already registered."
msgstr "コマンドがすでに登録されている場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandAlreadyRegistered:9
msgid "The name of the command already registered."
msgstr "すでに登録されているコマンドの名前。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandAlreadyRegistered:15
msgid "The guild ID this command was already registered at. If ``None`` then it was a global command."
msgstr "コマンドがすでに登録されているギルドのID。グローバルコマンドの場合 ``None`` 。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandSignatureMismatch:1
msgid "An exception raised when an application command from Discord has a different signature from the one provided in the code. This happens because your command definition differs from the command definition you provided Discord. Either your code is out of date or the data from Discord is out of sync."
msgstr "Discordからのアプリケーションコマンドがコード内のものとは異なるシグネチャを持つ場合に発生する例外。 これは、Discordに渡したコマンド定義と現在のコマンド定義が異なるときに発生します。 つまり、あなたのコードが古いものであるか、Discordのデータが同期されていません。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandSignatureMismatch:12
msgid "The command that had the signature mismatch."
msgstr "シグネチャが一致しないコマンド。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandSignatureMismatch:14
msgid "Union[:class:`~.app_commands.Command`, :class:`~.app_commands.ContextMenu`, :class:`~.app_commands.Group`]"
msgstr "Union[:class:`~.app_commands.Command`, :class:`~.app_commands.ContextMenu`, :class:`~.app_commands.Group`]"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandNotFound:1
msgid "An exception raised when an application command could not be found."
msgstr "アプリケーションコマンドが見つからなかった場合に発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandNotFound:9
msgid "The name of the application command not found."
msgstr "見つからなかったアプリケーションコマンドの名前。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandNotFound:15
msgid "A list of parent command names that were previously found prior to the application command not being found."
msgstr "見つからなかったアプリケーションコマンドの前に見つかった親コマンド名のリスト。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandNotFound:22
msgid "The type of command that was not found."
msgstr "見つからなかったコマンドの種類。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.MissingApplicationID:1
msgid "An exception raised when the client does not have an application ID set. An application ID is required for syncing application commands."
msgstr "クライアントにアプリケーションIDが設定されていない場合に発生する例外。アプリケーションコマンドの同期にはアプリケーションIDが必要です。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandSyncFailure:1
msgid "An exception raised when :meth:`CommandTree.sync` failed."
msgstr ":meth:`CommandTree.sync` が失敗したときに発生する例外。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandSyncFailure:3
msgid "This provides syncing failures in a slightly more readable format."
msgstr "これにより、同期の失敗に関する情報が少し読みやすい形式で提供されます。"

#: ../../../discord/app_commands/errors.py:docstring of discord.app_commands.errors.CommandSyncFailure:5
msgid "This inherits from :exc:`~discord.app_commands.AppCommandError` and :exc:`~discord.HTTPException`."
msgstr "これは :exc:`~discord.app_commands.AppCommandError` と :exc:`~discord.HTTPException` を継承します。"

#: ../../interactions/api.rst:818
msgid "Exception Hierarchy"
msgstr "例外の階層構造"

#: ../../interactions/api.rst:840
msgid ":exc:`~discord.DiscordException`"
msgstr ":exc:`~discord.DiscordException`"

#: ../../interactions/api.rst:838
msgid ":exc:`~discord.app_commands.AppCommandError`"
msgstr ":exc:`~discord.app_commands.AppCommandError`"

#: ../../interactions/api.rst:824
msgid ":exc:`~discord.app_commands.CommandInvokeError`"
msgstr ":exc:`~discord.app_commands.CommandInvokeError`"

#: ../../interactions/api.rst:825
msgid ":exc:`~discord.app_commands.TransformerError`"
msgstr ":exc:`~discord.app_commands.TransformerError`"

#: ../../interactions/api.rst:826
msgid ":exc:`~discord.app_commands.TranslationError`"
msgstr ":exc:`~discord.app_commands.TranslationError`"

#: ../../interactions/api.rst:832
msgid ":exc:`~discord.app_commands.CheckFailure`"
msgstr ":exc:`~discord.app_commands.CheckFailure`"

#: ../../interactions/api.rst:828
msgid ":exc:`~discord.app_commands.NoPrivateMessage`"
msgstr ":exc:`~discord.app_commands.NoPrivateMessage`"

#: ../../interactions/api.rst:829
msgid ":exc:`~discord.app_commands.MissingRole`"
msgstr ":exc:`~discord.app_commands.MissingRole`"

#: ../../interactions/api.rst:830
msgid ":exc:`~discord.app_commands.MissingAnyRole`"
msgstr ":exc:`~discord.app_commands.MissingAnyRole`"

#: ../../interactions/api.rst:831
msgid ":exc:`~discord.app_commands.MissingPermissions`"
msgstr ":exc:`~discord.app_commands.MissingPermissions`"

#: ../../interactions/api.rst:832
msgid ":exc:`~discord.app_commands.BotMissingPermissions`"
msgstr ":exc:`~discord.app_commands.BotMissingPermissions`"

#: ../../interactions/api.rst:833
msgid ":exc:`~discord.app_commands.CommandOnCooldown`"
msgstr ":exc:`~discord.app_commands.CommandOnCooldown`"

#: ../../interactions/api.rst:834
msgid ":exc:`~discord.app_commands.CommandLimitReached`"
msgstr ":exc:`~discord.app_commands.CommandLimitReached`"

#: ../../interactions/api.rst:835
msgid ":exc:`~discord.app_commands.CommandAlreadyRegistered`"
msgstr ":exc:`~discord.app_commands.CommandAlreadyRegistered`"

#: ../../interactions/api.rst:836
msgid ":exc:`~discord.app_commands.CommandSignatureMismatch`"
msgstr ":exc:`~discord.app_commands.CommandSignatureMismatch`"

#: ../../interactions/api.rst:837
msgid ":exc:`~discord.app_commands.CommandNotFound`"
msgstr ":exc:`~discord.app_commands.CommandNotFound`"

#: ../../interactions/api.rst:838
msgid ":exc:`~discord.app_commands.MissingApplicationID`"
msgstr ":exc:`~discord.app_commands.MissingApplicationID`"

#: ../../interactions/api.rst:839
#: ../../interactions/api.rst:841
msgid ":exc:`~discord.app_commands.CommandSyncFailure`"
msgstr ":exc:`~discord.app_commands.CommandSyncFailure`"

#: ../../interactions/api.rst:840
msgid ":exc:`~discord.HTTPException`"
msgstr ":exc:`~discord.HTTPException`"

