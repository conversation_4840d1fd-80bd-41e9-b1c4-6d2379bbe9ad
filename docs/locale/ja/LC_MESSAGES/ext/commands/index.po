msgid ""
msgstr ""
"Project-Id-Version: discordpy\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-21 01:17+0000\n"
"PO-Revision-Date: 2023-06-21 01:20\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: discordpy\n"
"X-Crowdin-Project-ID: 362783\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: /ext/commands/index.pot\n"
"X-Crowdin-File-ID: 66\n"
"Language: ja_JP\n"

#: ../../ext/commands/index.rst:4
msgid "``discord.ext.commands`` -- Bot commands framework"
msgstr "``discord.ext.commands`` -- ボットコマンドのフレームワーク"

#: ../../ext/commands/index.rst:6
msgid "``discord.py`` offers a lower level aspect on interacting with Discord. Often times, the library is used for the creation of bots. However this task can be daunting and confusing to get correctly the first time. Many times there comes a repetition in creating a bot command framework that is extensible, flexible, and powerful. For this reason, ``discord.py`` comes with an extension library that handles this for you."
msgstr "``discord.py`` は、Discordと連携するための低レベルな機能を提供します。多くの場合、このライブラリはBotの作成に用いられています。しかしこの作業を始めから正確に行うことは困難であり、混乱することもあるでしょう。拡張性が高く柔軟、そして強力なBotコマンドフレームワークは何度も繰り返し作成することになるでしょう。この理由から、 ``discord.py`` にはこれを扱う拡張ライブラリが付属しています。"

