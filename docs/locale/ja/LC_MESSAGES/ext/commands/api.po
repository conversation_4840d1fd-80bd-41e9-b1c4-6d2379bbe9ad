msgid ""
msgstr ""
"Project-Id-Version: discordpy\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-21 01:17+0000\n"
"PO-Revision-Date: 2023-06-21 01:20\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: discordpy\n"
"X-Crowdin-Project-ID: 362783\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: /ext/commands/api.pot\n"
"X-Crowdin-File-ID: 62\n"
"Language: ja_JP\n"

#: ../../ext/commands/api.rst:4
msgid "API Reference"
msgstr "APIリファレンス"

#: ../../ext/commands/api.rst:6
msgid "The following section outlines the API of discord.py's command extension module."
msgstr "この項目ではdiscord.pyのAPIが持つコマンド拡張モジュールについて解説します。"

#: ../../ext/commands/api.rst:11
msgid "Bots"
msgstr "Bot"

#: ../../ext/commands/api.rst:14
msgid "Bot"
msgstr "Bot"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:1
msgid "Represents a Discord bot."
msgstr "Discord Botを表します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:3
msgid "This class is a subclass of :class:`discord.Client` and as a result anything that you can do with a :class:`discord.Client` you can do with this bot."
msgstr "このクラスは :class:`discord.Client` のサブクラスのため、 :class:`discord.Client` でできることと同じことをこのBotで行うことができます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:7
msgid "This class also subclasses :class:`.GroupMixin` to provide the functionality to manage commands."
msgstr "また、 :class:`.GroupMixin` も継承しており、コマンド管理の機能も使用可能です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:10
msgid "Unlike :class:`discord.Client`, this class does not require manually setting a :class:`~discord.app_commands.CommandTree` and is automatically set upon instantiating the class."
msgstr ":class:`discord.Client` とは異なり、このクラスでは :class:`~discord.app_commands.CommandTree` を手動で設定する必要はなく、クラスのインスタンスを作成する際に自動的に設定されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:18
msgid "Asynchronously initialises the bot and automatically cleans up."
msgstr "非同期的にボットを初期化し自動でクリーンアップします。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:24
msgid "The command prefix is what the message content must contain initially to have a command invoked. This prefix could either be a string to indicate what the prefix should be, or a callable that takes in the bot as its first parameter and :class:`discord.Message` as its second parameter and returns the prefix. This is to facilitate \"dynamic\" command prefixes. This callable can be either a regular function or a coroutine."
msgstr "コマンドの接頭詞とは、コマンドの判定のためにメッセージの先頭に付けなければならないものです。接頭詞には、そのまま接頭詞として使用する文字列、または :class:`discord.Message` を二つ目の引数として受け取り、接頭詞を返す呼び出し可能な関数を渡すことができます。これは「動的な」接頭詞の実装を容易にするためです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:32
msgid "An empty string as the prefix always matches, enabling prefix-less command invocation. While this may be useful in DMs it should be avoided in servers, as it's likely to cause performance issues and unintended command invocations."
msgstr "接頭詞に空文字列を渡せば、接頭詞なしでコマンドの呼び出しができます。これはDM上では有用ですが、サーバーでは意図せずコマンドを呼び出してしまうことに繋がるため、避けるべきです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:37
msgid "The command prefix could also be an iterable of strings indicating that multiple checks for the prefix should be used and the first one to match will be the invocation prefix. You can get this prefix via :attr:`.Context.prefix`."
msgstr "コマンドの接頭辞を文字列のイテラブルで指定することもできます。この場合、接頭辞を複数回チェックし、最初にマッチしたものを呼び出し時の接頭辞とします。使用された接頭辞は、 :attr:`.Context.prefix` で取得することができます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:44
msgid "When passing multiple prefixes be careful to not pass a prefix that matches a longer prefix occurring later in the sequence.  For example, if the command prefix is ``('!', '!?')``  the ``'!?'`` prefix will never be matched to any message as the previous one matches messages starting with ``!?``. This is especially important when passing an empty string, it should always be last as no prefix after it will be matched."
msgstr "複数の接頭辞を渡すとき、後の接頭辞にマッチする接頭辞を、それよりも前に渡さないよう注意してください。たとえば、接頭辞が ``('!', '!?')`` のとき、 ``!?`` の接頭辞は、その前のものが ``!?`` で始まるメッセージにマッチするため、どのメッセージにも反応しません。これは空文字列を渡すときは特に重要で、その後の接頭辞は無視されるため、空文字列は最後に置かないといけません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:54
msgid "Whether the commands should be case insensitive. Defaults to ``False``. This attribute does not carry over to groups. You must set it to every group if you require group commands to be case insensitive as well."
msgstr "コマンド名で大文字と小文字を区別するかどうか。デフォルトでは\\ ``False``\\ です。この属性はグループに適用されません。もしグループコマンドも大文字と小文字を区別したくない場合、すべてのグループで設定しなければなりません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:0
msgid "type"
msgstr "型"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:58
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:102
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.is_owner:21
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:49
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:86
msgid ":class:`bool`"
msgstr ":class:`bool`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:62
msgid "The content prefixed into the default help message."
msgstr "デフォルトのヘルプメッセージの最初に表示される文字列。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:64
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:10
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:79
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.full_parent_name:6
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.qualified_name:7
msgid ":class:`str`"
msgstr ":class:`str`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:68
msgid "The help command implementation to use. This can be dynamically set at runtime. To remove the help command pass ``None``. For more information on implementing a help command, see :ref:`ext_commands_help_command`."
msgstr "使用するヘルプコマンドの実装。これは、実行時でも動的に設定できます。ヘルプコマンドを削除するには、``None`` を引数に入れてください。ヘルプ コマンドの実装の詳細については、 :ref:`ext_commands_help_command` を参照してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:72
msgid "Optional[:class:`.HelpCommand`]"
msgstr "Optional[:class:`.HelpCommand`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:76
msgid "The user ID that owns the bot. If this is not set and is then queried via :meth:`.is_owner` then it is fetched automatically using :meth:`~.Bot.application_info`."
msgstr "Botを所有するユーザーのID。 もし設定されていない場合、 :meth:`.is_owner` が呼び出されたとき、 :meth:`~.Bot.application_info` を用いて自動的に取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:80
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application_id:10
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:36
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:41
msgid "Optional[:class:`int`]"
msgstr "Optional[:class:`int`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:84
msgid "The user IDs that owns the bot. This is similar to :attr:`owner_id`. If this is not set and the application is team based, then it is fetched automatically using :meth:`~.Bot.application_info`. For performance reasons it is recommended to use a :class:`set` for the collection. You cannot set both ``owner_id`` and ``owner_ids``."
msgstr "Botを所有するユーザーのID群。これは :attr:`owner_id` と同様のものです。もしこの変数が設定されておらず、Botのアプリケーションがチームに帰属する場合、自動的に :meth:`~.Bot.application_info` から取得されます。パフォーマンス上の問題により、このID群を操作するにあたって :class:`set` を使うことが推奨されています。 ``owner_id`` と ``owner_ids`` は、どちらか片方しか設定することができません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:92
msgid "Optional[Collection[:class:`int`]]"
msgstr "Optional[Collection[:class:`int`]]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:96
msgid "Whether to strip whitespace characters after encountering the command prefix. This allows for ``!   hello`` and ``!hello`` to both work if the ``command_prefix`` is set to ``!``. Defaults to ``False``."
msgstr "接頭辞の後の空白を除去するかどうか。 ``command_prefix`` が ``!`` に設定されているときに、これが有効だと ``! hello`` 、 ``!hello`` 両方が動作するようになります。 デフォルトは ``False`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:106
msgid "The type of application command tree to use. Defaults to :class:`~discord.app_commands.CommandTree`."
msgstr "使用するアプリケーションコマンドツリーの型を指定します。デフォルトは :class:`~discord.app_commands.CommandTree` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.Bot:110
msgid "Type[:class:`~discord.app_commands.CommandTree`]"
msgstr "Type[:class:`~discord.app_commands.CommandTree`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:1
msgid "A decorator that registers a coroutine as a post-invoke hook."
msgstr "コルーチンを、実行後に呼び出すフックとして登録するデコレータ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:3
msgid "A post-invoke hook is called directly after the command is called. This makes it a useful function to clean-up database connections or any type of clean up required."
msgstr "実行後呼び出しフックは、コマンドが呼び出された直後に呼び出されます。 これにより、データベース接続をクリーンアップしたり、必要なクリーンアップを行うための便利な機能になります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:7
msgid "This post-invoke hook takes a sole parameter, a :class:`.Context`."
msgstr "この実行後呼び出しフックは、 :class:`.Context` を単独の引数として取ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:11
msgid "Similar to :meth:`~.Bot.before_invoke`\\, this is not called unless checks and argument parsing procedures succeed. This hook is, however, **always** called regardless of the internal command callback raising an error (i.e. :exc:`.CommandInvokeError`\\). This makes it ideal for clean-up scenarios."
msgstr ":meth:`~.Bot.before_invoke`\\と似て、チェックと引数展開が成功しない限り、これが呼び出されることはありません。ただし、このフックは、コマンドのエラー発生にかかわらず(例えば、:exc:`.CommandInvokeError`\\など)、**常時** 呼び出されます。そのため、クリーンアップするのに最適です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:19
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:18
#: ../../../discord/client.py:docstring of discord.client.Client.event:17
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:13
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:13
msgid "``coro`` parameter is now positional-only."
msgstr "``coro`` パラメータが位置指定のみになりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_check:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_command:0
msgid "Parameters"
msgstr "パラメータ"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:21
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:15
msgid "The coroutine to register as the post-invoke hook."
msgstr "実行後呼び出しフックとして登録するコルーチン。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:0
#: ../../../discord/client.py:docstring of discord.client.Client.event:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.listen:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:0
msgid "Raises"
msgstr "例外"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.after_invoke:24
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:23
#: ../../../discord/client.py:docstring of discord.client.Client.event:19
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:18
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:18
msgid "The coroutine passed is not actually a coroutine."
msgstr "渡された関数がコルーチンではない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:1
msgid "A decorator that registers a coroutine as a pre-invoke hook."
msgstr "渡されたコルーチンを、実行前呼び出しフックとして登録するデコレータ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:3
msgid "A pre-invoke hook is called directly before the command is called. This makes it a useful function to set up database connections or any type of set up required."
msgstr "実行前呼び出しフックは、コマンドが呼び出される前に呼び出されます。 これにより、データベース接続のセットアップや、必要なセットアップを行うにあたって便利です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:7
msgid "This pre-invoke hook takes a sole parameter, a :class:`.Context`."
msgstr "この実行前呼び出しフックは、 :class:`.Context` を単独の引数として取ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:11
msgid "The :meth:`~.Bot.before_invoke` and :meth:`~.Bot.after_invoke` hooks are only called if all checks and argument parsing procedures pass without error. If any check or argument parsing procedures fail then the hooks are not called."
msgstr ":meth:`~.Bot.before_invoke` と :meth:`~.Bot.after_invoke` は、すべてのチェックと引数解析が、例外を送出せずに渡された場合にのみ呼び出されます。 何らかのチェックまたは引数の解析に失敗した場合、これらのフックは呼び出されません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.before_invoke:20
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:15
msgid "The coroutine to register as the pre-invoke hook."
msgstr "実行前呼び出しフックとして登録するコルーチン。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check:1
msgid "A decorator that adds a global check to the bot."
msgstr "ボットにグローバルチェックを追加するデコレーター。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check:3
msgid "A global check is similar to a :func:`.check` that is applied on a per command basis except it is run before any command checks have been verified and applies to every command the bot has."
msgstr "このグローバルチェックは、コマンドごとに適用される :func:`.check` と似ていますが、コマンドチェックが検証される前に実行され、かつボットが持つすべてのコマンドに適用される点で異なります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check:9
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check_once:19
msgid "This function can either be a regular function or a coroutine."
msgstr "この関数は、通常の関数かコルーチン、どちらでも成り得ます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check:11
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check_once:21
msgid "Similar to a command :func:`.check`\\, this takes a single parameter of type :class:`.Context` and can only raise exceptions inherited from :exc:`.CommandError`."
msgstr ":func:`.check` コマンドと同様、 :class:`.Context` 型の単一のパラメータを取り、 :exc:`.CommandError` から継承された例外のみを投げることができます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check:16
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check_once:26
#: ../../../discord/client.py:docstring of discord.client.Client.event:8
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.listen:8
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_listener:13
msgid "Example"
msgstr "例"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check:25
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check_once:35
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_check:8
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_listener:5
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_check:8
msgid "``func`` parameter is now positional-only."
msgstr "``func`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check_once:1
msgid "A decorator that adds a \"call once\" global check to the bot."
msgstr "ボットに「一度だけ実行される」グローバルチェックを追加するデコレーター。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check_once:3
msgid "Unlike regular global checks, this one is called only once per :meth:`.invoke` call."
msgstr "通常のグローバルチェックとは異なり、 :meth:`.invoke` の呼び出し毎に一度だけ実行されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check_once:6
msgid "Regular global checks are called whenever a command is called or :meth:`.Command.can_run` is called. This type of check bypasses that and ensures that it's called only once, even inside the default help command."
msgstr "通常のグローバルチェックは、コマンドが呼び出されるか :meth:`.Command.can_run` が呼び出されるたび、実行されます。しかしこのグローバルチェックはそれを迂回し、デフォルトのhelpコマンドの中であっても、たった一度だけ呼ばれます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.check_once:13
msgid "When using this function the :class:`.Context` sent to a group subcommand may only parse the parent command and not the subcommands due to it being invoked once per :meth:`.Bot.invoke` call."
msgstr "この関数を使用する場合、グループのサブコマンドに送信される :class:`.Context` は、 :meth:`.Bot.invoke` の呼び出しごとに一度だけ呼び出されるため、親コマンドの時にしかチェックされず、サブコマンドの時はチェックはされません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:1
msgid "A shortcut decorator that invokes :func:`~discord.ext.commands.command` and adds it to the internal command list via :meth:`~.GroupMixin.add_command`."
msgstr ":func:`~discord.ext.commands.command` を呼び出し、 :meth:`~.GroupMixin.add_command` を介して内部コマンドリストに追加するショートカットデコレータ。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:0
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_command:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_group:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.application_info:0
msgid "Returns"
msgstr "戻り値"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:4
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_command:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:4
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.command:4
msgid "A decorator that converts the provided method into a Command, adds it to the bot, then returns it."
msgstr "提供されたメソッドをCommandに変換し、Botに追加し、さらにCommandを返すデコレータ。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:0
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_command:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_group:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.application_info:0
msgid "Return type"
msgstr "戻り値の型"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.command:5
msgid "Callable[..., :class:`Command`]"
msgstr "Callable[..., :class:`Command`]"

#: ../../../discord/client.py:docstring of discord.client.Client.event:1
msgid "A decorator that registers an event to listen to."
msgstr "受け取るイベントを登録するデコレータ。"

#: ../../../discord/client.py:docstring of discord.client.Client.event:3
msgid "You can find more info about the events on the :ref:`documentation below <discord-api-events>`."
msgstr "イベントの詳細については :ref:`以下のドキュメント <discord-api-events>` を参照してください。"

#: ../../../discord/client.py:docstring of discord.client.Client.event:5
msgid "The events must be a :ref:`coroutine <coroutine>`, if not, :exc:`TypeError` is raised."
msgstr "イベントは :ref:`コルーチン <coroutine>` でなければいけません。違う場合は :exc:`TypeError` が発生します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:1
msgid "A shortcut decorator that invokes :func:`.group` and adds it to the internal command list via :meth:`~.GroupMixin.add_command`."
msgstr ":func:`.group` を呼び出し、 :meth:`~.GroupMixin.add_command` を介して内部コマンドリストに追加するショートカットデコレータ。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:4
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_group:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:4
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.group:4
msgid "A decorator that converts the provided method into a Group, adds it to the bot, then returns it."
msgstr "提供されたメソッドをGroupに変換し、Botに追加し、そしてGroupを返すデコレータ。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.group:5
msgid "Callable[..., :class:`Group`]"
msgstr "Callable[..., :class:`Group`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_command:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.command:1
msgid "A shortcut decorator that invokes :func:`~discord.ext.commands.hybrid_command` and adds it to the internal command list via :meth:`add_command`."
msgstr ":func:`~discord.ext.commands.hybrid_command` を呼び出し、 :meth:`add_command` を介して内部コマンドリストに追加するショートカットデコレータ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_command:5
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.command:5
msgid "Callable[..., :class:`HybridCommand`]"
msgstr "Callable[..., :class:`HybridCommand`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_group:1
msgid "A shortcut decorator that invokes :func:`~discord.ext.commands.hybrid_group` and adds it to the internal command list via :meth:`add_command`."
msgstr ":func:`~discord.ext.commands.hybrid_group` を呼び出し、 :meth:`add_command` を介して内部コマンドリストに追加するショートカットデコレータ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.hybrid_group:5
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.group:5
msgid "Callable[..., :class:`HybridGroup`]"
msgstr "Callable[..., :class:`HybridGroup`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.listen:1
msgid "A decorator that registers another function as an external event listener. Basically this allows you to listen to multiple events from different places e.g. such as :func:`.on_ready`"
msgstr "関数を追加のイベントリスナーとして登録するデコレータ。 基本的には異なる場所から複数のイベントを登録することに使われます（ :func:`.on_ready` など）。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.listen:5
msgid "The functions being listened to must be a :ref:`coroutine <coroutine>`."
msgstr "登録する関数は :ref:`コルーチン <coroutine>` でなければなりません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.listen:21
msgid "Would print one and two in an unspecified order."
msgstr "の、oneとtwoの出力順は保証されません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.listen:23
msgid "The function being listened to is not a coroutine."
msgstr "受け取る関数がコルーチンではない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.activity:1
msgid "The activity being used upon logging in."
msgstr "ログイン時に使用されるアクティビティ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.activity:4
msgid "Optional[:class:`.BaseActivity`]"
msgstr "Optional[:class:`.BaseActivity`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_check:1
msgid "Adds a global check to the bot."
msgstr "ボットにグローバルチェックを追加します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_check:3
msgid "This is the non-decorator interface to :meth:`.check` and :meth:`.check_once`."
msgstr "これは :meth:`.check` と :meth:`.check_once` のデコレータでない実装です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_check:10
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.add_check:11
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.add_check:11
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.add_check:11
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.add_check:9
msgid "The :func:`~discord.ext.commands.check` decorator"
msgstr ":func:`~discord.ext.commands.check` デコレータ"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_check:12
msgid "The function that was used as a global check."
msgstr "グローバルチェックとして使用される関数。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_check:13
msgid "If the function should only be called once per :meth:`.invoke` call."
msgstr "関数が :meth:`.invoke` 呼び出しの時に、一度だけ呼び出されるべきかどうか。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:1
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.application_info:1
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.before_identify_hook:1
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.change_presence:1
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.close:1
msgid "|coro|"
msgstr "|coro|"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:3
msgid "Adds a \"cog\" to the bot."
msgstr "ボットに「コグ」を追加します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:5
msgid "A cog is a class that has its own event listeners and commands."
msgstr "コグは、イベントリスナーとコマンドを持つクラスです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:7
msgid "If the cog is a :class:`.app_commands.Group` then it is added to the bot's :class:`~discord.app_commands.CommandTree` as well."
msgstr "コグが :class:`.app_commands.Group` の場合、ボットの :class:`~discord.app_commands.CommandTree` にも追加されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:12
msgid "Exceptions raised inside a :class:`.Cog`'s :meth:`~.Cog.cog_load` method will be propagated to the caller."
msgstr ":class:`.Cog` の、 :meth:`~.Cog.cog_load` メソッド内で発生した例外は呼び出し元に伝播されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:17
msgid ":exc:`.ClientException` is raised when a cog with the same name is already loaded."
msgstr "同じ名前のコグがすでに読み込まれている場合、:exc:`.ClientException` が発生します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:22
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_cog_help:23
msgid "``cog`` parameter is now positional-only."
msgstr "``cog`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:26
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:14
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:16
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.unload_extension:15
msgid "This method is now a :term:`coroutine`."
msgstr "このメソッドは、:term:`coroutine` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:28
msgid "The cog to register to the bot."
msgstr "ボットに登録するコグ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:30
msgid "If a previously loaded cog with the same name should be ejected instead of raising an error."
msgstr "同じ名前のコグがすでに読み込まれているときに、例外を発生せず既存のものを削除するか。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:35
msgid "If the cog is an application command group, then this would be the guild where the cog group would be added to. If not given then it becomes a global command instead."
msgstr "コグがアプリケーションコマンドグループの場合、これはコググループが追加されるギルドになります。 \n"
"与えられない場合はグローバルコマンドになります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:41
msgid "If the cog is an application command group, then this would be the guilds where the cog group would be added to. If not given then it becomes a global command instead. Cannot be mixed with ``guild``."
msgstr "コグがアプリケーションコマンドグループの場合、これはコググループが追加されるギルドになります。 \n"
"与えられない場合はグローバルコマンドになります。\n"
"``guild`` と併用することはできません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:49
msgid "The cog does not inherit from :class:`.Cog`."
msgstr "コグが :class:`.Cog` から継承されていない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:50
msgid "An error happened during loading."
msgstr "読み込み中にエラーが発生した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_cog:51
msgid "A cog with the same name is already loaded."
msgstr "同じ名前のコグがすでに読み込まれている場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_command:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:1
msgid "Adds a :class:`.Command` into the internal list of commands."
msgstr ":class:`.Command` を内部のコマンドリストに追加します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_command:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.add_command:3
msgid "This is usually not called, instead the :meth:`~.GroupMixin.command` or :meth:`~.GroupMixin.group` shortcut decorators are used instead."
msgstr "これは通常、呼び出されません。代わりに :meth:`~.GroupMixin.command` か :meth:`~.GroupMixin.group` のショートカットデコレータが使われます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_command:6
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:6
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:6
msgid "Raise :exc:`.CommandRegistrationError` instead of generic :exc:`.ClientException`"
msgstr "一般的な :exc:`.ClientException` の代わりに、 :exc:`.CommandRegistrationError` を送出します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_command:11
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:11
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:11
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_command_signature:5
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:33
msgid "``command`` parameter is now positional-only."
msgstr "``command`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_command:13
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:13
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:13
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.add_command:6
msgid "The command to add."
msgstr "追加するコマンド。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_command:16
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:16
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:16
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.add_command:9
msgid "If the command or its alias is already registered by different command."
msgstr "コマンドやその別名が異なるコマンドによって、すでに登録されている場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_command:17
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:17
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.add_command:17
msgid "If the command passed is not a subclass of :class:`.Command`."
msgstr "渡されたコマンドが、 :class:`.Command` のサブクラスでない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_listener:1
msgid "The non decorator alternative to :meth:`.listen`."
msgstr ":meth:`.listen` に対するデコレーターでない代替物です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_listener:7
msgid "The function to call."
msgstr "呼び出される関数。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.add_listener:9
msgid "The name of the event to listen for. Defaults to ``func.__name__``."
msgstr "受け取るイベントの名前。デフォルトでは ``func.__name__`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.add_view:1
msgid "Registers a :class:`~discord.ui.View` for persistent listening."
msgstr ":class:`~discord.ui.View` を永続的にインタラクションを受け取るために登録します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.add_view:3
msgid "This method should be used for when a view is comprised of components that last longer than the lifecycle of the program."
msgstr "このメソッドは、ビューがプログラムのライフサイクルを超えて存在するコンポーネントで構成されている場合に利用すべきです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.add_view:8
msgid "The view to register for dispatching."
msgstr "実行するために登録するView"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.add_view:10
msgid "The message ID that the view is attached to. This is currently used to refresh the view's state during message update events. If not given then message update events are not propagated for the view."
msgstr "Viewが添付されているメッセージID。\n"
"これは、現在のViewの状態をメッセージ更新イベントの後に更新するのに使用されています。値が与えられていない場合はメッセージ更新のイベントはViewに伝わりません"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.add_view:15
msgid "A view was not passed."
msgstr "Viewが渡されなかった"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.add_view:16
msgid "The view is not persistent or is already finished. A persistent view has no timeout     and all their components have an explicitly provided custom_id."
msgstr ""

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.allowed_mentions:1
msgid "The allowed mention configuration."
msgstr "許可されたメンションの設定。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.allowed_mentions:5
msgid "Optional[:class:`~discord.AllowedMentions`]"
msgstr "Optional[:class:`~discord.AllowedMentions`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application:1
msgid "The client's application info."
msgstr "クライアントのアプリケーション情報。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application:3
msgid "This is retrieved on :meth:`~discord.Client.login` and is not updated afterwards. This allows populating the application_id without requiring a gateway connection."
msgstr "これは :meth:`~discord.Client.login` で取得され、その後更新されません。これによりゲートウェイ接続を行わずにapplication_idを取得できます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application:7
msgid "This is ``None`` if accessed before :meth:`~discord.Client.login` is called."
msgstr "もし :meth:`~discord.Client.login` の呼び出し前にアクセスされた場合は ``None`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application:9
msgid "The :meth:`~discord.Client.application_info` API call"
msgstr ":meth:`~discord.Client.application_info` API呼び出し"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application:13
msgid "Optional[:class:`~discord.AppInfo`]"
msgstr "Optional[:class:`~discord.AppInfo`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application_flags:1
msgid "The client's application flags."
msgstr "クライアントのアプリケーションフラグ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application_flags:5
msgid ":class:`~discord.ApplicationFlags`"
msgstr ":class:`~discord.ApplicationFlags`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application_id:1
msgid "The client's application ID."
msgstr "クライアントのアプリケーションID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.application_id:3
msgid "If this is not passed via ``__init__`` then this is retrieved through the gateway when an event contains the data or after a call to :meth:`~discord.Client.login`. Usually after :func:`~discord.on_connect` is called."
msgstr "これが ``__init__`` で渡されなかった場合、データを含むイベントが発生した際にゲートウェイを介して、または :meth:`~discord.Client.login` の後に取得されます。通常は :func:`~discord.on_connect` が呼び出された後です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.application_info:3
msgid "Retrieves the bot's application information."
msgstr "Botのアプリケーション情報を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.application_info:5
msgid "Retrieving the information failed somehow."
msgstr "情報の取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.application_info:7
msgid "The bot's application information."
msgstr "Botのアプリケーション情報。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.application_info:8
msgid ":class:`.AppInfo`"
msgstr ":class:`.AppInfo`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.before_identify_hook:3
msgid "A hook that is called before IDENTIFYing a session. This is useful if you wish to have more control over the synchronization of multiple IDENTIFYing clients."
msgstr "セッションをIDENTIFYingする前に、呼び出されるフック。 これは、複数の IDENTIFYing クライアントの同期を、より詳細に制御したい場合に便利です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.before_identify_hook:7
msgid "The default implementation sleeps for 5 seconds."
msgstr "デフォルトでは、5秒間スリープします。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.before_identify_hook:11
msgid "The shard ID that requested being IDENTIFY'd"
msgstr "IDENTIFYを要求した時のシャードID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.before_identify_hook:13
msgid "Whether this IDENTIFY is the first initial IDENTIFY."
msgstr "この IDENTIFY が、最初の初期化時の IDENTIFY であるかどうか。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.cached_messages:1
msgid "Read-only list of messages the connected client has cached."
msgstr "接続済みクライアントがキャッシュしている、読み取り専用のメッセージのリスト。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.cached_messages:5
msgid "Sequence[:class:`.Message`]"
msgstr "Sequence[:class:`.Message`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.change_presence:3
msgid "Changes the client's presence."
msgstr "クライアントのステータスを変更します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.change_presence:12
msgid "Removed the ``afk`` keyword-only parameter."
msgstr "キーワード引数 ``afk`` は削除されました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.change_presence:15
msgid "This function will now raise :exc:`TypeError` instead of ``InvalidArgument``."
msgstr "この関数は ``InvalidArgument`` の代わりに :exc:`TypeError` を発生させます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.change_presence:19
msgid "The activity being done. ``None`` if no currently active activity is done."
msgstr "実行中のアクティビティ。何も実行していない場合は ``None`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.change_presence:21
msgid "Indicates what status to change to. If ``None``, then :attr:`.Status.online` is used."
msgstr "変更するステータスを示します。 ``None`` の場合、 :attr:`.Status.online` が使用されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.change_presence:25
msgid "If the ``activity`` parameter is not the proper type."
msgstr "引数 ``activity`` が適切な型でない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.clear:1
msgid "Clears the internal state of the bot."
msgstr "Botの内部状態をクリアします。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.clear:3
msgid "After this, the bot can be considered \"re-opened\", i.e. :meth:`is_closed` and :meth:`is_ready` both return ``False`` along with the bot's internal cache cleared."
msgstr "これが実行されると、Botは「再実行」されたと見なされます。また、これにより :meth:`is_closed` や :meth:`is_ready` は ``False`` を返し、内部のキャッシュもクリアされます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.close:3
msgid "Closes the connection to Discord."
msgstr "Discordとの接続を閉じます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.cogs:1
msgid "A read-only mapping of cog name to cog."
msgstr "コグ名をキーとし、コグを値とする読み取り専用のマッピング。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.cogs:3
msgid "Mapping[:class:`str`, :class:`Cog`]"
msgstr "Mapping[:class:`str`, :class:`Cog`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.commands:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.commands:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.GroupMixin.commands:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.commands:1
msgid "A unique set of commands without aliases that are registered."
msgstr "登録済みの、別名を含まないコマンドの集合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.commands:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.commands:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.GroupMixin.commands:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.commands:3
msgid "Set[:class:`.Command`]"
msgstr "Set[:class:`.Command`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.connect:3
msgid "Creates a websocket connection and lets the websocket listen to messages from Discord. This is a loop that runs the entire event system and miscellaneous aspects of the library. Control is not resumed until the WebSocket connection is terminated."
msgstr "WebSocket接続を作成し、Discordからのメッセージを受け取れるようにします。これはイベントシステム全体とライブラリの様々な機能を実行するループです。WebSocket接続が終了するまで、制御は再開されません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.connect:8
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:22
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.start:8
msgid "If we should attempt reconnecting, either due to internet failure or a specific failure on Discord's part. Certain disconnects that lead to bad state will not be handled (such as invalid sharding payloads or bad tokens)."
msgstr "インターネットの障害やDiscord側の特定の障害が発生した際に再接続を試みるかどうか。不正な状態による特定の切断 (無効なシャーディングペイロードや不正なトークンなど) は処理されません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.connect:14
msgid "If the gateway to connect to Discord is not found. Usually if this     is thrown then there is a Discord API outage."
msgstr "Discordに接続するゲートウェイが見つからない場合。通常、これが発生した場合は、Discord側のAPIの停止が考えられます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.connect:15
msgid "The websocket connection has been terminated."
msgstr "WebSocket接続が終了した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_dm:3
msgid "Creates a :class:`.DMChannel` with this user."
msgstr "このユーザーと :class:`.DMChannel` を作成します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_dm:5
msgid "This should be rarely called, as this is done transparently for most people."
msgstr "これは、ほとんどの人にとっては自動で行われるため、呼び出す必要はめったにありません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_dm:10
msgid "The user to create a DM with."
msgstr "DMを作成するユーザー。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_dm:13
msgid "The channel that was created."
msgstr "作成されたチャンネル。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_dm:14
msgid ":class:`.DMChannel`"
msgstr ":class:`.DMChannel`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:3
msgid "Creates a :class:`.Guild`."
msgstr ":class:`.Guild` を作成します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:5
msgid "Bot accounts in more than 10 guilds are not allowed to create guilds."
msgstr "10以上のギルドに参加しているBotアカウントは、ギルドの作成ができません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:7
msgid "``name`` and ``icon`` parameters are now keyword-only. The ``region`` parameter has been removed."
msgstr "``name`` と ``icon`` パラメータはキーワード限定引数になりました。\n"
"``region`` パラメータは削除されました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:10
msgid "This function will now raise :exc:`ValueError` instead of ``InvalidArgument``."
msgstr "この関数は ``InvalidArgument`` の代わりに :exc:`ValueError` を発生させます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:14
msgid "The name of the guild."
msgstr "ギルドの名前。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:16
msgid "The :term:`py:bytes-like object` representing the icon. See :meth:`.ClientUser.edit` for more details on what is expected."
msgstr "アイコンを表す :term:`py:bytes-like object` 。引数の詳細については、 :meth:`ClientUser.edit` を参照してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:19
msgid "The code for a template to create the guild with."
msgstr "ギルドを作成するためのテンプレートコード。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:24
msgid "Guild creation failed."
msgstr "ギルドの作成に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:25
msgid "Invalid icon image format given. Must be PNG or JPG."
msgstr "アイコンの画像形式が無効だった場合。画像は、PNGまたはJPGである必要があります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:27
msgid "The guild created. This is not the same guild that is added to cache."
msgstr "作成されたGuild。Botのキャッシュに追加されるGuildとはまた別物です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.create_guild:29
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:32
msgid ":class:`.Guild`"
msgstr ":class:`.Guild`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.delete_invite:3
msgid "Revokes an :class:`.Invite`, URL, or ID to an invite."
msgstr ":class:`.Invite` や、招待のURL、IDを削除します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.delete_invite:5
msgid "You must have :attr:`~.Permissions.manage_channels` in the associated guild to do this."
msgstr "これを行うには、関連付けられたGuildにて、 :attr:`~.Permissions.manage_channels` が必要です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.delete_invite:10
msgid "``invite`` parameter is now positional-only."
msgstr "``invite`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.delete_invite:12
msgid "The invite to revoke."
msgstr "取り消す招待。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.delete_invite:15
msgid "You do not have permissions to revoke invites."
msgstr "削除する権限がない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.delete_invite:16
msgid "The invite is invalid or expired."
msgstr "招待が無効または期限切れの場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.delete_invite:17
msgid "Revoking the invite failed."
msgstr "招待の取り消しに失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.emojis:1
msgid "The emojis that the connected client has."
msgstr "接続したクライアントが利用できる絵文字。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.emojis:3
msgid "Sequence[:class:`.Emoji`]"
msgstr "Sequence[:class:`.Emoji`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.extensions:1
msgid "A read-only mapping of extension name to extension."
msgstr "エクステンション名の読み取り専用マッピング。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.extensions:3
msgid "Mapping[:class:`str`, :class:`py:types.ModuleType`]"
msgstr "Mapping[:class:`str`, :class:`py:types.ModuleType`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:3
msgid "Retrieves a :class:`.abc.GuildChannel`, :class:`.abc.PrivateChannel`, or :class:`.Thread` with the specified ID."
msgstr "指定されたIDを持つ :class:`.abc.GuildChannel` 、 :class:`.abc.PrivateChannel` 、または :class:`.Thread` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:7
msgid "This method is an API call. For general usage, consider :meth:`get_channel` instead."
msgstr "このメソッドはDiscord APIからGETします。通常は :meth:`get_channel` を代わりとして使用してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:13
msgid "``channel_id`` parameter is now positional-only."
msgstr "``channel_id`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:15
msgid "An unknown channel type was received from Discord."
msgstr "まだ定義されていないチャンネルタイプがDiscord APIから受信された場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:16
msgid "Retrieving the channel failed."
msgstr "チャンネルの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:17
msgid "Invalid Channel ID."
msgstr "引数が無効なチャンネル IDである場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:18
msgid "You do not have permission to fetch this channel."
msgstr "このチャンネルからメッセージを取得する権限がない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:20
msgid "The channel from the ID."
msgstr "IDから取得したチャンネル。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_channel:21
msgid "Union[:class:`.abc.GuildChannel`, :class:`.abc.PrivateChannel`, :class:`.Thread`]"
msgstr "Union[:class:`.abc.GuildChannel`, :class:`.abc.PrivateChannel`, :class:`.Thread`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:3
msgid "Retrieves a :class:`.Guild` from an ID."
msgstr "IDから :class:`.Guild` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:7
msgid "Using this, you will **not** receive :attr:`.Guild.channels`, :attr:`.Guild.members`, :attr:`.Member.activity` and :attr:`.Member.voice` per :class:`.Member`."
msgstr "これを使用した場合、 :attr:`.Guild.channels` 、 :attr:`.Guild.members` 、そして各 :class:`.Member` ごとの :attr:`.Member.activity` 、 :attr:`.Member.voice` を**取得することができません** 。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:12
msgid "This method is an API call. For general usage, consider :meth:`get_guild` instead."
msgstr "このメソッドはAPIを呼び出します。通常は :meth:`get_guild` を代わりとして使用してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:16
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_widget:11
msgid "``guild_id`` parameter is now positional-only."
msgstr "``guild_id`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:19
msgid "The guild's ID to fetch from."
msgstr "取得したいギルドのID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:21
msgid "Whether to include count information in the guild. This fills the :attr:`.Guild.approximate_member_count` and :attr:`.Guild.approximate_presence_count` attributes without needing any privileged intents. Defaults to ``True``."
msgstr "ギルドにカウント情報を含めるかどうか。これを使うことで特権インテントがなくても :attr:`.Guild.approximate_member_count` と :attr:`.Guild.approximate_presence_count` 属性が設定されます。デフォルトは ``True`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:28
msgid "You do not have access to the guild."
msgstr "Guildに「アクセス」する権限を持っていない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:29
msgid "Getting the guild failed."
msgstr "Guildの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guild:31
msgid "The guild from the ID."
msgstr "IDから取得したギルド。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:1
msgid "Retrieves an :term:`asynchronous iterator` that enables receiving your guilds."
msgstr "Botが所属するGuildを取得できる、 :term:`asynchronous iterator` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:5
msgid "Using this, you will only receive :attr:`.Guild.owner`, :attr:`.Guild.icon`, :attr:`.Guild.id`, :attr:`.Guild.name`, :attr:`.Guild.approximate_member_count`, and :attr:`.Guild.approximate_presence_count` per :class:`.Guild`."
msgstr ""

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:11
msgid "This method is an API call. For general usage, consider :attr:`guilds` instead."
msgstr "これはAPIを呼び出します。通常は :attr:`guilds` を代わりに使用してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:14
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:22
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:37
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check_any:20
#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:6
msgid "Examples"
msgstr "例"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:15
#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:7
msgid "Usage ::"
msgstr "使い方 ::"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:20
msgid "Flattening into a list ::"
msgstr "リストへフラット化 ::"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:25
#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:19
msgid "All parameters are optional."
msgstr "すべてのパラメータがオプションです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:27
msgid "The number of guilds to retrieve. If ``None``, it retrieves every guild you have access to. Note, however, that this would make it a slow operation. Defaults to ``200``."
msgstr "取得するギルドの数。 ``None`` の場合、Botがアクセスできるギルドすべてを取得します。ただし、これには時間が掛かることに注意してください。デフォルトは200です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:34
msgid "The default has been changed to 200."
msgstr "デフォルトが200に変更されました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:36
msgid "Retrieves guilds before this date or object. If a datetime is provided, it is recommended to use a UTC aware datetime. If the datetime is naive, it is assumed to be local time."
msgstr "渡された日付、またはギルドより前のギルドを取得します。日付を指定する場合、UTC aware datetimeを利用することを推奨します。naive datetimeである場合、これはローカル時間であるとみなされます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:40
msgid "Retrieve guilds after this date or object. If a datetime is provided, it is recommended to use a UTC aware datetime. If the datetime is naive, it is assumed to be local time."
msgstr "渡された日付、またはオブジェクトより後のギルドを取得します。日付を指定する場合、UTC対応の「aware」を利用することを推奨します。日付が「naive」である場合、これは地域時間であるとみなされます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:44
msgid "Whether to include count information in the guilds. This fills the :attr:`.Guild.approximate_member_count` and :attr:`.Guild.approximate_presence_count` attributes without needing any privileged intents. Defaults to ``True``."
msgstr ""

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:51
msgid "Getting the guilds failed."
msgstr "Guildの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_channels:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_members:0
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:0
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:0
msgid "Yields"
msgstr "Yieldする値"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_guilds:53
msgid ":class:`.Guild` -- The guild with the guild data parsed."
msgstr ":class:`.Guild` -- データを解析したGuild。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:3
msgid "Gets an :class:`.Invite` from a discord.gg URL or ID."
msgstr "discord.gg URLまたはIDから、 :class:`.Invite` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:7
msgid "If the invite is for a guild you have not joined, the guild and channel attributes of the returned :class:`.Invite` will be :class:`.PartialInviteGuild` and :class:`.PartialInviteChannel` respectively."
msgstr "もしBotがInviteのGuildに参加していない場合、 :class:`.Invite` のguildとchannel属性はそれぞれ :class:`.PartialInviteGuild` と :class:`.PartialInviteChannel` になります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:11
msgid "The Discord invite ID or URL (must be a discord.gg URL)."
msgstr "Discordの招待ID、またはURL (discord.gg URLである必要があります)。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:13
msgid "Whether to include count information in the invite. This fills the :attr:`.Invite.approximate_member_count` and :attr:`.Invite.approximate_presence_count` fields."
msgstr "招待にカウントの情報を含めるかどうか。これにより :attr:`.Invite.approximate_member_count` と :attr:`.Invite.approximate_presence_count` 属性が追加されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:17
msgid "Whether to include the expiration date of the invite. This fills the :attr:`.Invite.expires_at` field."
msgstr "招待の有効期限を含めるかどうか。有効期限は :attr:`.Invite.expires_at` に代入されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:22
msgid "The ID of the scheduled event this invite is for."
msgstr "招待に紐づいたスケジュールイベントのID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:26
msgid "It is not possible to provide a url that contains an ``event_id`` parameter when using this parameter."
msgstr "このパラメータを使用する場合、``event_id`` パラメータを含む url を指定することはできません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:32
msgid "The url contains an ``event_id``, but ``scheduled_event_id`` has also been provided."
msgstr "URLに ``event_id`` が含まれているのに、 ``scheduled_event_id`` も渡された場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:33
msgid "The invite has expired or is invalid."
msgstr "招待の有効期限が切れたか無効の場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:34
msgid "Getting the invite failed."
msgstr "招待の取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:36
msgid "The invite from the URL/ID."
msgstr "URL/IDから取得した招待。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_invite:37
msgid ":class:`.Invite`"
msgstr ":class:`.Invite`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_premium_sticker_packs:3
msgid "Retrieves all available premium sticker packs."
msgstr "利用可能なプレミアムスタンプパックをすべて取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_premium_sticker_packs:7
msgid "Retrieving the sticker packs failed."
msgstr "スタンプパックの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_premium_sticker_packs:9
msgid "All available premium sticker packs."
msgstr "利用可能なプレミアムスタンプパックすべて。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_premium_sticker_packs:10
msgid "List[:class:`.StickerPack`]"
msgstr "List[:class:`.StickerPack`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_stage_instance:3
msgid "Gets a :class:`.StageInstance` for a stage channel id."
msgstr "ステージチャンネルIDの :class:`.StageInstance` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_stage_instance:7
msgid "The stage channel ID."
msgstr "ステージチャンネルのID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_stage_instance:10
msgid "The stage instance or channel could not be found."
msgstr "ステージインスタンスまたはチャンネルが見つからなかった場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_stage_instance:11
msgid "Getting the stage instance failed."
msgstr "ステージインスタンスの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_stage_instance:13
msgid "The stage instance from the stage channel ID."
msgstr "ステージチャンネルIDで取得したステージインスタンス"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_stage_instance:14
msgid ":class:`.StageInstance`"
msgstr ":class:`.StageInstance`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_sticker:3
msgid "Retrieves a :class:`.Sticker` with the specified ID."
msgstr "特定のIDの :class:`.Sticker` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_sticker:7
msgid "Retrieving the sticker failed."
msgstr "スタンプの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_sticker:8
msgid "Invalid sticker ID."
msgstr "スタンプIDが無効な場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_sticker:10
msgid "The sticker you requested."
msgstr "要求されたスタンプ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_sticker:11
msgid "Union[:class:`.StandardSticker`, :class:`.GuildSticker`]"
msgstr "Union[:class:`.StandardSticker`, :class:`.GuildSticker`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_template:3
msgid "Gets a :class:`.Template` from a discord.new URL or code."
msgstr "discord.new URL またはコードから :class:`.Template` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_template:5
msgid "The Discord Template Code or URL (must be a discord.new URL)."
msgstr "DiscordテンプレートコードまたはURL (discord.new URLである必要があります)。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_template:8
msgid "The template is invalid."
msgstr "無効なテンプレートである場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_template:9
msgid "Getting the template failed."
msgstr "テンプレートの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_template:11
msgid "The template from the URL/code."
msgstr "URL/コードからのテンプレート。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_template:12
msgid ":class:`.Template`"
msgstr ":class:`.Template`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_user:3
msgid "Retrieves a :class:`~discord.User` based on their ID. You do not have to share any guilds with the user to get this information, however many operations do require that you do."
msgstr "IDをもとに :class:`~discord.User` を取得します。そのユーザーとギルドを共有する必要はありませんが、操作の多くはそれを必要とします。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_user:9
msgid "This method is an API call. If you have :attr:`discord.Intents.members` and member cache enabled, consider :meth:`get_user` instead."
msgstr "このメソッドはAPIを呼び出します。 :attr:`discord.Intents.members` とメンバーキャッシュを有効化している場合は、 :meth:`get_user` を使用するべきです"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_user:13
msgid "``user_id`` parameter is now positional-only."
msgstr "``user_id`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_user:15
msgid "The user's ID to fetch from."
msgstr "取得したいユーザーのID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_user:18
msgid "A user with this ID does not exist."
msgstr "この ID を持つユーザーが存在しない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_user:19
msgid "Fetching the user failed."
msgstr "ユーザーの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_user:21
msgid "The user you requested."
msgstr "あなたがリクエストしたユーザー。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_user:22
msgid ":class:`~discord.User`"
msgstr ":class:`~discord.User`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_webhook:3
msgid "Retrieves a :class:`.Webhook` with the specified ID."
msgstr "指定した ID の :class:`.Webhook` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_webhook:7
msgid "``webhook_id`` parameter is now positional-only."
msgstr "``webhook_id`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_webhook:9
msgid "Retrieving the webhook failed."
msgstr "Webhookの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_webhook:10
msgid "Invalid webhook ID."
msgstr "無効なWebhookのIDだった場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_webhook:11
msgid "You do not have permission to fetch this webhook."
msgstr "Webhookを取得する権限がない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_webhook:13
msgid "The webhook you requested."
msgstr "BotがリクエストしたWebhook。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_webhook:14
msgid ":class:`.Webhook`"
msgstr ":class:`.Webhook`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_widget:3
msgid "Gets a :class:`.Widget` from a guild ID."
msgstr "Guild IDから :class:`.Widget` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_widget:7
msgid "The guild must have the widget enabled to get this information."
msgstr "この情報を取得するためには、Guildのウィジェットを有効化しておく必要があります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_widget:13
msgid "The ID of the guild."
msgstr "GuildのID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_widget:16
msgid "The widget for this guild is disabled."
msgstr "Guildのウィジェットが無効になっている場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_widget:17
msgid "Retrieving the widget failed."
msgstr "ウィジェットの取得に失敗した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_widget:19
msgid "The guild's widget."
msgstr "Guildのウィジェット。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.fetch_widget:20
msgid ":class:`.Widget`"
msgstr ":class:`.Widget`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_channels:1
msgid "A generator that retrieves every :class:`.abc.GuildChannel` the client can 'access'."
msgstr "クライアントが「アクセス」できるすべての :class:`.abc.GuildChannel` のジェネレータを取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_channels:3
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_members:3
msgid "This is equivalent to: ::"
msgstr "これは以下に相当します: ::"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_channels:11
msgid "Just because you receive a :class:`.abc.GuildChannel` does not mean that you can communicate in said channel. :meth:`.abc.GuildChannel.permissions_for` should be used for that."
msgstr ":class:`.abc.GuildChannel` を受け取ったからと言って、そのチャンネルで、クライアントが発言可能であるとは限りません。クライアントが発言可能なチャンネルのみを取得したいのなら、 :meth:`.abc.GuildChannel.permissions_for` を使ってください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_channels:15
msgid ":class:`.abc.GuildChannel` -- A channel the client can 'access'."
msgstr ":class:`.abc.GuildChannel` -- クライアントが「アクセスする」ことができるチャンネル。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_members:1
msgid "Returns a generator with every :class:`.Member` the client can see."
msgstr "クライアントが参照可能なすべての :class:`.Member` のgeneratorを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_all_members:9
msgid ":class:`.Member` -- A member the client can see."
msgstr ":class:`.Member` -- クライアントが見れる、Guildのメンバー。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_channel:1
msgid "Returns a channel or thread with the given ID."
msgstr "与えられたIDのチャンネルまたはスレッドを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_channel:5
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_emoji:5
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_guild:5
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_user:5
msgid "``id`` parameter is now positional-only."
msgstr "``id`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_channel:7
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_emoji:7
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_guild:7
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_stage_instance:5
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_user:7
msgid "The ID to search for."
msgstr "検索するID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_channel:10
msgid "The returned channel or ``None`` if not found."
msgstr "チャンネル、または該当するものが見つからない場合 ``None`` が返ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_channel:11
msgid "Optional[Union[:class:`.abc.GuildChannel`, :class:`.Thread`, :class:`.abc.PrivateChannel`]]"
msgstr "Optional[Union[:class:`.abc.GuildChannel`, :class:`.Thread`, :class:`.abc.PrivateChannel`]]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_cog:1
msgid "Gets the cog instance requested."
msgstr "要求されたコグのインスタンスを取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_cog:3
msgid "If the cog is not found, ``None`` is returned instead."
msgstr "コグが見つからなかった場合、代わりに ``None`` が返されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_cog:7
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.get_command:12
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:12
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_command:8
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:12
msgid "``name`` parameter is now positional-only."
msgstr "``name`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_cog:9
msgid "The name of the cog you are requesting. This is equivalent to the name passed via keyword argument in class creation or the class name if unspecified."
msgstr "リクエストしているコグの名前です。これは、クラス作成時にキーワード引数で渡された名前と同等です。指定しない場合は、クラス名と同等になります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_cog:14
msgid "The cog that was requested. If not found, returns ``None``."
msgstr "要求されたコグ。見つからない場合は ``None`` を返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_cog:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:62
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.HelpCommand.cog:10
msgid "Optional[:class:`Cog`]"
msgstr "Optional[:class:`bool`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.get_command:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.get_command:1
msgid "Get a :class:`.Command` from the internal list of commands."
msgstr "内部のコマンドリストから検索し、 :class:`.Command` を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.get_command:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:4
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.get_command:4
msgid "This could also be used as a way to get aliases."
msgstr "これはコマンドのエイリアスを取得する方法としても使用できます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.get_command:6
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:6
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:6
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.get_command:6
msgid "The name could be fully qualified (e.g. ``'foo bar'``) will get the subcommand ``bar`` of the group command ``foo``. If a subcommand is not found then ``None`` is returned just as usual."
msgstr "名前は修飾されていても構いません(例： ``'foo bar'`` など)。サブコマンドが見つからなかった場合は、通常通り ``None`` が返されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.get_command:14
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:14
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:14
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.get_command:14
msgid "The name of the command to get."
msgstr "取得するコマンドの名前。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.get_command:17
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:17
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:17
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.get_command:17
msgid "The command that was requested. If not found, returns ``None``."
msgstr "要求されたコマンド。見つからなければ ``None`` を返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.get_command:18
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:103
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:18
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.get_command:18
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.get_command:18
msgid "Optional[:class:`Command`]"
msgstr "Optional[:class:`Command`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:3
msgid "Returns the invocation context from the message or interaction."
msgstr "メッセージまたはインタラクションから、呼び出しコンテキストを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:5
msgid "This is a more low-level counter-part for :meth:`.process_commands` to allow users more fine grained control over the processing."
msgstr "これは :meth:`.process_commands` より低レベルなcounter-partで、開発者がより細かく処理を制御できるようにするためのものです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:8
msgid "The returned context is not guaranteed to be a valid invocation context, :attr:`.Context.valid` must be checked to make sure it is. If the context is not valid then it is not a valid candidate to be invoked under :meth:`~.Bot.invoke`."
msgstr "返されたコンテキストが、有効な呼び出しコンテキストであることは保証していないので、 :attr:`.Context.valid` によってチェックして確認する必要があります。コンテキストが有効でない場合は、 :meth:`~.Bot.invoke` で呼び出される有効な候補ではありません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:15
msgid "In order for the custom context to be used inside an interaction-based context (such as :class:`HybridCommand`) then this method must be overridden to return that class."
msgstr "インタラクションベースのコンテキストでカスタムコンテキストを使用する場合（ :class:`HybridCommand` など）にはこのメソッドを上書きしてクラスを返さないといけません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:21
msgid "``message`` parameter is now positional-only and renamed to ``origin``."
msgstr "``message`` パラメータが位置指定のみになり、 ``origin`` に改名されました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:23
msgid "The message or interaction to get the invocation context from."
msgstr "呼び出しコンテキストを取得するためのメッセージやインタラクション。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:25
msgid "The factory class that will be used to create the context. By default, this is :class:`.Context`. Should a custom class be provided, it must be similar enough to :class:`.Context`\\'s interface."
msgstr "コンテキストを作成するために使用されるファクトリークラス。デフォルトでは、これは :class:`.Context` です。カスタムクラスを提供する場合は、 :class:`.Context` の引数が同じようにならなければなりません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:30
msgid "The invocation context. The type of this can change via the ``cls`` parameter."
msgstr "呼び出しコンテキスト。このタイプは ``cls`` パラメータを使用して変更できます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_context:32
msgid ":class:`.Context`"
msgstr ":class:`.Context`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_emoji:1
msgid "Returns an emoji with the given ID."
msgstr "与えられた ID の絵文字を返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_emoji:10
msgid "The custom emoji or ``None`` if not found."
msgstr "カスタム絵文字が返されます。見つからなかった場合は、 ``None`` が返ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_emoji:11
msgid "Optional[:class:`.Emoji`]"
msgstr "Optional[:class:`.Emoji`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_guild:1
msgid "Returns a guild with the given ID."
msgstr "与えられたIDを検索し、それに合致するGuildを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_guild:10
msgid "The guild or ``None`` if not found."
msgstr "Guildが返されます。見つからなかった場合は、 ``None`` が返ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_guild:11
#: ../../docstring of discord.ext.commands.Context.guild:3
msgid "Optional[:class:`.Guild`]"
msgstr "Optional[:class:`.Guild`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:1
msgid "Returns a partial messageable with the given channel ID."
msgstr "与えられたチャンネルIDのPartialMessageableを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:3
msgid "This is useful if you have a channel_id but don't want to do an API call to send messages to it."
msgstr "これはチャンネルIDがあるがメッセージを送信するためにAPI呼び出しをしたくない場合に便利です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:8
msgid "The channel ID to create a partial messageable for."
msgstr "PartialMessageableを作成するためのチャンネルID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:10
msgid "The optional guild ID to create a partial messageable for.  This is not required to actually send messages, but it does allow the :meth:`~discord.PartialMessageable.jump_url` and :attr:`~discord.PartialMessageable.guild` properties to function properly."
msgstr "部分的なメッセージ可能チャンネルを作成するためのオプションのギルドID。これはメッセージの送信には必須ではありませんが、 :meth:`~discord.PartialMessageable.jump_url` や :attr:`~discord.PartialMessageable.guild` プロパティが適切に動作するようになります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:10
msgid "The optional guild ID to create a partial messageable for."
msgstr "部分的なメッセージ可能チャンネルを作成するためのオプションのギルドID。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:12
msgid "This is not required to actually send messages, but it does allow the :meth:`~discord.PartialMessageable.jump_url` and :attr:`~discord.PartialMessageable.guild` properties to function properly."
msgstr "これはメッセージの送信には必須ではありませんが、 :meth:`~discord.PartialMessageable.jump_url` や :attr:`~discord.PartialMessageable.guild` プロパティが適切に動作するようになります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:16
msgid "The underlying channel type for the partial messageable."
msgstr "PartialMessageableの基礎となるチャンネルタイプ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:19
msgid "The partial messageable"
msgstr "PartialMessageable"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_partial_messageable:20
msgid ":class:`.PartialMessageable`"
msgstr ":class:`.PartialMessageable`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_prefix:3
msgid "Retrieves the prefix the bot is listening to with the message as a context."
msgstr "特定のメッセージの文脈内でボットが使用する接頭辞を取得します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_prefix:8
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.process_commands:19
msgid "``message`` parameter is now positional-only."
msgstr "``message`` パラメータが位置指定のみになりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_prefix:10
msgid "The message context to get the prefix of."
msgstr "接頭辞を取得するメッセージのコンテキスト。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_prefix:13
msgid "A list of prefixes or a single prefix that the bot is listening for."
msgstr "ボットが受け取る接頭辞またはそのリスト。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.get_prefix:15
msgid "Union[List[:class:`str`], :class:`str`]"
msgstr "Union[List[:class:`str`], :class:`str`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_stage_instance:1
msgid "Returns a stage instance with the given stage channel ID."
msgstr "与えられたステージチャンネルIDを持つステージインスタンスを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_stage_instance:8
msgid "The stage instance or ``None`` if not found."
msgstr "ステージインスタンス、または該当するものが見つからない場合 ``None`` が返ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_stage_instance:9
msgid "Optional[:class:`.StageInstance`]"
msgstr "Optional[:class:`.StageInstance`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_sticker:1
msgid "Returns a guild sticker with the given ID."
msgstr "与えられたIDに合致するギルドスタンプを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_sticker:7
msgid "To retrieve standard stickers, use :meth:`.fetch_sticker`. or :meth:`.fetch_premium_sticker_packs`."
msgstr "標準スタンプを取得するには、 :meth:`.fetch_sticker` か :meth:`.fetch_premium_sticker_packs` を使用してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_sticker:10
msgid "The sticker or ``None`` if not found."
msgstr "Stickerが返ります。見つからなかった場合は ``None`` が返ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_sticker:11
msgid "Optional[:class:`.GuildSticker`]"
msgstr "Optional[:class:`.GuildSticker`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_user:1
msgid "Returns a user with the given ID."
msgstr "与えられたIDを検索し、それに合致するユーザーを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_user:10
msgid "The user or ``None`` if not found."
msgstr "Userが返されます。見つからなかった場合は、 ``None`` が返ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.get_user:11
msgid "Optional[:class:`~discord.User`]"
msgstr "Optional[:class:`~discord.User`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.guilds:1
msgid "The guilds that the connected client is a member of."
msgstr "接続したクライアントがメンバーとして参加しているギルド。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.guilds:3
msgid "Sequence[:class:`.Guild`]"
msgstr "Sequence[:class:`.Guild`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.intents:1
msgid "The intents configured for this connection."
msgstr "この接続用に設定されたインテント。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.intents:5
msgid ":class:`~discord.Intents`"
msgstr ":class:`~discord.Intents`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.invoke:3
msgid "Invokes the command given under the invocation context and handles all the internal event dispatch mechanisms."
msgstr "コンテキストに与えられたコマンドを呼び出します。すべての内部イベントも処理されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.invoke:8
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.reset_cooldown:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:12
msgid "``ctx`` parameter is now positional-only."
msgstr "``ctx`` パラメータは位置指定のみになりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.invoke:10
msgid "The invocation context to invoke."
msgstr "呼び出すコンテキスト。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.is_closed:1
msgid ":class:`bool`: Indicates if the websocket connection is closed."
msgstr ":class:`bool`: WebSocket接続が閉じられているかどうか。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.is_owner:3
msgid "Checks if a :class:`~discord.User` or :class:`~discord.Member` is the owner of this bot."
msgstr ":class:`~discord.User` または :class:`~discord.Member` がこのボットの所有者か確認します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.is_owner:6
msgid "If an :attr:`owner_id` is not set, it is fetched automatically through the use of :meth:`~.Bot.application_info`."
msgstr ":attr:`owner_id` が設定されていない場合、 :meth:`~.Bot.application_info` から取得されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.is_owner:9
msgid "The function also checks if the application is team-owned if :attr:`owner_ids` is not set."
msgstr ":attr:`owner_ids` が設定されていない場合、この関数はチームが所有しているかも確認します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.is_owner:15
msgid "``user`` parameter is now positional-only."
msgstr "``user`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.is_owner:17
msgid "The user to check for."
msgstr "確認するユーザー。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.is_owner:20
msgid "Whether the user is the owner."
msgstr "ユーザーが所有者かどうか。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.is_ready:1
msgid ":class:`bool`: Specifies if the client's internal cache is ready for use."
msgstr ":class:`bool`: クライアントの内部キャッシュが利用可能となっているかを表します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.is_ws_ratelimited:1
msgid ":class:`bool`: Whether the websocket is currently rate limited."
msgstr ":class:`bool`: WebSocketがレート制限中かどうか。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.is_ws_ratelimited:3
msgid "This can be useful to know when deciding whether you should query members using HTTP or via the gateway."
msgstr "メンバーへのクエリをHTTPで行うか、ゲートウェイ経由で行うかを決めるときに役立ちます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.latency:1
msgid "Measures latency between a HEARTBEAT and a HEARTBEAT_ACK in seconds."
msgstr "HEARTBEATとHEARTBEAT_ACKの間の遅延を秒単位で測定します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.latency:3
msgid "This could be referred to as the Discord WebSocket protocol latency."
msgstr "DiscordのWebSocketプロトコルの遅延として使うこともできます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.latency:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:14
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:14
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:14
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandOnCooldown:22
msgid ":class:`float`"
msgstr ":class:`float`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:3
msgid "Loads an extension."
msgstr "エクステンションを読み込みます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:5
msgid "An extension is a python module that contains commands, cogs, or listeners."
msgstr "エクステンションはコマンド、コグ、リスナーを含むPythonのモジュールです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:8
msgid "An extension must have a global function, ``setup`` defined as the entry point on what to do when the extension is loaded. This entry point must have a single argument, the ``bot``."
msgstr "エクステンションは ``setup`` という名前のグローバルな関数を持っている必要があります。 ``setup`` はエクステンションを読み込むときのエントリーポイントになります。エントリーポイントは ``bot`` という引数を持っている必要があります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:16
msgid "The extension name to load. It must be dot separated like regular Python imports if accessing a sub-module. e.g. ``foo.test`` if you want to import ``foo/test.py``."
msgstr "読み込むエクステンションの名前。サブモジュールにアクセスする場合はPythonのimport文のようにドットで区切る必要があります。 例えば ``hoge/test.py`` を読み込むときには ``hoge.test`` になります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:20
msgid "The package name to resolve relative imports with. This is required when loading an extension using a relative path, e.g ``.foo.test``. Defaults to ``None``."
msgstr "相対インポートを解決するためのパッケージ名。例えば ``.foo.test`` のように、相対パスで拡張機能を読み込む場合に必要になります。デフォルトは ``None`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:27
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.reload_extension:22
msgid "The extension could not be imported.     This is also raised if the name of the extension could not     be resolved using the provided ``package`` parameter."
msgstr "エクステンションを読み込めなかった場合。この例外は渡された ``package`` パラメータを解釈できなかったときにも発生します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:28
msgid "The extension is already loaded."
msgstr "エクステンションがすでに読み込まれている場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:29
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.reload_extension:23
msgid "The extension does not have a setup function."
msgstr "エクステンションに ``setup`` 関数がない場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.load_extension:30
msgid "The extension or its setup function had an execution error."
msgstr "エクステンション、または ``setup`` 関数で例外が発生した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.login:3
msgid "Logs in the client with the specified credentials and calls the :meth:`setup_hook`."
msgstr "指定した資格情報を使用してクライアントにログインし、 :meth:`setup_hook` を呼び出します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.login:7
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:19
#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.start:5
msgid "The authentication token. Do not prefix this token with anything as the library will do it for you."
msgstr "認証トークン。ライブラリが処理するのでトークンには何もつけないでください（ ``Bot`` 等）。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.login:11
msgid "The wrong credentials are passed."
msgstr "不正な認証情報が渡された場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.login:12
msgid "An unknown HTTP related error occurred,     usually when it isn't 200 or the known incorrect credentials     passing status code."
msgstr "不明なHTTP関連のエラーが発生した場合。通常、ステータスコードが200でないか、既知の誤った資格情報が渡された場合です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.on_command_error:3
msgid "The default command error handler provided by the bot."
msgstr "ボットに渡されたデフォルトのコマンドの例外のハンドラ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.on_command_error:5
msgid "By default this logs to the library logger, however it could be overridden to have a different implementation."
msgstr "デフォルトではライブラリロガーに出力しますが、他の実装をするために上書きすることもできます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.on_command_error:8
msgid "This only fires if you do not specify any listeners for command error."
msgstr "これは、コマンドの例外のリスナーを指定していない場合にのみ発生します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.on_command_error:12
msgid "``context`` and ``exception`` parameters are now positional-only. Instead of writing to ``sys.stderr`` this now uses the library logger."
msgstr "``context`` と ``exception`` が位置限定引数になりました。 ``sys.stderr`` に出力するのではなくライブラリロガーが使用されるようになりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.on_error:3
msgid "The default error handler provided by the client."
msgstr "クライアントによって提供されるデフォルトのエラーハンドラ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.on_error:5
msgid "By default this logs to the library logger however it could be overridden to have a different implementation. Check :func:`~discord.on_error` for more details."
msgstr "デフォルトでは、これはライブラリロガーに出力されますが、異なる実装によって上書きされる可能性があります。詳細については :func:`~discord.on_error` を確認してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.on_error:11
msgid "``event_method`` parameter is now positional-only and instead of writing to ``sys.stderr`` it logs instead."
msgstr "``event_method`` パラメータが位置限定引数になり、 ``sys.stderr`` に出力するのではなくログに記録するようになりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.persistent_views:1
msgid "A sequence of persistent views added to the client."
msgstr "クライアントに追加された永続的なビューのシーケンスです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.persistent_views:5
msgid "Sequence[:class:`.View`]"
msgstr "Sequence[:class:`.View`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.private_channels:1
msgid "The private channels that the connected client is participating on."
msgstr "接続したクライアントが参加しているプライベートチャンネル。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.private_channels:5
msgid "This returns only up to 128 most recent private channels due to an internal working on how Discord deals with private channels."
msgstr "Discordでのプライベートチャンネルの取扱いは内部的に処理されているため、これは最新のプライベートチャンネルから最大128個までしか取得できません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.private_channels:8
msgid "Sequence[:class:`.abc.PrivateChannel`]"
msgstr "Sequence[:class:`.abc.PrivateChannel`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.process_commands:3
msgid "This function processes the commands that have been registered to the bot and other groups. Without this coroutine, none of the commands will be triggered."
msgstr "ボットとそのグルーブに登録されたコマンドを処理します。この関数を取り除くとコマンドは処理されません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.process_commands:7
msgid "By default, this coroutine is called inside the :func:`.on_message` event. If you choose to override the :func:`.on_message` event, then you should invoke this coroutine as well."
msgstr "デフォルトでは、この関数は :func:`on_message` のなかで呼び出されます。 :func:`on_message` を上書きした場合にはこの関数を呼び出す処理を書く必要があります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.process_commands:11
msgid "This is built using other low level tools, and is equivalent to a call to :meth:`~.Bot.get_context` followed by a call to :meth:`~.Bot.invoke`."
msgstr "これは他の低レベルのツールを使って構築されており、 :meth:`~.Bot.get_context` の呼び出しに続いて、 :meth:`~.Bot.invoke` の呼び出しに相当します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.process_commands:14
msgid "This also checks if the message's author is a bot and doesn't call :meth:`~.Bot.get_context` or :meth:`~.Bot.invoke` if so."
msgstr "また、メッセージの作成者がボットであるか確認し、その場合 :meth:`~.Bot.get_context` や :meth:`~.Bot.invoke` を呼び出しません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.process_commands:21
msgid "The message to process commands for."
msgstr "コマンドを処理するメッセージ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.reload_extension:3
msgid "Atomically reloads an extension."
msgstr "拡張機能を「極小単位で」リロードします。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.reload_extension:5
msgid "This replaces the extension with the same extension, only refreshed. This is equivalent to a :meth:`unload_extension` followed by a :meth:`load_extension` except done in an atomic way. That is, if an operation fails mid-reload then the bot will roll-back to the prior working state."
msgstr "これにより、拡張機能が、ほとんど同じ拡張機能で置き換えられ、更新されるだけです。 これは 、:meth:`unload_extension` が実行され、次に :meth:`load_extension` が実行される、この一連の流れと同等です。 つまり、操作がリロード中に失敗した場合、Botは以前の状態にロールバックします。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.reload_extension:10
msgid "The extension name to reload. It must be dot separated like regular Python imports if accessing a sub-module. e.g. ``foo.test`` if you want to import ``foo/test.py``."
msgstr "リロードする拡張モジュール名。サブモジュールにアクセスする場合は、通常の Pythonのimportのように、ドットで区切られている必要があります。 例: ``foo.test`` を ``foo/test.py`` をインポートする場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.reload_extension:14
msgid "The package name to resolve relative imports with. This is required when reloading an extension using a relative path, e.g ``.foo.test``. Defaults to ``None``."
msgstr "相対インポートを解決するためのパッケージ名。 ``.hoge.test`` のように、相対パスを使用してエクステンションを再読込する場合に必要です。デフォルトは ``None`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.reload_extension:21
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.unload_extension:29
msgid "The extension was not loaded."
msgstr "拡張機能がもともとロードされていなかった場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.reload_extension:24
msgid "The extension setup function had an execution error."
msgstr "エクステンションセットアップ関数に、何らかの実行エラーが発生した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_check:1
msgid "Removes a global check from the bot."
msgstr "ボットからグローバルチェックを除去します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_check:3
msgid "This function is idempotent and will not raise an exception if the function is not in the global checks."
msgstr "この関数は冪等性を保持しており、関数がグローバルチェックに含まれていない場合でも例外が発生しません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_check:10
msgid "The function to remove from the global checks."
msgstr "グローバルチェックから除去する関数。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_check:11
msgid "If the function was added with ``call_once=True`` in the :meth:`.Bot.add_check` call or using :meth:`.check_once`."
msgstr ":meth:`.Bot.add_check` によって ``call_once=True`` を指定して関数を追加していた場合、または :meth:`.check_once` を使用して関数を追加していた場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:3
msgid "Removes a cog from the bot and returns it."
msgstr "ボットからコグを除去し、それを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:5
msgid "All registered commands and event listeners that the cog has registered will be removed as well."
msgstr "これにより、コグが登録したすべての登録済みのコマンドと、コグに登録されているイベントリスナーも全て削除されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:8
msgid "If no cog is found then this method has no effect."
msgstr "コグが見つからない場合、このメソッドによる影響はありません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:18
msgid "The name of the cog to remove."
msgstr "削除するコグの名前。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:20
msgid "If the cog is an application command group, then this would be the guild where the cog group would be removed from. If not given then a global command is removed instead instead."
msgstr "コグがアプリケーションコマンドグループの場合、これはコググループが除去されるギルドになります。 \n"
"与えられない場合はグローバルコマンドになります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:26
msgid "If the cog is an application command group, then this would be the guilds where the cog group would be removed from. If not given then a global command is removed instead instead. Cannot be mixed with ``guild``."
msgstr "コグがアプリケーションコマンドグループの場合、これはコググループが除去されるギルドになります。 \n"
"与えられない場合はグローバルコマンドになります。\n"
"``guild`` と併用することはできません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:34
msgid "The cog that was removed. ``None`` if not found."
msgstr "除去されたコグ。見つからない場合は ``None`` 。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_cog:35
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.Context.cog:3
msgid "Optional[:class:`.Cog`]"
msgstr "Optional[:class:`.Cog`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_command:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.remove_command:1
msgid "Remove a :class:`.Command` from the internal list of commands."
msgstr "内部のコマンドリストから該当するコマンドを検索し、その :class:`.Command` を除去します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_command:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:4
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.remove_command:4
msgid "This could also be used as a way to remove aliases."
msgstr "これはコマンドのエイリアスによって、コマンドを除去することもできます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_command:10
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:10
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:10
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.remove_command:10
msgid "The name of the command to remove."
msgstr "除去するコマンドの名前。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_command:13
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:13
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:13
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.remove_command:13
msgid "The command that was removed. If the name is not valid then ``None`` is returned instead."
msgstr "除去されたコマンド。有効な名前ではない場合は ``None`` が返されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_command:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:15
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.remove_command:15
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.remove_command:15
msgid "Optional[:class:`.Command`]"
msgstr "Optional[:class:`.Command`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_listener:1
msgid "Removes a listener from the pool of listeners."
msgstr "リスナープールからリスナーを除去します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_listener:7
msgid "The function that was used as a listener to remove."
msgstr "除去するリスナーとして使用された関数。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.remove_listener:8
msgid "The name of the event we want to remove. Defaults to ``func.__name__``."
msgstr "除去したいイベントの名前。デフォルトでは ``func.__name__`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:1
msgid "A blocking call that abstracts away the event loop initialisation from you."
msgstr "イベントループの初期化を簡単に行うことができるブロッキングコール。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:4
msgid "If you want more control over the event loop then this function should not be used. Use :meth:`start` coroutine or :meth:`connect` + :meth:`login`."
msgstr "イベントループをより詳細に制御するには、この関数を使用しないでください。 :meth:`start` または :meth:`connect` + :meth:`login` を使用してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:8
msgid "This function also sets up the logging library to make it easier for beginners to know what is going on with the library. For more advanced users, this can be disabled by passing ``None`` to the ``log_handler`` parameter."
msgstr "またこの関数は、初心者がライブラリの仕組みを知ることが簡単にできるよう logging ライブラリを設定します。より高度なユーザーは、 ``log_handler`` に ``None`` を渡してこれを無効化することもできます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:15
msgid "This function must be the last function to call due to the fact that it is blocking. That means that registration of events or anything being called after this function call will not execute until it returns."
msgstr "この関数はブロッキングを行うため、必ず最後の方で呼び出してください。この関数を呼び出した後に書かれているイベントや関数は、Botが停止するまで実行されません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:27
msgid "The log handler to use for the library's logger. If this is ``None`` then the library will not set up anything logging related. Logging will still work if ``None`` is passed, though it is your responsibility to set it up."
msgstr "ライブラリロガーにて使用すべきログハンドラ。もしこれが ``None`` の場合ライブラリはログ関連のセットアップを一切行いません。 ``None`` を渡してもログは記録されますが、設定は自己責任となります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:32
msgid "The default log handler if not provided is :class:`logging.StreamHandler`."
msgstr "渡されない場合のデフォルトは :class:`logging.StreamHandler` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:36
msgid "The formatter to use with the given log handler. If not provided then it defaults to a colour based logging formatter (if available)."
msgstr "指定されたログハンドラで使用するフォーマッタ。渡されない場合は、既定で色ベースのログフォーマッタが使用されます (使用可能な場合)。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:41
msgid "The default log level for the library's logger. This is only applied if the ``log_handler`` parameter is not ``None``. Defaults to ``logging.INFO``."
msgstr "ライブラリロガーの既定のログレベル。これは ``log_handler`` が ``None`` でない場合のみ適用されます。デフォルトは ``logging.INFO`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:46
msgid "Whether to set up the root logger rather than the library logger. By default, only the library logger (``'discord'``) is set up. If this is set to ``True`` then the root logger is set up as well."
msgstr "ライブラリロガーではなくルートロガーを設定するかどうか。デフォルトではライブラリロガー (``'discord'``) のみが設定されています。 これが ``True`` に設定されている場合、ルートロガーも設定されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.run:50
msgid "Defaults to ``False``."
msgstr "デフォルトでは ``False`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.setup_hook:3
msgid "A coroutine to be called to setup the bot, by default this is blank."
msgstr "Botのセットアップ時に呼び出されるコルーチンです。デフォルトでは空白です"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.setup_hook:5
msgid "To perform asynchronous setup after the bot is logged in but before it has connected to the Websocket, overwrite this coroutine."
msgstr "Websocketに接続する前になにか非同期な関数を実行したい場合は、このコルーチンを上書きして下さい。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.setup_hook:8
msgid "This is only called once, in :meth:`login`, and will be called before any events are dispatched, making it a better solution than doing such setup in the :func:`~discord.on_ready` event."
msgstr ":meth:`login` で１回だけ呼ばれ、全てのイベントが発火する前に呼ばれます。 :func:`~discord.on_ready` より良いセットアップの方法です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.setup_hook:14
msgid "Since this is called *before* the websocket connection is made therefore anything that waits for the websocket will deadlock, this includes things like :meth:`wait_for` and :meth:`wait_until_ready`."
msgstr "これはWebSocket接続の *前に* 呼ばれるため、WebSocketを待つ関数の呼び出しはデッドロックします。これには :meth:`wait_for` や :meth:`wait_until_ready` が含まれます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.start:3
msgid "A shorthand coroutine for :meth:`login` + :meth:`connect`."
msgstr ":meth:`login` + :meth:`connect` を簡略化したコルーチン。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.start:14
msgid "An unexpected keyword argument was received."
msgstr "予期しないキーワード引数を受け取った場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.status:1
msgid ":class:`.Status`: The status being used upon logging on to Discord."
msgstr ":class:`.Status`: Discordへのログイン時に使用されるステータス。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.stickers:1
msgid "The stickers that the connected client has."
msgstr "接続したクライアントが持つスタンプ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.stickers:5
msgid "Sequence[:class:`.GuildSticker`]"
msgstr "Sequence[:class:`.GuildSticker`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.tree:1
msgid "The command tree responsible for handling the application commands in this bot."
msgstr "このボット内のアプリケーションコマンドを処理するコマンドツリー。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.tree:6
msgid ":class:`~discord.app_commands.CommandTree`"
msgstr ":class:`~discord.app_commands.CommandTree`"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.unload_extension:3
msgid "Unloads an extension."
msgstr "拡張機能をアンロードします。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.unload_extension:5
msgid "When the extension is unloaded, all commands, listeners, and cogs are removed from the bot and the module is un-imported."
msgstr "拡張機能がアンロードされると、すべてのコマンド、リスナー、コグがボットから除去され、モジュールはアンインポートされます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.unload_extension:8
msgid "The extension can provide an optional global function, ``teardown``, to do miscellaneous clean-up if necessary. This function takes a single parameter, the ``bot``, similar to ``setup`` from :meth:`~.Bot.load_extension`."
msgstr "拡張機能から提供される、オプションのグローバル関数 ``teardown`` によって、必要に応じてその他のクリーンアップを行うことができます。 この関数は、 :meth:`~.Bot.load_extension` の ``setup`` と似た、単一のパラメータを取ります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.unload_extension:17
msgid "The extension name to unload. It must be dot separated like regular Python imports if accessing a sub-module. e.g. ``foo.test`` if you want to import ``foo/test.py``."
msgstr "アンロードする拡張モジュール名。サブモジュールにアクセスする場合は、通常の Pythonのimportのように、ドットで区切られている必要があります。 例: ``foo/test.py`` をアンロードする場合、``foo.test`` と書いてください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.unload_extension:21
msgid "The package name to resolve relative imports with. This is required when unloading an extension using a relative path, e.g ``.foo.test``. Defaults to ``None``."
msgstr "相対インポートを解決するためのパッケージ名。例えば ``.foo.test`` のように、相対パスで拡張機能をアンロードするときに必要です。デフォルトでは ``None`` です。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.BotBase.unload_extension:28
msgid "The name of the extension could not     be resolved using the provided ``package`` parameter."
msgstr "渡された ``package`` パラメータを使用してエクステンションの名前を解決できなかった場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.user:1
msgid "Represents the connected client. ``None`` if not logged in."
msgstr "接続されたクライアントを表します。ログインしていない場合は ``None`` が返されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.user:3
msgid "Optional[:class:`.ClientUser`]"
msgstr "Optional[:class:`.ClientUser`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.users:1
msgid "Returns a list of all the users the bot can see."
msgstr "Botが見ることができるユーザーのリストを返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.users:3
msgid "List[:class:`~discord.User`]"
msgstr "List[:class:`~discord.User`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.voice_clients:1
msgid "Represents a list of voice connections."
msgstr "ボイス接続のリストを表します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.voice_clients:3
msgid "These are usually :class:`.VoiceClient` instances."
msgstr "これらは通常、 :class:`.VoiceClient` のインスタンスです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.Bot.voice_clients:5
msgid "List[:class:`.VoiceProtocol`]"
msgstr "List[:class:`.VoiceProtocol`]"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:3
msgid "Waits for a WebSocket event to be dispatched."
msgstr "WebSocketイベントがディスパッチされるまで待機します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:5
msgid "This could be used to wait for a user to reply to a message, or to react to a message, or to edit a message in a self-contained way."
msgstr "これは、ユーザーがメッセージに返信するのを待ったり、メッセージに反応したり、メッセージを自己完結的に編集したりするために使うことができます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:9
msgid "The ``timeout`` parameter is passed onto :func:`asyncio.wait_for`. By default, it does not timeout. Note that this does propagate the :exc:`asyncio.TimeoutError` for you in case of timeout and is provided for ease of use."
msgstr "``timeout`` パラメータは :func:`asyncio.wait_for` に渡されます。デフォルトではタイムアウトしません。タイムアウトした際に :exc:`asyncio.TimeoutError` が発生するのは、使いやすさを考慮したためです。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:14
msgid "In case the event returns multiple arguments, a :class:`tuple` containing those arguments is returned instead. Please check the :ref:`documentation <discord-api-events>` for a list of events and their parameters."
msgstr "指定されたイベントが複数の引数を返す場合は、代わりとしてその引数を含んだ :class:`tuple` が返ります。イベントとそのパラメータについては :ref:`documentation <discord-api-events>` を参照してください。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:19
msgid "This function returns the **first event that meets the requirements**."
msgstr "この関数は **条件を満たす最初のイベント** を返します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:23
msgid "Waiting for a user reply: ::"
msgstr "ユーザーからの返信メッセージを待つ場合、次のような書き方ができます: ::"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:37
msgid "Waiting for a thumbs up reaction from the message author: ::"
msgstr "また、メッセージ送信者が、サムズアップ リアクションを付けるのを待つ場合、次のようにも書けます: ::"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:57
msgid "``event`` parameter is now positional-only."
msgstr "``event`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:59
msgid "The event name, similar to the :ref:`event reference <discord-api-events>`, but without the ``on_`` prefix, to wait for."
msgstr "イベント名は :ref:`イベントリファレンス <discord-api-events>` に似ていますが、接頭詞の ``on_`` がありません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:62
msgid "A predicate to check what to wait for. The arguments must meet the parameters of the event being waited for."
msgstr "待っているものに該当するかを確認する関数。引数は、待機しているイベントの返り値を満たしている必要があります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:65
msgid "The number of seconds to wait before timing out and raising :exc:`asyncio.TimeoutError`."
msgstr "タイムアウトして :exc:`asyncio.TimeoutError` が送出されるまでの秒数。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:69
msgid "If a timeout is provided and it was reached."
msgstr "タイムアウトが設定されていて、かつその時間が経過した場合。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_for:71
msgid "Returns no arguments, a single argument, or a :class:`tuple` of multiple arguments that mirrors the parameters passed in the :ref:`event reference <discord-api-events>`."
msgstr "単一の引数、あるいは :ref:`イベントリファレンス <discord-api-events>` の返り値を反映した、複数の引数の値を含む :class:`tuple` が返ります。返り値がない場合もあります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_until_ready:3
msgid "Waits until the client's internal cache is all ready."
msgstr "クライアントの内部キャッシュの準備が完了するまで待機します。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.client.Client.wait_until_ready:7
msgid "Calling this inside :meth:`setup_hook` can lead to a deadlock."
msgstr ":meth:`setup_hook` の内部でこれを呼び出すと、デッドロックになる可能性があります。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:1
msgid "An iterator that recursively walks through all commands and subcommands."
msgstr "すべてのコマンドとサブコマンドを、再帰的に網羅するイテレータ。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:3
msgid "Duplicates due to aliases are no longer returned"
msgstr "エイリアスによって重複した場合は、そのエイリアスまたはコマンドは返しません。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:6
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:6
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:6
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.GroupMixin.walk_commands:6
msgid "Union[:class:`.Command`, :class:`.Group`] -- A command or group from the internal list of commands."
msgstr "Union[:class:`.Command`, :class:`.Group`] -- コマンドの内部リストからの、コマンドまたはグループ。"

#: ../../ext/commands/api.rst:54
msgid "AutoShardedBot"
msgstr "AutoShardedBot"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.AutoShardedBot:1
msgid "This is similar to :class:`.Bot` except that it is inherited from :class:`discord.AutoShardedClient` instead."
msgstr "これは、 :class:`discord.AutoShardedClient` から代わりに継承されていることを除いて、 :class:`.Bot` と似ています。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.AutoShardedBot:8
msgid "Asynchronously initialises the bot and automatically cleans."
msgstr "非同期的にボットを初期化し自動でクリーンアップします。"

#: ../../ext/commands/api.rst:62
msgid "Prefix Helpers"
msgstr "接頭辞のヘルパー"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.when_mentioned:1
msgid "A callable that implements a command prefix equivalent to being mentioned."
msgstr "メンションされるのと同等のコマンドの接頭辞を実装する呼び出し可能オブジェクト。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.when_mentioned:3
#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.when_mentioned_or:3
msgid "These are meant to be passed into the :attr:`.Bot.command_prefix` attribute."
msgstr "これらは :attr:`.Bot.command_prefix` 属性に渡されます。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.when_mentioned:7
msgid "``bot`` and ``msg`` parameters are now positional-only."
msgstr "``bot`` と ``msg`` のパラメータは位置指定のみになりました。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.when_mentioned_or:1
msgid "A callable that implements when mentioned or other prefixes provided."
msgstr "上記または他の接頭辞が提供されたときの接頭辞を実装する呼び出し可能オブジェクト。"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.when_mentioned_or:13
msgid "This callable returns another callable, so if this is done inside a custom callable, you must call the returned callable, for example:"
msgstr "この呼び出し可能オブジェクトは別の呼び出し可能オブジェクトを返すため、カスタム呼び出し可能オブジェクト内でこれが行われる場合は、返される呼び出し可能オブジェクトを必ず呼び出さなければなりません。例:"

#: ../../../discord/ext/commands/bot.py:docstring of discord.ext.commands.bot.when_mentioned_or:23
msgid ":func:`.when_mentioned`"
msgstr ":func:`.when_mentioned`"

#: ../../ext/commands/api.rst:71
msgid "Event Reference"
msgstr "イベントリファレンス"

#: ../../ext/commands/api.rst:73
msgid "These events function similar to :ref:`the regular events <discord-api-events>`, except they are custom to the command extension module."
msgstr "これらのイベントは、コマンド拡張モジュールのカスタム関数であることを除けば、 :ref:`通常のイベント <discord-api-events>` に似ています。"

#: ../../ext/commands/api.rst:78
msgid "An error handler that is called when an error is raised inside a command either through user input error, check failure, or an error in your own code."
msgstr "ユーザーによる入力エラー、チェックの失敗、またはコードの記述ミスによって、コマンド内でエラーが発生したときに呼び出されるエラーハンドラ。"

#: ../../ext/commands/api.rst:82
msgid "A default one is provided (:meth:`.Bot.on_command_error`)."
msgstr "デフォルトは (:meth:`.Bot.on_command_error`) です。"

#: ../../ext/commands/api.rst:84
#: ../../ext/commands/api.rst:96
#: ../../ext/commands/api.rst:106
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_before_invoke:9
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_after_invoke:9
msgid "The invocation context."
msgstr "呼び出しコンテキスト。"

#: ../../ext/commands/api.rst:86
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.on_help_command_error:17
msgid "The error that was raised."
msgstr "発生したエラー。"

#: ../../ext/commands/api.rst:91
msgid "An event that is called when a command is found and is about to be invoked."
msgstr "該当するコマンドが見つかり、呼び出されようとするときに、呼び出されるイベント。"

#: ../../ext/commands/api.rst:93
msgid "This event is called regardless of whether the command itself succeeds via error or completes."
msgstr "このイベントは、エラーまたは完了を介して、コマンド自体が成功するかどうかに関係なく、必ず呼び出されます。"

#: ../../ext/commands/api.rst:101
msgid "An event that is called when a command has completed its invocation."
msgstr "コマンドの呼び出しが完了したときに呼び出されるイベント。"

#: ../../ext/commands/api.rst:103
msgid "This event is called only if the command succeeded, i.e. all checks have passed and the user input it correctly."
msgstr "このイベントは、コマンドの実行が成功した場合にのみ呼び出されます。すなわち、すべてのチェックが成功し、ユーザーによって正しく入力された場合にのみ実行されます。"

#: ../../ext/commands/api.rst:112
msgid "Commands"
msgstr "コマンド"

#: ../../ext/commands/api.rst:115
msgid "Decorators"
msgstr "デコレータ"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.command:1
msgid "A decorator that transforms a function into a :class:`.Command` or if called with :func:`.group`, :class:`.Group`."
msgstr "関数を :class:`.Command` 、または :func:`.group` で呼び出した場合には :class:`.Group` に変換するデコレータです。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.command:4
msgid "By default the ``help`` attribute is received automatically from the docstring of the function and is cleaned up with the use of ``inspect.cleandoc``. If the docstring is ``bytes``, then it is decoded into :class:`str` using utf-8 encoding."
msgstr "デフォルトでは、 ``help`` 属性は、関数の docstring から自動的に取得したものを ``inspect.cleandoc`` を使用してクリーンアップされたものを使用します。 docstring が ``bytes`` の場合、utf-8 エンコーディングを使って :class:`str` にデコードされます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.command:9
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:15
msgid "All checks added using the :func:`.check` & co. decorators are added into the function. There is no way to supply your own checks through this decorator."
msgstr ":func:`.check` & co.デコレータを使用して追加された、すべてのチェックを機能に追加されます。 このデコレータを通さずして、独自のチェックを提供する以外の方法はありません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.command:13
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:21
msgid "The name to create the command with. By default this uses the function name unchanged."
msgstr "コマンドの名前。デフォルトでは関数名をそのまま使用します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.command:16
msgid "The class to construct with. By default this is :class:`.Command`. You usually do not change this."
msgstr "構築するクラス。デフォルトでは、 :class:`.Command` です。通常これは変更しません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.command:18
msgid "Keyword arguments to pass into the construction of the class denoted by ``cls``."
msgstr "``cls`` で指定されたクラスの構築時に渡すキーワード引数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.command:21
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:29
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_group:12
msgid "If the function is not a coroutine or is already a command."
msgstr "関数がコルーチンでない場合、またはすでにコマンドが登録されている場合。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.group:1
msgid "A decorator that transforms a function into a :class:`.Group`."
msgstr "関数を :class:`.Group` に変換するデコレータ。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.group:3
msgid "This is similar to the :func:`~discord.ext.commands.command` decorator but the ``cls`` parameter is set to :class:`Group` by default."
msgstr "これは :func:`~discord.ext.commands.command` デコレータに似ていますが、デフォルトでは引数の ``cls`` は :class:`Group` に設定されています。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.group:6
msgid "The ``cls`` parameter can now be passed."
msgstr "``cls`` パラメータを渡せるようになりました。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:1
msgid "A decorator that transforms a function into a :class:`.HybridCommand`."
msgstr "関数を :class:`.HybridCommand` に変換するデコレータ。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:3
msgid "A hybrid command is one that functions both as a regular :class:`.Command` and one that is also a :class:`app_commands.Command <discord.app_commands.Command>`."
msgstr "ハイブリッドコマンドは、通常の :class:`.Command` と :class:`app_commands.Command <discord.app_commands.Command>` の両方として機能するコマンドです。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:6
msgid "The callback being attached to the command must be representable as an application command callback. Converters are silently converted into a :class:`~discord.app_commands.Transformer` with a :attr:`discord.AppCommandOptionType.string` type."
msgstr "コマンドのコールバックはアプリケーションコマンドコールバックとして表現できるものでないといけません。コンバーターは :attr:`discord.AppCommandOptionType.string` 型の :class:`~discord.app_commands.Transformer` に暗黙的に変換されます。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:11
msgid "Checks and error handlers are dispatched and called as-if they were commands similar to :class:`.Command`. This means that they take :class:`Context` as a parameter rather than :class:`discord.Interaction`."
msgstr "チェックとエラーハンドラは、 :class:`.Command` のようなコマンドであるかのように呼び出されます。つまり、パラメータには :class:`discord.Interaction` ではなく :class:`Context` を取ります。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:24
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_group:9
msgid "Whether to register the command also as an application command."
msgstr "アプリケーションコマンドとしてもコマンドを登録するかどうか。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_command:26
msgid "Keyword arguments to pass into the construction of the hybrid command."
msgstr "ハイブリッドコマンドの構築時に渡すキーワード引数。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_group:1
msgid "A decorator that transforms a function into a :class:`.HybridGroup`."
msgstr "関数を :class:`.HybridGroup` に変換するデコレータ。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_group:3
msgid "This is similar to the :func:`~discord.ext.commands.group` decorator except it creates a hybrid group instead."
msgstr "これは :func:`~discord.ext.commands.group` デコレータに似ていますが、代わりにハイブリッドグループを作成します。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.hybrid_group:6
msgid "The name to create the group with. By default this uses the function name unchanged."
msgstr ""

#: ../../ext/commands/api.rst:131
msgid "Command"
msgstr "Command"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:1
msgid "A class that implements the protocol for a bot text command."
msgstr "Botのテキストコマンドのプロトコルを実装するクラス。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand:8
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup:10
msgid "These are not created manually, instead they are created via the decorator or functional interface."
msgstr "これらは手動では作成されず、デコレータまたは機能インターフェースを介して作成されます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:8
msgid "The name of the command."
msgstr "コマンドの名前。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:14
msgid "The coroutine that is executed when the command is called."
msgstr "コマンドが呼び出されたときに実行されるコルーチン。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:16
msgid ":ref:`coroutine <coroutine>`"
msgstr ":ref:`コルーチン <coroutine>`"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:20
msgid "The long help text for the command."
msgstr "コマンドに関する、長いヘルプテキスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:22
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:28
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:34
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.cog_name:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.cog_name:3
msgid "Optional[:class:`str`]"
msgstr "Optional[:class:`str`]"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:26
msgid "The short help text for the command."
msgstr "コマンドに関する、短いヘルプテキスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:32
msgid "A replacement for arguments in the default help text."
msgstr "デフォルトのヘルプ テキストを置き換えるための引数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:38
msgid "The list of aliases the command can be invoked under."
msgstr "そのコマンドを呼び出すことができるエイリアスのリスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:40
msgid "Union[List[:class:`str`], Tuple[:class:`str`]]"
msgstr "Union[List[:class:`str`], Tuple[:class:`str`]]"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:44
msgid "A boolean that indicates if the command is currently enabled. If the command is invoked while it is disabled, then :exc:`.DisabledCommand` is raised to the :func:`.on_command_error` event. Defaults to ``True``."
msgstr "コマンドが、現在、有効かどうかを示すbool値。コマンドが無効化されている状態で呼び出された場合、:exc:`.DisabledCommand` が :func:`.on_command_error` にて発生します。デフォルトは ``True`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:53
msgid "The parent group that this command belongs to. ``None`` if there isn't one."
msgstr "このコマンドが属する親コマンド。 親コマンドが存在しない場合、``None`` が返ります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:56
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.root_parent:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.root_parent:7
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.root_parent:7
msgid "Optional[:class:`Group`]"
msgstr "Optional[:class:`Group`]"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:60
msgid "The cog that this command belongs to. ``None`` if there isn't one."
msgstr "このコマンドが属するコグ。 属するコグが存在しない場合、``None`` が返ります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:66
msgid "A list of predicates that verifies if the command could be executed with the given :class:`.Context` as the sole parameter. If an exception is necessary to be thrown to signal failure, then one inherited from :exc:`.CommandError` should be used. Note that if the checks fail then :exc:`.CheckFailure` exception is raised to the :func:`.on_command_error` event."
msgstr "与えられた :class:`.Context` を唯一のパラメータとしてコマンドが実行できるかどうかを確認するチェック関数のリストです。もし失敗を知らせるために例外を発生させる必要がある場合には、 :exc:`.CommandError` から継承したものを使用する必要があります。もしチェックに失敗した場合には、 :func:`.on_command_error` イベントで :exc:`.CheckFailure` 例外が発生することに注意してください。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:73
msgid "List[Callable[[:class:`.Context`], :class:`bool`]]"
msgstr "List[Callable[[:class:`.Context`], :class:`bool`]]"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:77
msgid "The message prefixed into the default help command."
msgstr "デフォルトのヘルプコマンドの前に表示されるメッセージ。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:83
msgid "If ``True``\\, the default help command does not show this in the help output."
msgstr "``True`` の場合、デフォルトのヘルプコマンドではヘルプ出力に表示されません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:90
msgid "If ``False`` and a keyword-only argument is provided then the keyword only argument is stripped and handled as if it was a regular argument that handles :exc:`.MissingRequiredArgument` and default values in a regular matter rather than passing the rest completely raw. If ``True`` then the keyword-only argument will pass in the rest of the arguments in a completely raw matter. Defaults to ``False``."
msgstr "もし ``False`` でキーワードのみの引数を指定した場合には、キーワードのみの引数は取り除かれて、残りを完全に生のまま渡すのではなく、 :exc:`.MissingRequiredArgument` やデフォルト値を通常の引数として扱うように処理されます。もし ``True`` ならば、キーワードのみの引数は、残りの引数を完全に生のまま渡します。デフォルトは ``False`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:101
msgid "The subcommand that was invoked, if any."
msgstr "存在する場合、呼び出されたサブコマンド。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:107
msgid "If ``True`` and a variadic positional argument is specified, requires the user to specify at least one argument. Defaults to ``False``."
msgstr "もし ``True`` で可変長の位置引数が指定された場合、少なくとも1つの引数を指定するようユーザーに要求します。デフォルトは ``False`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:116
msgid "If ``True``\\, ignores extraneous strings passed to a command if all its requirements are met (e.g. ``?foo a b c`` when only expecting ``a`` and ``b``). Otherwise :func:`.on_command_error` and local error handlers are called with :exc:`.TooManyArguments`. Defaults to ``True``."
msgstr "もし ``True`` ならば、コマンドの要求がすべて満たされていれば、余計な文字列を無視します (例: ``?foo a b c`` で ``a`` と ``b`` しか期待できない場合)。そうでなければ :func:`.on_command_error` とローカルのエラーハンドラが :exc:`.TooManyArguments` と共に呼び出されます。デフォルトは ``True`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:125
msgid "If ``True``\\, cooldown processing is done after argument parsing, which calls converters. If ``False`` then cooldown processing is done first and then the converters are called second. Defaults to ``False``."
msgstr "もし ``True`` ならば、引数解析の後にクールダウン処理が行われ、コンバータが呼び出されます。 ``False`` の場合は、クールダウン処理が最初に行われ、その後にコンバータが呼ばれます。デフォルトは ``False`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:133
msgid "A dict of user provided extras to attach to the Command."
msgstr "コマンドに添付するユーザー提供のオマケの辞書。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:136
msgid "This object may be copied by the library."
msgstr "このオブジェクトはライブラリによってコピーされることがあります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command:138
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin:9
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:65
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:112
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:44
msgid ":class:`dict`"
msgstr ":class:`dict`"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.after_invoke:9
msgid "See :meth:`.Bot.after_invoke` for more info."
msgstr "詳細は :meth:`.Bot.after_invoke` を参照してください。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.before_invoke:9
msgid "See :meth:`.Bot.before_invoke` for more info."
msgstr "詳細は :meth:`.Bot.before_invoke` を参照してください。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:1
msgid "A decorator that registers a coroutine as a local error handler."
msgstr "コルーチンをローカルエラーハンドラとして登録するデコレータです。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:3
msgid "A local error handler is an :func:`.on_command_error` event limited to a single command. However, the :func:`.on_command_error` is still invoked afterwards as the catch-all."
msgstr "ローカルエラーハンドラは、一つのコマンドに限定された :func:`.on_command_error` イベントです。しかし、キャッチオールとして :func:`.on_command_error` イベントはその後も呼び出されます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:11
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:11
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:11
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.error:11
msgid "The coroutine to register as the local error handler."
msgstr "ローカルエラーハンドラとして登録するコルーチン。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.add_check:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.add_check:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.add_check:1
msgid "Adds a check to the command."
msgstr "コマンドにチェックを追加します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.add_check:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.add_check:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.add_check:3
msgid "This is the non-decorator interface to :func:`.check`."
msgstr "これは :func:`.check` に対する非デコレーターインターフェイスです。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.add_check:13
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.add_check:13
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.add_check:13
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.add_check:11
msgid "The function that will be used as a check."
msgstr "チェックとして使用される関数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.remove_check:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.remove_check:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.remove_check:1
msgid "Removes a check from the command."
msgstr "コマンドからチェックを削除します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.remove_check:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.remove_check:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.remove_check:3
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.remove_check:3
msgid "This function is idempotent and will not raise an exception if the function is not in the command's checks."
msgstr "この関数は冪等性を保持しており、関数がグローバルチェックに含まれていない場合でも例外が発生しません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.remove_check:12
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.remove_check:12
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.remove_check:12
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.remove_check:12
msgid "The function to remove from the checks."
msgstr "チェックから除去する関数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.update:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.update:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.update:1
msgid "Updates :class:`Command` instance with updated attribute."
msgstr ":class:`Command` インスタンスを更新された属性で更新します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.update:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.update:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.update:3
msgid "This works similarly to the :func:`~discord.ext.commands.command` decorator in terms of parameters in that they are passed to the :class:`Command` or subclass constructors, sans the name and callback."
msgstr "これは :func:`~discord.ext.commands.command` デコレーターと同様に動作します。パラメータは :class:`Command` やサブクラスのコンストラクターに渡され、名前とコールバックは除かれます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.__call__:3
msgid "Calls the internal callback that the command holds."
msgstr "コマンドが保持する内部コールバックを呼び出します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.__call__:7
msgid "This bypasses all mechanisms -- including checks, converters, invoke hooks, cooldowns, etc. You must take care to pass the proper arguments and types to this function."
msgstr "これは、チェック、コンバーター、呼び出しフック、クールダウンなど、すべてのメカニズムを回避します。この関数には、適切な引数と型を渡すように注意してください。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.__call__:15
msgid "``context`` parameter is now positional-only."
msgstr "``ctx`` パラメータは位置指定のみになりました。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.copy:1
msgid "Creates a copy of this command."
msgstr "コマンドのコピーを作成します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.copy:3
msgid "A new instance of this command."
msgstr "このコマンドの新しいインスタンス。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.copy:4
msgid ":class:`Command`"
msgstr ":class:`Command`"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.clean_params:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.clean_params:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.clean_params:1
msgid "Dict[:class:`str`, :class:`Parameter`]: Retrieves the parameter dictionary without the context or self parameters."
msgstr "Dict[:class:`str`, :class:`Parameter`]: コンテキストまたは自己のパラメータなしでパラメータ辞書を取得します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.clean_params:4
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.clean_params:4
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.clean_params:4
msgid "Useful for inspecting signature."
msgstr "シグネチャーの検査に便利です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.cooldown:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.cooldown:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.cooldown:1
msgid "The cooldown of a command when invoked or ``None`` if the command doesn't have a registered cooldown."
msgstr "コマンドを実行したときのクールダウン。クールダウンが登録されていないコマンドの場合は ``None`` となります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.cooldown:6
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.cooldown:6
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.cooldown:6
msgid "Optional[:class:`~discord.app_commands.Cooldown`]"
msgstr "Optional[:class:`discord.app_commands.Cooldown`]"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.full_parent_name:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.full_parent_name:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.full_parent_name:1
msgid "Retrieves the fully qualified parent command name."
msgstr "完全修飾された親コマンド名を取得します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.full_parent_name:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.full_parent_name:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.full_parent_name:3
msgid "This the base command name required to execute it. For example, in ``?one two three`` the parent name would be ``one two``."
msgstr "これは実行に必要なベースコマンド名です。例えば、 ``?one two three`` では親名は ``one two`` になります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.parents:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.parents:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.parents:1
msgid "Retrieves the parents of this command."
msgstr "このコマンドの親を取得します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.parents:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.parents:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.parents:3
msgid "If the command has no parents then it returns an empty :class:`list`."
msgstr "親がない場合は、空の :class:`list` を返します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.parents:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.parents:5
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.parents:5
msgid "For example in commands ``?a b c test``, the parents are ``[c, b, a]``."
msgstr "例えば、``?a b c test`` では、親は ``[c, b, a]`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.parents:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.parents:9
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.parents:9
msgid "List[:class:`Group`]"
msgstr "List[:class:`Group`]"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.root_parent:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.root_parent:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.root_parent:1
msgid "Retrieves the root parent of this command."
msgstr "このコマンドの大元の親を取得します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.root_parent:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.root_parent:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.root_parent:3
msgid "If the command has no parents then it returns ``None``."
msgstr "親がない場合は、``None`` を返します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.root_parent:5
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.root_parent:5
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.root_parent:5
msgid "For example in commands ``?a b c test``, the root parent is ``a``."
msgstr "例えば、``?a b c test`` では、親は ``a`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.qualified_name:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.qualified_name:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.qualified_name:1
msgid "Retrieves the fully qualified command name."
msgstr "完全修飾されたコマンド名を取得します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.qualified_name:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.qualified_name:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.qualified_name:3
msgid "This is the full parent name with the command name as well. For example, in ``?one two three`` the qualified name would be ``one two three``."
msgstr "これは、コマンド名を含む完全な親名称です。例えば、 ``?one two three`` の場合、完全修飾名は ``one two three`` となります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:1
msgid "Checks whether the command is currently on cooldown."
msgstr "コマンドが現在クールダウン中であるかどうかを確認します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:7
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:7
msgid "The invocation context to use when checking the commands cooldown status."
msgstr "コマンドのクールダウン状況を確認するときに使用する呼び出しコンテキスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:10
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:10
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.is_on_cooldown:10
msgid "A boolean indicating if the command is on cooldown."
msgstr "コマンドがクールダウン中かを示すbool値。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.reset_cooldown:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.reset_cooldown:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.reset_cooldown:1
msgid "Resets the cooldown on this command."
msgstr "このコマンドのクールダウンをリセットします。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.reset_cooldown:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.reset_cooldown:7
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.reset_cooldown:7
msgid "The invocation context to reset the cooldown under."
msgstr "クールダウンをリセットするための呼び出しコンテキスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:1
msgid "Retrieves the amount of seconds before this command can be tried again."
msgstr "このコマンドを再試行できるまでの秒数を取得します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:9
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:9
msgid "The invocation context to retrieve the cooldown from."
msgstr "クールダウンをリセットするための呼び出しコンテキスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:12
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:12
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.get_cooldown_retry_after:12
msgid "The amount of time left on this command's cooldown in seconds. If this is ``0.0`` then the command isn't on cooldown."
msgstr "このコマンドのクールダウンの残り時間（秒）。これが ``0.0`` であれば、コマンドはクールダウン中ではありません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.has_error_handler:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.has_error_handler:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Command.has_error_handler:1
msgid ":class:`bool`: Checks whether the command has an error handler registered."
msgstr ":class:`bool`: コマンドにエラーハンドラが登録されているかどうかを確認します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.cog_name:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.cog_name:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.cog_name:1
msgid "The name of the cog this command belongs to, if any."
msgstr "このコマンドが属するコグの名前があれば、その名前。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.short_doc:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.short_doc:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.short_doc:1
msgid "Gets the \"short\" documentation of a command."
msgstr "コマンドの「短い」説明を取得します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.short_doc:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.short_doc:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.short_doc:3
msgid "By default, this is the :attr:`.brief` attribute. If that lookup leads to an empty string then the first line of the :attr:`.help` attribute is used instead."
msgstr "デフォルトでは、これは :attr:`brief` 属性です。属性値が空の文字列であれば、代わりに :attr:`help` 属性の最初の行が使われます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Command.signature:1
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.Group.signature:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.HybridGroup.signature:1
msgid "Returns a POSIX-like signature useful for help command output."
msgstr "POSIX のような書式テキストを返します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.can_run:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.can_run:3
msgid "Checks if the command can be executed by checking all the predicates inside the :attr:`~Command.checks` attribute. This also checks whether the command is disabled."
msgstr ":attr:`~Command.checks` 属性内のすべての関数をチェックすることで、コマンドが実行可能かどうかをチェックします。また、コマンドが無効かどうかもチェックします。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:7
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:7
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.can_run:7
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.can_run:7
msgid "Checks whether the command is disabled or not"
msgstr "コマンドが無効であるかどうかをチェックします。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:14
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:14
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.can_run:14
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.can_run:14
msgid "The ctx of the command currently being invoked."
msgstr "現在呼び出されているコマンドのコンテキスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:17
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:17
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.can_run:17
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.can_run:17
msgid "Any command error that was raised during a check call will be propagated     by this function."
msgstr "チェック中に発生したコマンドエラーは、この関数によってキャッチされます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:19
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Command.can_run:19
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.can_run:19
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.can_run:19
msgid "A boolean indicating if the command can be invoked."
msgstr "コマンドが呼び出し可能かどうかを示すbool値。"

#: ../../ext/commands/api.rst:150
msgid "Group"
msgstr "Group"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Group:1
msgid "A class that implements a grouping protocol for commands to be executed as subcommands."
msgstr "サブコマンドとして実行されるコマンド用のgroupingプロトコルを実装したクラス。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Group:4
msgid "This class is a subclass of :class:`.Command` and thus all options valid in :class:`.Command` are valid in here as well."
msgstr "このクラスは :class:`.Command` のサブクラスなので、 :class:`.Command` で使える関数も使えます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Group:9
msgid "Indicates if the group callback should begin parsing and invocation only if no subcommand was found. Useful for making it an error handling function to tell the user that no subcommand was found or to have different functionality in case no subcommand was found. If this is ``False``, then the group callback will always be invoked first. This means that the checks and the parsing dictated by its parameters will be executed. Defaults to ``False``."
msgstr "サブコマンドが見つからなかった場合にのみ、グループコールバックが解析と呼び出しを開始するかどうかを示します。 サブコマンドが見つからなかったこと、またはサブコマンドが見つからなかった場合に異なる機能を持つことをユーザに伝えるエラー処理関数にするのに便利です。 これが ``False`` の場合、グループコールバックは常に最初に呼び出されます。 つまり、パラメータによって決定されるチェックと解析が実行されます。デフォルトは ``False`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Group:22
msgid "Indicates if the group's commands should be case insensitive. Defaults to ``False``."
msgstr "グループのコマンドが大文字と小文字を区別する必要があるかどうかを示します。デフォルトは ``False`` です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Group.copy:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Group.copy:1
msgid "Creates a copy of this :class:`Group`."
msgstr "この :class:`Group` のコピーを作成します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Group.copy:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Group.copy:3
msgid "A new instance of this group."
msgstr "このグループの新しいインスタンス。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.Group.copy:4
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.core.Group.copy:4
msgid ":class:`Group`"
msgstr ":class:`Group`"

#: ../../ext/commands/api.rst:175
msgid "GroupMixin"
msgstr "GroupMixin"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin:1
msgid "A mixin that implements common functionality for classes that behave similar to :class:`.Group` and are allowed to register commands."
msgstr ":class:`.Group` と同様に動作し、コマンドを登録可能なクラスの共通機能を実装したミックスイン。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin:6
msgid "A mapping of command name to :class:`.Command` objects."
msgstr "コマンド名の :class:`.Command` オブジェクトへのマッピング。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.GroupMixin:13
msgid "Whether the commands should be case insensitive. Defaults to ``False``."
msgstr "コマンドの大文字小文字を区別するかどうか。デフォルトは ``False`` です。"

#: ../../ext/commands/api.rst:190
msgid "HybridCommand"
msgstr "HybridCommand"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand:1
msgid "A class that is both an application command and a regular text command."
msgstr "アプリケーションコマンドと通常のテキストコマンドの両方であるクラス。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand:3
msgid "This has the same parameters and attributes as a regular :class:`~discord.ext.commands.Command`. However, it also doubles as an :class:`application command <discord.app_commands.Command>`. In order for this to work, the callbacks must have the same subset that is supported by application commands."
msgstr "これは通常の :class:`~discord.ext.commands.Command` と同じパラメータや属性を有します。しかし、これは :class:`アプリケーションコマンド <discord.app_commands.Command>` としても動作します。このため、コールバックはアプリケーションコマンドでサポートされるものでないといけません。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.autocomplete:1
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.autocomplete:1
msgid "A decorator that registers a coroutine as an autocomplete prompt for a parameter."
msgstr "パラメータのオートコンプリートに使用されるコルーチンを登録するデコレータ。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.autocomplete:3
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.autocomplete:3
msgid "This is the same as :meth:`~discord.app_commands.Command.autocomplete`. It is only applicable for the application command and doesn't do anything if the command is a regular command."
msgstr "これは :meth:`~discord.app_commands.Command.autocomplete` と同じです。これはアプリケーションコマンドにのみ適用され、通常のコマンドでは何もしません。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.autocomplete:9
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.autocomplete:11
msgid "Similar to the :meth:`~discord.app_commands.Command.autocomplete` method, this takes :class:`~discord.Interaction` as a parameter rather than a :class:`Context`."
msgstr ":meth:`~discord.app_commands.Command.autocomplete` メソッドと同様に、 :class:`Context` ではなく :class:`~discord.Interaction` をパラメータとして取ります。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.autocomplete:12
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.autocomplete:14
msgid "The parameter name to register as autocomplete."
msgstr "オートコンプリートに登録するパラメータ名。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridCommand.autocomplete:15
#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.autocomplete:17
msgid "The coroutine passed is not actually a coroutine or     the parameter is not found or of an invalid type."
msgstr "渡されたコルーチンが実際にはコルーチンでない場合や、パラメータが見つからず、または無効な型であった場合。"

#: ../../ext/commands/api.rst:212
msgid "HybridGroup"
msgstr "HybridGroup"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup:1
msgid "A class that is both an application command group and a regular text group."
msgstr "アプリケーションコマンドグループと通常のテキストグループの両方であるクラス。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup:3
msgid "This has the same parameters and attributes as a regular :class:`~discord.ext.commands.Group`. However, it also doubles as an :class:`application command group <discord.app_commands.Group>`. Note that application commands groups cannot have callbacks associated with them, so the callback is only called if it's not invoked as an application command."
msgstr "これは通常の :class:`~discord.ext.commands.Group` と同じパラメータや属性を有します。しかし、これは :class:`アプリケーションコマンドグループ <discord.app_commands.Group>` としても動作します。なお、アプリケーションコマンドグループにはコールバックを付けることはできないため、コールバックはアプリケーションコマンドとして呼び出されていない場合のみに呼び出されます。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup:8
msgid "Hybrid groups will always have :attr:`Group.invoke_without_command` set to ``True``."
msgstr "ハイブリッドグループでは :attr:`Group.invoke_without_command` が常に ``True`` に設定されています。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup:17
msgid "The command name to use as a fallback for the application command. Since application command groups cannot be invoked, this creates a subcommand within the group that can be invoked with the given group callback. If ``None`` then no fallback command is given. Defaults to ``None``."
msgstr "アプリケーションコマンド用のフォールバックとして使用するコマンド名。アプリケーションコマンドグループを呼び出すことはできないため、これはグループ内に与えられたグループコールバックで呼び出すことのできるサブコマンドを作成します。 ``None`` の場合、フォールバックコマンドは指定されません。デフォルトは ``None`` です。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.autocomplete:7
msgid "This is only available if the group has a fallback application command registered."
msgstr "これは、フォールバックアプリケーションコマンドが登録されている場合にのみ使用できます。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.group:1
msgid "A shortcut decorator that invokes :func:`~discord.ext.commands.hybrid_group` and adds it to the internal command list via :meth:`~.GroupMixin.add_command`."
msgstr ":func:`~discord.ext.commands.hybrid_group` を呼び出し、 :meth:`~.GroupMixin.add_command` を介して内部コマンドリストに追加するショートカットデコレータ。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.add_command:1
msgid "Adds a :class:`.HybridCommand` into the internal list of commands."
msgstr ":class:`.HybridCommand` を内部のコマンドリストに追加します。"

#: ../../../discord/ext/commands/hybrid.py:docstring of discord.ext.commands.hybrid.HybridGroup.add_command:10
msgid "If the command passed is not a subclass of :class:`.HybridCommand`."
msgstr "渡されたコマンドが、 :class:`.HybridCommand` のサブクラスでない場合。"

#: ../../ext/commands/api.rst:243
msgid "Cogs"
msgstr "Cogs"

#: ../../ext/commands/api.rst:246
msgid "Cog"
msgstr "Cog"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog:1
msgid "The base class that all cogs must inherit from."
msgstr "すべてのコグが継承しなければならないベースクラス。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog:3
msgid "A cog is a collection of commands, listeners, and optional state to help group commands together. More information on them can be found on the :ref:`ext_commands_cogs` page."
msgstr "コグは、コマンドをまとめるのに役立つ、コマンド、リスナー、任意の状態の集まりです。 詳細については、 :ref:`ext_commands_cogs` ページを参照してください。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog:7
msgid "When inheriting from this class, the options shown in :class:`CogMeta` are equally valid here."
msgstr "このクラスを継承する場合、 :class:`CogMeta` に表示されているオプションもここで同様に有効です。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_commands:1
msgid "Returns the commands that are defined inside this cog."
msgstr "このコグ内で定義されているコマンドを返します。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_commands:3
msgid "This does *not* include :class:`discord.app_commands.Command` or :class:`discord.app_commands.Group` instances."
msgstr "これには :class:`discord.app_commands.Command` や :class:`discord.app_commands.Group` のインスタンスは *含まれません* 。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_commands:6
msgid "A :class:`list` of :class:`.Command`\\s that are defined inside this cog, not including subcommands."
msgstr "このコグ内で定義されているサブコマンドを含まない :class:`.Command` の :class:`list` 。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_commands:8
msgid "List[:class:`.Command`]"
msgstr "List[:class:`.Command`]"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_app_commands:1
msgid "Returns the app commands that are defined inside this cog."
msgstr "このコグ内で定義されているアプリケーションコマンドを返します。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_app_commands:3
msgid "A :class:`list` of :class:`discord.app_commands.Command`\\s and :class:`discord.app_commands.Group`\\s that are defined inside this cog, not including subcommands."
msgstr "このコグ内で定義されている、サブコマンドを除いた :class:`discord.app_commands.Command` と :class:`discord.app_commands.Group` の :class:`list` 。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_app_commands:5
msgid "List[Union[:class:`discord.app_commands.Command`, :class:`discord.app_commands.Group`]]"
msgstr "List[Union[:class:`discord.app_commands.Command`, :class:`discord.app_commands.Group`]]"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.Cog.qualified_name:1
msgid "Returns the cog's specified name, not the class name."
msgstr "クラス名ではなく、コグの指定した名前を返します。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.Cog.description:1
msgid "Returns the cog's description, typically the cleaned docstring."
msgstr "コグの説明を返します。通常、クリーンな docstring を返します。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.walk_commands:1
msgid "An iterator that recursively walks through this cog's commands and subcommands."
msgstr "このコグのすべてのコマンドとサブコマンドを、再帰的に網羅するイテレータ。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.walk_commands:3
msgid "Union[:class:`.Command`, :class:`.Group`] -- A command or group from the cog."
msgstr "Union[:class:`.Command`, :class:`.Group`] -- コグ内のコマンドまたはグループ。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.walk_app_commands:1
msgid "An iterator that recursively walks through this cog's app commands and subcommands."
msgstr "このコグのすべてのアプリケーションコマンドとサブコマンドを、再帰的に網羅するイテレータ。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.walk_app_commands:3
msgid "Union[:class:`discord.app_commands.Command`, :class:`discord.app_commands.Group`] -- An app command or group from the cog."
msgstr "Union[:class:`discord.app_commands.Command`, :class:`discord.app_commands.Group`] -- コグのアプリケーションコマンドまたはグループ。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.Cog.app_command:1
msgid "Returns the associated group with this cog."
msgstr "このコグに関連付けられたグループを返します。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.Cog.app_command:3
msgid "This is only available if inheriting from :class:`GroupCog`."
msgstr ":class:`GroupCog` から継承する場合にのみ使用できます。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.Cog.app_command:5
msgid "Optional[:class:`discord.app_commands.Group`]"
msgstr "Optional[:class:`discord.app_commands.Group`]"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_listeners:1
msgid "Returns a :class:`list` of (name, function) listener pairs that are defined in this cog."
msgstr "このコグで定義されている (コマンド名 , 関数) リスナーのペアの :class:`list` を返します。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_listeners:3
msgid "The listeners defined in this cog."
msgstr "このコグで定義されるリスナー。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.get_listeners:4
msgid "List[Tuple[:class:`str`, :ref:`coroutine <coroutine>`]]"
msgstr "List[Tuple[:class:`str`, :ref:`coroutine <coroutine>`]]"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.listener:1
msgid "A decorator that marks a function as a listener."
msgstr "関数をリスナーとしてマークするデコレータ。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.listener:3
msgid "This is the cog equivalent of :meth:`.Bot.listen`."
msgstr "これはコグの :meth:`.Bot.listen` に相当するものです。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.listener:5
msgid "The name of the event being listened to. If not provided, it defaults to the function's name."
msgstr "受信するイベント名です。指定されていない場合は、関数の名前がデフォルトで使われます。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.listener:9
msgid "The function is not a coroutine function or a string was not passed as     the name."
msgstr "関数がコルーチンではないか、文字列が名前として渡されていません。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.has_error_handler:1
msgid ":class:`bool`: Checks whether the cog has an error handler."
msgstr ":class:`bool`: コグにエラーハンドラが登録されているかどうかをチェックします。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.has_app_command_error_handler:1
msgid ":class:`bool`: Checks whether the cog has an app error handler."
msgstr ":class:`bool`: コグにアプリケーションエラーハンドラが登録されているかどうかをチェックします。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_load:1
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_unload:1
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_not_found:1
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:1
msgid "|maybecoro|"
msgstr "|maybecoro|"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_load:3
msgid "A special method that is called when the cog gets loaded."
msgstr "コグが読み込まれた際に呼び出される特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_load:5
msgid "Subclasses must replace this if they want special asynchronous loading behaviour. Note that the ``__init__`` special method does not allow asynchronous code to run inside it, thus this is helpful for setting up code that needs to be asynchronous."
msgstr "サブクラスは特別な非同期読み込み動作が必要な場合にこれを置き換えなければなりません。 特別な ``__init__`` メソッドは、その中で非同期コードを実行することを許可しないので、これは非同期な必要があるコードを設定することに役立つ、ということに注意してください。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_unload:3
msgid "A special method that is called when the cog gets removed."
msgstr "コグが削除された際に呼び出される特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_unload:5
msgid "Subclasses must replace this if they want special unloading behaviour."
msgstr "サブクラスは特別なアンロード動作が必要な場合にこれを置き換えなければなりません。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_unload:7
msgid "Exceptions raised in this method are ignored during extension unloading."
msgstr ""

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_unload:11
msgid "This method can now be a :term:`coroutine`."
msgstr "このメソッドは現在 :term:`coroutine` になりました。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.bot_check_once:1
msgid "A special method that registers as a :meth:`.Bot.check_once` check."
msgstr ":meth:`.Bot.check_once` チェックとして登録する特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.bot_check_once:4
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.bot_check:4
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_check:4
msgid "This function **can** be a coroutine and must take a sole parameter, ``ctx``, to represent the :class:`.Context`."
msgstr "この関数はコルーチンであり、 :class:`.Context` を表すために唯一のパラメータである ``ctx`` を取る必要があります。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.bot_check:1
msgid "A special method that registers as a :meth:`.Bot.check` check."
msgstr ":meth:`.Bot.check` チェックとして登録する特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_check:1
msgid "A special method that registers as a :func:`~discord.ext.commands.check` for every command and subcommand in this cog."
msgstr "このコグのすべてのコマンドとサブコマンドに対して :func:`~discord.ext.commands.check` として登録する特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.interaction_check:1
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.interaction_check:1
msgid "A special method that registers as a :func:`discord.app_commands.check` for every app command and subcommand in this cog."
msgstr ""

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.interaction_check:4
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.interaction_check:4
msgid "This function **can** be a coroutine and must take a sole parameter, ``interaction``, to represent the :class:`~discord.Interaction`."
msgstr ""

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_command_error:3
msgid "A special method that is called whenever an error is dispatched inside this cog."
msgstr "このコグ内でエラーが発生するたびに呼び出される特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_command_error:6
msgid "This is similar to :func:`.on_command_error` except only applying to the commands inside this cog."
msgstr "これは :func:`.on_command_error` に似ていますが、コグ内のコマンドにのみ適用されます。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_command_error:9
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_app_command_error:9
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_before_invoke:7
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_after_invoke:7
msgid "This **must** be a coroutine."
msgstr "**コルーチンでなければなりません**。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_command_error:11
msgid "The invocation context where the error happened."
msgstr "エラーが発生した呼び出しコンテキスト。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_command_error:13
msgid "The error that happened."
msgstr "発生したエラー"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_app_command_error:3
msgid "A special method that is called whenever an error within an application command is dispatched inside this cog."
msgstr "このコグ内でアプリケーションコマンドのエラーが発生するたびに呼び出される特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_app_command_error:6
msgid "This is similar to :func:`discord.app_commands.CommandTree.on_error` except only applying to the application commands inside this cog."
msgstr "これは :func:`discord.app_commands.CommandTree.on_error` に似ていますが、コグ内のアプリケーションコマンドにのみ適用されます。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_app_command_error:11
msgid "The interaction that is being handled."
msgstr "処理中のインタラクション。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_app_command_error:13
msgid "The exception that was raised."
msgstr "発生した例外。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_before_invoke:3
msgid "A special method that acts as a cog local pre-invoke hook."
msgstr "コグのローカル事前呼び出しフックとして機能する特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_before_invoke:5
msgid "This is similar to :meth:`.Command.before_invoke`."
msgstr "これは :meth:`.Command.before_invoke` に似ています。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_after_invoke:3
msgid "A special method that acts as a cog local post-invoke hook."
msgstr "コグのローカルポスト呼び出しフックとして機能する特別なメソッド。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.Cog.cog_after_invoke:5
msgid "This is similar to :meth:`.Command.after_invoke`."
msgstr "これは :meth:`.Command.after_invoke` に似ています。"

#: ../../ext/commands/api.rst:254
msgid "GroupCog"
msgstr "GroupCog"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.GroupCog:1
msgid "Represents a cog that also doubles as a parent :class:`discord.app_commands.Group` for the application commands defined within it."
msgstr "この中で定義されたアプリケーションコマンドの親 :class:`discord.app_commands.Group` としても機能するコグ。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.GroupCog:4
msgid "This inherits from :class:`Cog` and the options in :class:`CogMeta` also apply to this. See the :class:`Cog` documentation for methods."
msgstr "これは :class:`Cog` から継承され、 :class:`CogMeta` のオプションもこれに適用されます。メソッドについては :class:`Cog` ドキュメントを参照してください。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.GroupCog:7
msgid "Decorators such as :func:`~discord.app_commands.guild_only`, :func:`~discord.app_commands.guilds`, and :func:`~discord.app_commands.default_permissions` will apply to the group if used on top of the cog."
msgstr ":func:`~discord.app_commands.guild_only` 、 :func:`~discord.app_commands.guilds` 、 :func:`~discord.app_commands.default_permissions` のようなデコレータはコグの上に使用されている場合、グループに適用されます。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.GroupCog:11
msgid "Hybrid commands will also be added to the Group, giving the ability to categorize slash commands into groups, while keeping the prefix-style command as a root-level command."
msgstr ""

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.GroupCog:14
msgid "For example:"
msgstr "例："

#: ../../ext/commands/api.rst:263
msgid "CogMeta"
msgstr "CogMeta"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:1
msgid "A metaclass for defining a cog."
msgstr "コグを定義するメタクラス。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:3
msgid "Note that you should probably not use this directly. It is exposed purely for documentation purposes along with making custom metaclasses to intermix with other metaclasses such as the :class:`abc.ABCMeta` metaclass."
msgstr "これを直接使うべきではないということに注意してください。 これは、 :class:`abc.ABCMeta` のような他のメタクラスと相互に混在させるカスタムメタクラスとして説明する目的だけで公開されています。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:7
msgid "For example, to create an abstract cog mixin class, the following would be done."
msgstr "たとえば、抽象的なコグのミックスインのクラスを作成するには、次のようにします。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:24
msgid "When passing an attribute of a metaclass that is documented below, note that you must pass it as a keyword-only argument to the class creation like the following example:"
msgstr "以下に文書化されているメタクラスの属性を渡す場合、次の例のように、キーワードのみの引数としてクラスの作成に渡す必要があることに注意してください:"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:35
msgid "The cog name. By default, it is the name of the class with no modification."
msgstr "コグの名前。デフォルトではクラス名そのままになります。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:41
msgid "The cog description. By default, it is the cleaned docstring of the class."
msgstr "コグの説明。デフォルトでは、綺麗にされたクラスのdocstringになります。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:49
msgid "A list of attributes to apply to every command inside this cog. The dictionary is passed into the :class:`Command` options at ``__init__``. If you specify attributes inside the command attribute in the class, it will override the one specified inside this attribute. For example:"
msgstr "このコグ内のすべてのコマンドに適用される属性のリスト。 辞書は :class:`Command` オプションの ``__init__`` に渡されます。 クラス内の command 属性内で属性を指定すると、この属性内で指定された属性を上書きします。例えば:"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:69
msgid "The group name of a cog. This is only applicable for :class:`GroupCog` instances. By default, it's the same value as :attr:`name`."
msgstr "コグのグループ名。これは :class:`GroupCog` インスタンスにのみ適用されます。デフォルトでは、 :attr:`name` と同じ値です。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:74
#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:83
msgid "Union[:class:`str`, :class:`~discord.app_commands.locale_str`]"
msgstr "Union[:class:`str`, :class:`~discord.app_commands.locale_str`]"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:78
msgid "The group description of a cog. This is only applicable for :class:`GroupCog` instances. By default, it's the same value as :attr:`description`."
msgstr "コグのグループ説明。これは :class:`GroupCog` インスタンスにのみ適用されます。デフォルトでは、 :attr:`description` と同じ値です。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:87
msgid "Whether the application command group is NSFW. This is only applicable for :class:`GroupCog` instances. By default, it's ``False``."
msgstr "アプリケーションコマンドグループに年齢制限をかけるか。これは :class:`GroupCog` インスタンスにのみ適用されます。デフォルトでは ``False`` です。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:96
msgid "If this is set to ``True``, then all translatable strings will implicitly be wrapped into :class:`~discord.app_commands.locale_str` rather than :class:`str`. Defaults to ``True``."
msgstr "これが ``True`` に設定されている場合、翻訳可能な文字列は :class:`str` ではなく暗黙的に :class:`~discord.app_commands.locale_str` にラップされます。デフォルトは ``True`` です。"

#: ../../../discord/ext/commands/cog.py:docstring of discord.ext.commands.cog.CogMeta:106
msgid "A dictionary that can be used to store extraneous data. This is only applicable for :class:`GroupCog` instances. The library will not touch any values or keys within this dictionary."
msgstr "追加のデータを保管できる辞書型。これは :class:`GroupCog` インスタンスにのみ適用されます。ライブラリは辞書型の中のキーや値を一切操作しません。"

#: ../../ext/commands/api.rst:273
msgid "Help Commands"
msgstr "ヘルプコマンド"

#: ../../ext/commands/api.rst:276
msgid "HelpCommand"
msgstr "HelpCommand"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:1
msgid "The base implementation for help command formatting."
msgstr "ヘルプコマンドの書式設定のための基本的実装。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:5
msgid "Internally instances of this class are deep copied every time the command itself is invoked to prevent a race condition mentioned in :issue:`2123`."
msgstr "内部的にこのクラスのインスタンスは、 :issue:`2123` に記載されている競合状態を防ぐため、コマンド自体が呼び出されるたびにディープコピーされます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:9
msgid "This means that relying on the state of this class to be the same between command invocations would not work as expected."
msgstr "つまり、このクラスの状態に依存してコマンドの呼び出しが同じになると、期待どおりに動作しないということです。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:14
msgid "The context that invoked this help formatter. This is generally set after the help command assigned, :func:`command_callback`\\, has been called."
msgstr "このヘルプを整形するクラスを呼び出したコンテキストです。これは一般的にヘルプコマンドが割り当てられ、:func:`command_callback` が呼び出された後に設定されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:17
msgid "Optional[:class:`Context`]"
msgstr "Optional[:class:`Context`]"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:21
msgid "Specifies if hidden commands should be shown in the output. Defaults to ``False``."
msgstr "隠しコマンドを表示するかどうかを指定します。デフォルトでは ``False`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:28
msgid "Specifies if commands should have their :attr:`.Command.checks` called and verified. If ``True``, always calls :attr:`.Command.checks`. If ``None``, only calls :attr:`.Command.checks` in a guild setting. If ``False``, never calls :attr:`.Command.checks`. Defaults to ``True``."
msgstr "コマンドが、 :attr:`.Command.checks` を呼び出し検証すべきかを指定します。もし ``True`` なら、常に :attr:`.Command.checks` を呼び出します。もし ``None`` なら、ギルドでのみ :attr:`.Command.checks` を呼び出します。もし ``False`` なら、 :attr:`.Command.checks` は一切呼び出しません。デフォルトは ``True`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:35
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:29
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:34
msgid "Optional[:class:`bool`]"
msgstr "Optional[:class:`bool`]"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand:39
msgid "A dictionary of options to pass in for the construction of the help command. This allows you to change the command behaviour without actually changing the implementation of the command. The attributes will be the same as the ones passed in the :class:`.Command` constructor."
msgstr "ヘルプコマンドの構築のために渡すオプションの辞書。 これにより、実際にヘルプコマンドの実装を変更することなく、その動作を変更できます。 属性は :class:`.Command` コンストラクタで渡されたものと同じになります。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.add_check:1
msgid "Adds a check to the help command."
msgstr "ヘルプコマンドにチェックを追加します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.remove_check:1
msgid "Removes a check from the help command."
msgstr "ヘルプコマンドからチェックを除去します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_bot_mapping:1
msgid "Retrieves the bot mapping passed to :meth:`send_bot_help`."
msgstr ":meth:`send_bot_help` に渡されたボットのマッピングを取得します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.HelpCommand.invoked_with:1
msgid "Similar to :attr:`Context.invoked_with` except properly handles the case where :meth:`Context.send_help` is used."
msgstr ":meth:`Context.send_help` が使われた場合にちゃんと処理することを除いて、:attr:`Context.invoked_with` と同様です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.HelpCommand.invoked_with:4
msgid "If the help command was used regularly then this returns the :attr:`Context.invoked_with` attribute. Otherwise, if it the help command was called using :meth:`Context.send_help` then it returns the internal command name of the help command."
msgstr "ヘルプコマンドが標準的に使われたならば、これは :attr:`Context.invoked_with` を返します。\n"
"そうではなく、もしヘルプコマンドが :meth:`Context.send_help` を用いて呼び出されたなら、その時はヘルプコマンドの内部的なコマンド名を返します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.HelpCommand.invoked_with:9
msgid "The command name that triggered this invocation."
msgstr "この実行を引き起こしたコマンド名。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_command_signature:1
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_command_signature:1
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_command_signature:1
msgid "Retrieves the signature portion of the help page."
msgstr "ヘルプページに表示される、コマンドの使用方法を示す文字列を返します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_command_signature:7
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_command_signature:8
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_command_signature:7
msgid "The command to get the signature of."
msgstr "使用方法を取得したいコマンド。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_command_signature:10
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_command_signature:11
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_command_signature:10
msgid "The signature for the command."
msgstr "コマンドの使用方法を示す文字列。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.remove_mentions:1
msgid "Removes mentions from the string to prevent abuse."
msgstr "不正使用を防ぐために文字列からメンションを除去します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.remove_mentions:3
msgid "This includes ``@everyone``, ``@here``, member mentions and role mentions."
msgstr "これには ``@everyone`` 、 ``@here`` 、 メンバーメンションとロールメンションが含まれます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.remove_mentions:7
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_not_found:10
msgid "``string`` parameter is now positional-only."
msgstr "``string`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.remove_mentions:9
msgid "The string with mentions removed."
msgstr "メンションが除去された文字列。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.HelpCommand.cog:1
msgid "A property for retrieving or setting the cog for the help command."
msgstr "ヘルプコマンドのコグを取得または設定するためのプロパティ。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.HelpCommand.cog:3
msgid "When a cog is set for the help command, it is as-if the help command belongs to that cog. All cog special methods will apply to the help command and it will be automatically unset on unload."
msgstr "ヘルプコマンドにコグが設定されている場合は、ヘルプコマンドがそのコグに属している場合と同様です。 コグの特別なメソッドはすべてヘルプコマンドに適用され、アンロード時に自動的にアンロードされます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.HelpCommand.cog:7
msgid "To unbind the cog from the help command, you can set it to ``None``."
msgstr "コグをヘルプコマンドから解除するには、``None`` を設定します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.HelpCommand.cog:9
msgid "The cog that is currently set for the help command."
msgstr "ヘルプコマンドに現在設定されているコグ。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_not_found:3
msgid "A method called when a command is not found in the help command. This is useful to override for i18n."
msgstr "ヘルプコマンドでコマンドが見つからない場合に呼び出されるメソッド。これは、i18nでオーバーライドするのに便利です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_not_found:6
msgid "Defaults to ``No command called {0} found.``"
msgstr "デフォルトは ``No command called {0} found.`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_not_found:12
msgid "The string that contains the invalid command. Note that this has had mentions removed to prevent abuse."
msgstr "不正なコマンドを含む文字列。不正使用を防ぐためにメンションが削除されていることに注意してください。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_not_found:16
msgid "The string to use when a command has not been found."
msgstr "コマンドが見つからないときに使用する文字列。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:3
msgid "A method called when a command did not have a subcommand requested in the help command. This is useful to override for i18n."
msgstr "ヘルプコマンドでコマンドが要求されたサブコマンドを持っていない場合に呼び出されるメソッド。これは、i18nでオーバーライドするのに便利です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:6
msgid "Defaults to either:"
msgstr "デフォルトは以下のどちらかです:"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:8
msgid "``'Command \"{command.qualified_name}\" has no subcommands.'``"
msgstr "``'Command \"{command.qualified_name}\" has no subcommands.'``"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:9
msgid "If there is no subcommand in the ``command`` parameter."
msgstr "``command`` パラメータにサブコマンドがない場合。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:11
msgid "``'Command \"{command.qualified_name}\" has no subcommand named {string}'``"
msgstr "``'Command \"{command.qualified_name}\" has no subcommand named {string}'``"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:11
msgid "If the ``command`` parameter has subcommands but not one named ``string``."
msgstr "``command`` パラメータにサブコマンドはあるが、``string`` という名前のものがない場合。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:15
msgid "``command`` and ``string`` parameters are now positional-only."
msgstr "``command`` と ``string`` のパラメータは位置指定のみになりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:17
msgid "The command that did not have the subcommand requested."
msgstr "そのサブコマンドを持っていなかった要求されたコマンド。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:19
msgid "The string that contains the invalid subcommand. Note that this has had mentions removed to prevent abuse."
msgstr "不正なサブコマンドを含む文字列。不正使用を防ぐためにメンションが削除されていることに注意してください。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.subcommand_not_found:23
msgid "The string to use when the command did not have the subcommand requested."
msgstr "要求されたコマンドがそのサブコマンドを持っていなかった時に使われる文字列。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.filter_commands:3
msgid "Returns a filtered list of commands and optionally sorts them."
msgstr "フィルタリングされたコマンドのリストを返し、必要に応じて並び替えます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.filter_commands:5
msgid "This takes into account the :attr:`verify_checks` and :attr:`show_hidden` attributes."
msgstr "これは :attr:`verify_checks` と :attr:`show_hidden` 属性を考慮します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.filter_commands:10
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_max_size:5
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_indented_commands:10
msgid "``commands`` parameter is now positional-only."
msgstr "``commands`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.filter_commands:12
msgid "An iterable of commands that are getting filtered."
msgstr "フィルタリングされるコマンドのイテラブル。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.filter_commands:14
msgid "Whether to sort the result."
msgstr "結果を並び替えるかどうか。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.filter_commands:16
msgid "An optional key function to pass to :func:`py:sorted` that takes a :class:`Command` as its sole parameter. If ``sort`` is passed as ``True`` then this will default as the command name."
msgstr ":class:`Command` を唯一のパラメータとして取る、 :func:`py:sorted` に渡す任意のキー関数。 ``sort`` が ``True`` として渡された場合、これはコマンド名としてデフォルトになります。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.filter_commands:21
msgid "A list of commands that passed the filter."
msgstr "フィルタを通過したコマンドのリスト。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.filter_commands:22
msgid "List[:class:`Command`]"
msgstr "List[:class:`Command`]"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_max_size:1
msgid "Returns the largest name length of the specified command list."
msgstr "指定されたコマンドリストの中の名前の最大の長さを返します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_max_size:7
msgid "A sequence of commands to check for the largest size."
msgstr "最大の長さをチェックするコマンドのシーケンス。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_max_size:10
msgid "The maximum width of the commands."
msgstr "コマンドの最大の幅。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_max_size:11
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:12
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:42
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator:25
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.Context.filesize_limit:5
msgid ":class:`int`"
msgstr ":class:`int`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_destination:1
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_destination:1
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_destination:1
msgid "Returns the :class:`~discord.abc.Messageable` where the help command will be output."
msgstr "ヘルプコマンドが出力される :class:`~discord.abc.Messageable` を返します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_destination:3
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_error_message:6
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_bot_help:11
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_cog_help:11
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_group_help:11
msgid "You can override this method to customise the behaviour."
msgstr "このメソッドをオーバーライドして動作をカスタマイズできます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_destination:5
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_destination:5
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_destination:5
msgid "By default this returns the context's channel."
msgstr "デフォルトでは、コンテキストのチャンネルを返します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_destination:7
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_destination:7
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_destination:7
msgid "The destination where the help command will be output."
msgstr "ヘルプコマンドの出力先。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.get_destination:8
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_destination:8
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_destination:8
msgid ":class:`.abc.Messageable`"
msgstr ":class:`.abc.Messageable`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_error_message:3
msgid "Handles the implementation when an error happens in the help command. For example, the result of :meth:`command_not_found` will be passed here."
msgstr "ヘルプコマンドでエラーが発生したときにその実装を処理します。例えば、 :meth:`command_not_found` の結果がここに渡されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_error_message:8
msgid "By default, this sends the error message to the destination specified by :meth:`get_destination`."
msgstr "デフォルトでは、これは :meth:`get_destination` で指定された宛先にエラーメッセージを送信します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_error_message:13
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_bot_help:15
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_cog_help:15
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_group_help:15
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:14
msgid "You can access the invocation context with :attr:`HelpCommand.context`."
msgstr ":attr:`HelpCommand.context` で呼び出しコンテキストにアクセスできます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_error_message:17
msgid "``error`` parameter is now positional-only."
msgstr "``error`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_error_message:19
msgid "The error message to display to the user. Note that this has had mentions removed to prevent abuse."
msgstr "ユーザーに表示されるエラーメッセージ。これは、メンションが不正使用を防ぐために削除されていることに注意してください。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.on_help_command_error:3
msgid "The help command's error handler, as specified by :ref:`ext_commands_error_handler`."
msgstr "ヘルプコマンドのエラーハンドラは :ref:`ext_commands_error_handler` で指定されています。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.on_help_command_error:5
msgid "Useful to override if you need some specific behaviour when the error handler is called."
msgstr "エラーハンドラが呼び出されたときに特定の動作が必要な場合にオーバーライドするのに便利です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.on_help_command_error:8
msgid "By default this method does nothing and just propagates to the default error handlers."
msgstr "デフォルトでは、このメソッドは何もせず、単にデフォルトのエラーハンドラに伝播します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.on_help_command_error:13
msgid "``ctx`` and ``error`` parameters are now positional-only."
msgstr "``bot`` と ``msg`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_bot_help:3
msgid "Handles the implementation of the bot command page in the help command. This function is called when the help command is called with no arguments."
msgstr "ボットのコマンドページの実装をヘルプコマンドで処理します。この関数は、引数なしでヘルプコマンドが呼び出されたときに呼び出されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_bot_help:6
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_cog_help:6
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_group_help:6
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:5
msgid "It should be noted that this method does not return anything -- rather the actual message sending should be done inside this method. Well behaved subclasses should use :meth:`get_destination` to know where to send, as this is a customisation point for other users."
msgstr "このメソッドは何も返さないことに注意してください -- むしろ実際のメッセージ送信はこのメソッド内で行う必要があります。 適切な振る舞いをするサブクラスは、他のユーザ向けにカスタマイズする箇所であるため、どこに送信するかを知るために :meth:`get_destination` を使用する必要があります。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_bot_help:17
msgid "Also, the commands in the mapping are not filtered. To do the filtering you will have to call :meth:`filter_commands` yourself."
msgstr "また、マッピング内のコマンドはフィルタリングされません。フィルタリングを行うには、 :meth:`filter_commands` を自分で呼び出す必要があります。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_bot_help:22
msgid "``mapping`` parameter is now positional-only."
msgstr "``mapping`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_bot_help:24
msgid "A mapping of cogs to commands that have been requested by the user for help. The key of the mapping is the :class:`~.commands.Cog` that the command belongs to, or ``None`` if there isn't one, and the value is a list of commands that belongs to that cog."
msgstr "ヘルプのためにユーザーから要求されたコマンドへのコグのマッピング。マッピングのキーはコマンドが属する :class:`~.commands.Cog` です。値がない場合は ``None`` になり、そのコグに属するコマンドのリストになります。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_cog_help:3
msgid "Handles the implementation of the cog page in the help command. This function is called when the help command is called with a cog as the argument."
msgstr "ヘルプコマンドでコグページの実装を処理します。 この関数は、ヘルプコマンドがコグを引数として呼び出されたときに呼び出されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_cog_help:17
msgid "To get the commands that belong to this cog see :meth:`Cog.get_commands`. The commands returned not filtered. To do the filtering you will have to call :meth:`filter_commands` yourself."
msgstr "このコグに属するコマンドを取得するには、 :meth:`Cog.get_commands` を確認してください。返されるコマンドはフィルタリングされていません。フィルタリングをするには、自分で :meth:`filter_commands` を呼び出す必要があるでしょう。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_cog_help:25
msgid "The cog that was requested for help."
msgstr "ヘルプを要求されたコグ。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_group_help:3
msgid "Handles the implementation of the group page in the help command. This function is called when the help command is called with a group as the argument."
msgstr "ヘルプコマンドでグループページの実装を処理します。 この関数は、ヘルプコマンドがグループを引数として呼び出されたときに呼び出されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_group_help:17
msgid "To get the commands that belong to this group without aliases see :attr:`Group.commands`. The commands returned not filtered. To do the filtering you will have to call :meth:`filter_commands` yourself."
msgstr "このコグに属するコマンドを別名を除いて取得するには、 :attr:`Group.commands` を確認してください。返されるコマンドはフィルタリングされていません。フィルタリングをするには、自分で :meth:`filter_commands` を呼び出す必要があるでしょう。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_group_help:23
msgid "``group`` parameter is now positional-only."
msgstr "``group`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_group_help:25
msgid "The group that was requested for help."
msgstr "ヘルプを要求されたグループ。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:3
msgid "Handles the implementation of the single command page in the help command."
msgstr "ヘルプコマンドで単一のコマンドページの実装を処理します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:16
msgid "Showing Help"
msgstr "ヘルプを表示"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:19
msgid "There are certain attributes and methods that are helpful for a help command to show such as the following:"
msgstr "以下のような、ヘルプコマンドに役立つ属性やメソッドがあります。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:22
msgid ":attr:`Command.help`"
msgstr ":attr:`Command.help`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:23
msgid ":attr:`Command.brief`"
msgstr ":attr:`Command.brief`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:24
msgid ":attr:`Command.short_doc`"
msgstr ":attr:`Command.short_doc`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:25
msgid ":attr:`Command.description`"
msgstr ":attr:`Command.description`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:26
msgid ":meth:`get_command_signature`"
msgstr ":meth:`get_command_signature`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:28
msgid "There are more than just these attributes but feel free to play around with these to help you get started to get the output that you want."
msgstr "これら以外にももっと属性はありますが、欲しい出力を得るのを助けてくれるこいつらで遊ぶことに開放感を感じてみてください。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.send_command_help:35
msgid "The command that was requested for help."
msgstr "ヘルプを要求されたコマンド。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.prepare_help_command:3
msgid "A low level method that can be used to prepare the help command before it does anything. For example, if you need to prepare some state in your subclass before the command does its processing then this would be the place to do it."
msgstr "ヘルプコマンドを準備するのに使用できる低レベルのメソッドです。 例えば、 コマンドが処理する前にサブクラスの状態を準備する必要がある場合は、ここで行うことができます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.prepare_help_command:8
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_ending_note:3
msgid "The default implementation does nothing."
msgstr "デフォルトの実装では何もしません。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.prepare_help_command:12
msgid "This is called *inside* the help command callback body. So all the usual rules that happen inside apply here as well."
msgstr "これは *内部* ヘルプコマンドコールバック本体と呼ばれていますので、内部で発生する通常のルールはすべてここでも適用されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.prepare_help_command:17
msgid "``ctx`` and ``command`` parameters are now positional-only."
msgstr "``ctx`` と ``command`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.prepare_help_command:21
msgid "The argument passed to the help command."
msgstr "ヘルプコマンドに渡される引数。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:3
msgid "The actual implementation of the help command."
msgstr "ヘルプコマンドの実際の実装。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:5
msgid "It is not recommended to override this method and instead change the behaviour through the methods that actually get dispatched."
msgstr "このメソッドをオーバーライドして、実際にディスパッチされるメソッドを通じて動作を変更することは推奨されません。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:8
msgid ":meth:`send_bot_help`"
msgstr ":meth:`send_bot_help`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:9
msgid ":meth:`send_cog_help`"
msgstr ":meth:`send_cog_help`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:10
msgid ":meth:`send_group_help`"
msgstr ":meth:`send_group_help`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:11
msgid ":meth:`send_command_help`"
msgstr ":meth:`send_command_help`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:12
msgid ":meth:`get_destination`"
msgstr ":meth:`get_destination`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:13
msgid ":meth:`command_not_found`"
msgstr ":meth:`command_not_found`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:14
msgid ":meth:`subcommand_not_found`"
msgstr ":meth:`subcommand_not_found`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:15
msgid ":meth:`send_error_message`"
msgstr ":meth:`send_error_message`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:16
msgid ":meth:`on_help_command_error`"
msgstr ":meth:`on_help_command_error`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.HelpCommand.command_callback:17
msgid ":meth:`prepare_help_command`"
msgstr ":meth:`prepare_help_command`"

#: ../../ext/commands/api.rst:284
msgid "DefaultHelpCommand"
msgstr "DefaultHelpCommand"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:1
msgid "The implementation of the default help command."
msgstr "デフォルトのヘルプコマンドの実装。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:3
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:3
msgid "This inherits from :class:`HelpCommand`."
msgstr "これは :class:`HelpCommand` から継承されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:5
msgid "It extends it with the following attributes."
msgstr "以下の属性で拡張されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:9
msgid "The maximum number of characters that fit in a line. Defaults to 80."
msgstr "1行に収める最大の文字数を指定します。デフォルトでは80です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:16
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:7
msgid "Whether to sort the commands in the output alphabetically. Defaults to ``True``."
msgstr "出力されるコマンドをアルファベット順に並べ替えるかどうか。デフォルトは ``True`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:22
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:27
msgid "A tribool that indicates if the help command should DM the user instead of sending it to the channel it received it from. If the boolean is set to ``True``, then all help output is DM'd. If ``False``, none of the help output is DM'd. If ``None``, then the bot will only DM when the help message becomes too long (dictated by more than :attr:`dm_help_threshold` characters). Defaults to ``False``."
msgstr "ヘルプコマンドが、受信したチャンネルに送信する代わりにユーザのDMに送信するかどうかを示す三値論理値。 ブール値が ``True`` に設定されている場合、すべてのヘルプ出力は DMに送信されます。 ``False`` の場合、いずれのヘルプ出力もDMには送信されません。 ``None`` の場合、ボットはヘルプメッセージが長すぎる場合にのみDMに送信します( :attr:`dm_help_threshold` の文字数以上で決定されます)。 デフォルトは ``False`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:33
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:38
msgid "The number of characters the paginator must accumulate before getting DM'd to the user if :attr:`dm_help` is set to ``None``. Defaults to 1000."
msgstr ":attr:`dm_help` が ``None`` に設定されているときに、ペジネーターがユーザのDMに送信するのに必要な文字数。デフォルトは 1000 です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:40
msgid "How much to indent the commands from a heading. Defaults to ``2``."
msgstr "見出しからのコマンドのインデントの量。デフォルトは ``2`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:46
msgid "The arguments list's heading string used when the help command is invoked with a command name. Useful for i18n. Defaults to ``\"Arguments:\"``. Shown when :attr:`.show_parameter_descriptions` is ``True``."
msgstr "コマンド名を指定してhelpコマンドを実行したときに使用される引数リストの見出し文字列です。国際化対応に便利です。デフォルトは ``\"Arguments:\"`` です。:attr:`.show_parameter_descriptions` が ``True`` の場合に表示されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:56
msgid "Whether to show the parameter descriptions. Defaults to ``True``. Setting this to ``False`` will revert to showing the :attr:`~.commands.Command.signature` instead."
msgstr "パラメータの説明を表示するかどうか。デフォルトは ``True`` です。``False`` に設定すると、代わりに :attr:`~.commands.Command.signature` が表示されるようになります。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:65
msgid "The command list's heading string used when the help command is invoked with a category name. Useful for i18n. Defaults to ``\"Commands:\"``"
msgstr "ヘルプコマンドがカテゴリ名で呼び出されたときに使用されるコマンドリストの見出し文字列。i18nに便利です。デフォルトは ``\"Commands:\"`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:72
msgid "The default argument description string used when the argument's :attr:`~.commands.Parameter.description` is ``None``. Useful for i18n. Defaults to ``\"No description given.\"``"
msgstr "引数の :attr:`~.commands.Parameter.description` が ``None`` である場合に使用される、デフォルトの引数の説明文字列です。国際化対応に便利です。デフォルトは ``\"No description given.\"`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:81
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:45
msgid "The string used when there is a command which does not belong to any category(cog). Useful for i18n. Defaults to ``\"No Category\"``"
msgstr "任意のカテゴリ(コグ) に属さないコマンドがある場合に使用される文字列。i18nに便利です。デフォルトは ``\"No Category\"`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:88
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:52
msgid "The paginator used to paginate the help command output."
msgstr "ヘルプコマンドの出力にページ番号を付けるために使用されるペジネーター。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand:90
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:54
msgid ":class:`Paginator`"
msgstr ":class:`Paginator`"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.shorten_text:1
msgid ":class:`str`: Shortens text to fit into the :attr:`width`."
msgstr ":class:`str`：渡された文字列を、 :attr:`width` に収まるよう省略します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.shorten_text:5
msgid "``text`` parameter is now positional-only."
msgstr "``text`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_ending_note:1
msgid ":class:`str`: Returns help command's ending note. This is mainly useful to override for i18n purposes."
msgstr ":class:`str`: Helpコマンドの末尾の文字列を返します。主に翻訳する際にオーバーライドしてください。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.get_command_signature:3
msgid "Calls :meth:`~.HelpCommand.get_command_signature` if :attr:`show_parameter_descriptions` is ``False`` else returns a modified signature where the command parameters are not shown."
msgstr ":attr:`show_parameter_descriptions` が ``False`` の場合に :meth:`~.HelpCommand.get_command_signature` を呼び出し、それ以外の場合はコマンドパラメータを表示しない修正したシグネチャを返します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_indented_commands:1
msgid "Indents a list of commands after the specified heading."
msgstr "指定した見出しの後の、コマンドのリストをインデントします。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_indented_commands:3
msgid "The formatting is added to the :attr:`paginator`."
msgstr "書式は :attr:`paginator` に追加されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_indented_commands:5
msgid "The default implementation is the command name indented by :attr:`indent` spaces, padded to ``max_size`` followed by the command's :attr:`Command.short_doc` and then shortened to fit into the :attr:`width`."
msgstr "デフォルトの実装は :attr:`indent` の空白でインデントされ、コマンドの :attr:`Command.short_doc` に先立って ``max_size`` まで肉付けされ、 :attr:`width` に沿うように縮められたコマンド名です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_indented_commands:13
msgid "A list of commands to indent for output."
msgstr "出力にてインデントするコマンドのリスト。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_indented_commands:15
msgid "The heading to add to the output. This is only added if the list of commands is greater than 0."
msgstr "出力に追加する見出し。これはコマンドのリストが 0 より大きい場合にのみ追加されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_indented_commands:18
msgid "The max size to use for the gap between indents. If unspecified, calls :meth:`~HelpCommand.get_max_size` on the commands parameter."
msgstr "インデント間の差分に使用する最大サイズです。指定されていない場合は、 :meth:`~HelpCommand.get_max_size` をコマンドパラメータとして呼び出します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_command_arguments:1
msgid "Indents a list of command arguments after the :attr:`.arguments_heading`."
msgstr "コマンドの引数のリストを :attr:`.arguments_heading` の後にインデントします。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_command_arguments:3
msgid "The default implementation is the argument :attr:`~.commands.Parameter.name` indented by :attr:`indent` spaces, padded to ``max_size`` using :meth:`~HelpCommand.get_max_size` followed by the argument's :attr:`~.commands.Parameter.description` or :attr:`.default_argument_description` and then shortened to fit into the :attr:`width` and then :attr:`~.commands.Parameter.displayed_default` between () if one is present after that."
msgstr "デフォルトの実装では、引数の :attr:`~.commands.Parameter.name` は :attr:`indent` の量のスペースでインデントされ、引数の :attr:`~.commands.Parameter.description` または :attr:`.default_argument_description` が後に続くよう :meth:`~HelpCommand.get_max_size` を用いて ``max_size`` にパディングされ、そして引数の :attr:`width` と :attr:`~.commands.Parameter.displayed_default` の間に () があれば、その後に入るように短縮されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_command_arguments:12
msgid "The command to list the arguments for."
msgstr "引数の一覧を表示するコマンド。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.send_pages:3
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.send_pages:3
msgid "A helper utility to send the page output from :attr:`paginator` to the destination."
msgstr ":attr:`paginator` から出力されたページを宛先に送信するためのヘルパーユーティリティ。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_command_formatting:1
msgid "A utility function to format the non-indented block of commands and groups."
msgstr "コマンドとグループのインデントされていないブロックをフォーマットするユーティリティ関数。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_command_formatting:7
msgid ":meth:`.add_command_arguments` is now called if :attr:`.show_parameter_descriptions` is ``True``."
msgstr ":meth:`.add_command_arguments` は :attr:`.show_parameter_descriptions` が ``True`` であれば呼び出されるようになりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.DefaultHelpCommand.add_command_formatting:10
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_command_formatting:7
msgid "The command to format."
msgstr "フォーマットするコマンド。"

#: ../../ext/commands/api.rst:293
msgid "MinimalHelpCommand"
msgstr "MinimalHelpCommand"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:1
msgid "An implementation of a help command with minimal output."
msgstr "最小限の出力を持つヘルプコマンドの実装。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:13
msgid "The command list's heading string used when the help command is invoked with a category name. Useful for i18n. Defaults to ``\"Commands\"``"
msgstr "ヘルプコマンドがカテゴリ名で呼び出されたときに使用されるコマンドリストの見出し文字列。i18nに便利です。デフォルトは ``\"Commands\"`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand:20
msgid "The alias list's heading string used to list the aliases of the command. Useful for i18n. Defaults to ``\"Aliases:\"``."
msgstr "コマンドのエイリアスを列挙するために使用するエイリアス一覧のの見出し文字列。i18nに便利です。デフォルトでは ``\"Aliases:\"`` です。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_opening_note:1
msgid "Returns help command's opening note. This is mainly useful to override for i18n purposes."
msgstr "ヘルプコマンドの冒頭の文字列を返します。主に翻訳する際にオーバーライドしてください。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_opening_note:3
msgid "The default implementation returns ::"
msgstr "デフォルトの実装では次を返します ::"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_opening_note:8
msgid "The help command opening note."
msgstr "ヘルプコマンドの冒頭の文字列。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_ending_note:1
msgid "Return the help command's ending note. This is mainly useful to override for i18n purposes."
msgstr "ヘルプコマンドの末尾の文字列を返します。主に翻訳する際にオーバーライドしてください。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.get_ending_note:5
msgid "The help command ending note."
msgstr "ヘルプコマンドの末尾の文字列。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_bot_commands_formatting:1
msgid "Adds the minified bot heading with commands to the output."
msgstr "出力に小さなボットの見出しとコマンドを追加します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_bot_commands_formatting:3
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_subcommand_formatting:3
#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_aliases_formatting:3
msgid "The formatting should be added to the :attr:`paginator`."
msgstr "書式は :attr:`paginator` に追加すべきです。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_bot_commands_formatting:5
msgid "The default implementation is a bold underline heading followed by commands separated by an EN SPACE (U+2002) in the next line."
msgstr "デフォルトの実装では、太字の下線の見出しに続いて、次の行にEN スペース (U+2002) で区切られたコマンドが出力されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_bot_commands_formatting:10
msgid "``commands`` and ``heading`` parameters are now positional-only."
msgstr "``commands`` と ``heading`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_bot_commands_formatting:12
msgid "A list of commands that belong to the heading."
msgstr "この見出しに属するコマンドのリスト。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_bot_commands_formatting:14
msgid "The heading to add to the line."
msgstr "行に追加する見出し。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_subcommand_formatting:1
msgid "Adds formatting information on a subcommand."
msgstr "サブコマンドの書式情報を追加します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_subcommand_formatting:5
msgid "The default implementation is the prefix and the :attr:`Command.qualified_name` optionally followed by an En dash and the command's :attr:`Command.short_doc`."
msgstr "デフォルトの実装ではプレフィックスと、 :attr:`Command.qualified_name` の後にEn ダッシュとコマンドの :attr:`Command.short_doc` が追加されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_subcommand_formatting:12
msgid "The command to show information of."
msgstr "情報を表示するコマンド。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_aliases_formatting:1
msgid "Adds the formatting information on a command's aliases."
msgstr "コマンドのエイリアスの書式情報を追加します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_aliases_formatting:5
msgid "The default implementation is the :attr:`aliases_heading` bolded followed by a comma separated list of aliases."
msgstr "デフォルトの実装では、太字の :attr:`aliases_heading` の後にカンマで区切られたエイリアスの一覧が出力されます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_aliases_formatting:8
msgid "This is not called if there are no aliases to format."
msgstr "フォーマットするエイリアスがない場合は呼び出されません。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_aliases_formatting:12
msgid "``aliases`` parameter is now positional-only."
msgstr "引数 ``aliases`` は位置専用引数となりました。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_aliases_formatting:14
msgid "A list of aliases to format."
msgstr "フォーマットするエイリアスのリスト。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.MinimalHelpCommand.add_command_formatting:1
msgid "A utility function to format commands and groups."
msgstr "コマンドとグループをフォーマットするユーティリティ関数。"

#: ../../ext/commands/api.rst:302
msgid "Paginator"
msgstr "Paginator"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator:1
msgid "A class that aids in paginating code blocks for Discord messages."
msgstr "Discordメッセージのコードブロックをページに分割するのに役立つクラス。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator:7
msgid "Returns the total number of characters in the paginator."
msgstr "ペジネーター内の文字数の合計を返します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator:11
msgid "The prefix inserted to every page. e.g. three backticks, if any."
msgstr "存在する場合、毎ページに挿入されるプレフィックス。例: バックティック3個。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator:17
msgid "The suffix appended at the end of every page. e.g. three backticks, if any."
msgstr "存在する場合、毎ページの末尾に挿入されるサフィックス。例: バックティック3個。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator:23
msgid "The maximum amount of codepoints allowed in a page."
msgstr "ページ内で許可されるコードポイントの最大数。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator:32
msgid "The character string inserted between lines. e.g. a newline character."
msgstr "行間に挿入される文字列。例: 改行文字。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator.clear:1
msgid "Clears the paginator to have no pages."
msgstr "ページネーターのページを消去します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator.add_line:1
msgid "Adds a line to the current page."
msgstr "現在のページに行を追加します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator.add_line:3
msgid "If the line exceeds the :attr:`max_size` then an exception is raised."
msgstr "行が :attr:`max_size` を超えると、例外が発生します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator.add_line:6
msgid "The line to add."
msgstr "追加する行。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator.add_line:8
msgid "Indicates if another empty line should be added."
msgstr "別に空行を追加するかどうかを示します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator.add_line:11
msgid "The line was too big for the current :attr:`max_size`."
msgstr "現在の :attr:`max_size` に対し行が大きすぎる場合。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.help.Paginator.close_page:1
msgid "Prematurely terminate a page."
msgstr "ページを改めます。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.Paginator.pages:1
msgid "Returns the rendered list of pages."
msgstr "レンダリングされたページの一覧を返します。"

#: ../../../discord/ext/commands/help.py:docstring of discord.ext.commands.Paginator.pages:3
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:98
#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:17
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingPermissions:10
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingPermissions:10
msgid "List[:class:`str`]"
msgstr "List[:class:`str`]"

#: ../../ext/commands/api.rst:310
msgid "Enums"
msgstr "列挙型"

#: ../../ext/commands/api.rst:315
msgid "Specifies a type of bucket for, e.g. a cooldown."
msgstr "クールダウンなどに使用するバケットの種類を指定します。"

#: ../../ext/commands/api.rst:319
msgid "The default bucket operates on a global basis."
msgstr "デフォルトのバケットはグローバル基準で動作します。"

#: ../../ext/commands/api.rst:322
msgid "The user bucket operates on a per-user basis."
msgstr "ユーザーバケットは、ユーザーごとに動作します。"

#: ../../ext/commands/api.rst:325
msgid "The guild bucket operates on a per-guild basis."
msgstr "ギルドバケットは、ギルドごとに動作します。"

#: ../../ext/commands/api.rst:328
msgid "The channel bucket operates on a per-channel basis."
msgstr "チャンネルバケットは、チャンネルごとに動作します。"

#: ../../ext/commands/api.rst:331
msgid "The member bucket operates on a per-member basis."
msgstr "メンバーバケットは、メンバーごとに動作します。"

#: ../../ext/commands/api.rst:334
msgid "The category bucket operates on a per-category basis."
msgstr "カテゴリバケットは、カテゴリごとに動作します。"

#: ../../ext/commands/api.rst:337
msgid "The role bucket operates on a per-role basis."
msgstr "ロールバケットは、ロールごとに動作します。"

#: ../../ext/commands/api.rst:345
msgid "Checks"
msgstr "Checks"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:1
msgid "A decorator that adds a check to the :class:`.Command` or its subclasses. These checks could be accessed via :attr:`.Command.checks`."
msgstr ":class:`.Command` またはそのサブクラスにチェックを追加するデコレータ。これらのチェックは :attr:`.Command.checks` 経由でアクセスできます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:4
msgid "These checks should be predicates that take in a single parameter taking a :class:`.Context`. If the check returns a ``False``\\-like value then during invocation a :exc:`.CheckFailure` exception is raised and sent to the :func:`.on_command_error` event."
msgstr "これらのチェックは唯一の引数 :class:`.Context` を取る関数であるべきです。もしチェックが ``False`` のような値を返せば、呼び出し中に :exc:`.CheckFailure` が発生され :func:`.on_command_error` に送られます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:9
msgid "If an exception should be thrown in the predicate then it should be a subclass of :exc:`.CommandError`. Any exception not subclassed from it will be propagated while those subclassed will be sent to :func:`.on_command_error`."
msgstr "もし関数内で例外が発生するとしたら、それは :exc:`.CommandError` のサブクラスであるべきです。これらのサブクラスが :func:`.on_command_error` に送られるのに対し、それから継承されていないどんな例外も、伝播します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:14
msgid "A special attribute named ``predicate`` is bound to the value returned by this decorator to retrieve the predicate passed to the decorator. This allows the following introspection and chaining to be done:"
msgstr "``predicate`` という特別な属性は、デコレータから返された値にバインドされ、デコレータに渡された関数を取得します。 これにより、次のイントロスペクションとチェーンを行うことができます:"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:30
msgid "The function returned by ``predicate`` is **always** a coroutine, even if the original function was not a coroutine."
msgstr "``predicate`` によって返される関数は、たとえ元の関数がコルーチンでなくても、**常に** コルーチンです。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:33
msgid "The ``predicate`` attribute was added."
msgstr "``predicate`` 属性が追加されました。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:38
msgid "Creating a basic check to see if the command invoker is you."
msgstr "コマンドを呼び出したのがあなたであるかどうかを確認するための基本的なチェックの作成。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:50
msgid "Transforming common checks into its own decorator:"
msgstr "一般的なチェックを独自のデコレータに変換します:"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:66
msgid "``predicate`` parameter is now positional-only."
msgstr "``predicate`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check:68
msgid "The predicate to check if the command should be invoked."
msgstr "コマンドが呼び出されるかどうかをチェックする関数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check_any:1
msgid "A :func:`check` that is added that checks if any of the checks passed will pass, i.e. using logical OR."
msgstr "渡されたチェックのいずれかが合格するか、すなわち論理ORを使用するかをチェックする追加された :func:`check` 。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check_any:4
msgid "If all checks fail then :exc:`.CheckAnyFailure` is raised to signal the failure. It inherits from :exc:`.CheckFailure`."
msgstr "すべてのチェックに失敗した場合、 :exc:`.CheckAnyFailure` が発生して失敗したことを通知します。これは :exc:`.CheckFailure` を継承します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check_any:9
msgid "The ``predicate`` attribute for this function **is** a coroutine."
msgstr "この関数の ``predicate`` 属性は**コルーチン**です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check_any:13
msgid "An argument list of checks that have been decorated with the :func:`check` decorator."
msgstr ":func:`check` デコレータで装飾されたチェックの引数リスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check_any:17
msgid "A check passed has not been decorated with the :func:`check`     decorator."
msgstr "渡されたチェックが :func:`check` デコレータで装飾されていません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.check_any:21
msgid "Creating a basic check to see if it's the bot owner or the server owner:"
msgstr "ボットの所有者かサーバーの所有者かを確認するための基本的なチェックの作成なら:"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_role:1
msgid "A :func:`.check` that is added that checks if the member invoking the command has the role specified via the name or ID specified."
msgstr "コマンドを呼び出したメンバーが、指定された名前またはIDのロールを持っているかどうかのチェックを追加する :func:`.check` 。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_role:4
msgid "If a string is specified, you must give the exact name of the role, including caps and spelling."
msgstr "文字列が指定された場合は、大文字やつづりを含めロールの名前を正確に指定する必要があります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_role:7
msgid "If an integer is specified, you must give the exact snowflake ID of the role."
msgstr "整数が指定されている場合は、ロールの正確なsnowflake IDを指定する必要があります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_role:9
msgid "If the message is invoked in a private message context then the check will return ``False``."
msgstr "メッセージがプライベートメッセージのコンテキストで呼び出された場合、チェックは ``False`` を返します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_role:12
msgid "This check raises one of two special exceptions, :exc:`.MissingRole` if the user is missing a role, or :exc:`.NoPrivateMessage` if it is used in a private message. Both inherit from :exc:`.CheckFailure`."
msgstr "このチェックは２つの特別な例外のうち１つ、もしユーザがロールを持っていないなら :exc:`.MissingRole` を、プライベートメッセージで使用されたなら :exc:`.NoPrivateMessage` を発生します。どちらも :exc:`.CheckFailure` を継承します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_role:18
msgid "Raise :exc:`.MissingRole` or :exc:`.NoPrivateMessage` instead of generic :exc:`.CheckFailure`"
msgstr "一般的な :exc:`.CheckFailure` の代わりに :exc:`.MissingRole` または :exc:`.NoPrivateMessage` を発生させます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_role:23
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_role:15
msgid "``item`` parameter is now positional-only."
msgstr "``item`` 引数は位置限定引数になりました。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_role:25
msgid "The name or ID of the role to check."
msgstr "チェックするロールの名前またはID。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_permissions:1
msgid "A :func:`.check` that is added that checks if the member has all of the permissions necessary."
msgstr "メンバーが必要なすべての権限を持っているかどうかのチェックを追加する :func:`.check` 。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_permissions:4
msgid "Note that this check operates on the current channel permissions, not the guild wide permissions."
msgstr "このチェックはギルド全体の権限ではなく、現在のチャンネルの権限で動作することに注意してください。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_permissions:7
msgid "The permissions passed in must be exactly like the properties shown under :class:`.discord.Permissions`."
msgstr "渡された権限は、 :class:`.discord.Permissions` のプロパティとまったく同じでなければなりません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_permissions:10
msgid "This check raises a special exception, :exc:`.MissingPermissions` that is inherited from :exc:`.CheckFailure`."
msgstr "このチェックは特別な例外であり、 :exc:`.CheckFailure` から継承されている :exc:`.MissingPermissions` を発生します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_permissions:13
msgid "An argument list of permissions to check for."
msgstr "チェックする権限の引数のリスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_guild_permissions:1
msgid "Similar to :func:`.has_permissions`, but operates on guild wide permissions instead of the current channel permissions."
msgstr ":func:`.has_permissions` と似ていますが、現在のチャンネルの権限の代わりにギルド全体の権限で動作します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_guild_permissions:4
msgid "If this check is called in a DM context, it will raise an exception, :exc:`.NoPrivateMessage`."
msgstr "このチェックがDMのコンテキストで呼び出されると、 :exc:`.NoPrivateMessage` 例外が発生します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_any_role:1
msgid "A :func:`.check` that is added that checks if the member invoking the command has **any** of the roles specified. This means that if they have one out of the three roles specified, then this check will return ``True``."
msgstr "コマンドを呼び出したメンバーが、指定された名前またはIDのロールのうちの **どれか** を持っているかをチェックを追加する :func:`.check` 。 これは、指定された3つのうちのどのロールが指定されていても、このチェックが ``True`` を返すことを意味します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_any_role:5
msgid "Similar to :func:`.has_role`\\, the names or IDs passed in must be exact."
msgstr ":func:`.has_role` と同様に、渡された名前やIDは正確でなければなりません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_any_role:7
msgid "This check raises one of two special exceptions, :exc:`.MissingAnyRole` if the user is missing all roles, or :exc:`.NoPrivateMessage` if it is used in a private message. Both inherit from :exc:`.CheckFailure`."
msgstr "このチェックは２つの特別な例外のうち１つ、もしユーザがロールをすべて持っていないなら :exc:`.MissingAnyRole` を、プライベートメッセージで使用されたなら :exc:`.NoPrivateMessage` を発生します。どちらも :exc:`.CheckFailure` を継承します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_any_role:13
msgid "Raise :exc:`.MissingAnyRole` or :exc:`.NoPrivateMessage` instead of generic :exc:`.CheckFailure`"
msgstr "一般的な :exc:`.CheckFailure` の代わりに :exc:`.MissingAnyRole` または :exc:`.NoPrivateMessage` を発生させます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.has_any_role:16
msgid "An argument list of names or IDs to check that the member has roles wise."
msgstr "メンバーが割り当てられたロールを持っているかどうかをチェックする名前またはIDの引数リスト。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_role:1
msgid "Similar to :func:`.has_role` except checks if the bot itself has the role."
msgstr "ボット自体がロールを持っているかどうかをチェックする以外は、 :func:`.has_role` と同様です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_role:4
msgid "This check raises one of two special exceptions, :exc:`.BotMissingRole` if the bot is missing the role, or :exc:`.NoPrivateMessage` if it is used in a private message. Both inherit from :exc:`.CheckFailure`."
msgstr "このチェックは２つの特別な例外のうち１つ、もしボットがロールを持っていないなら :exc:`.BotMissingRole` を、プライベートメッセージで使用されたなら :exc:`.NoPrivateMessage` を発生します。どちらも :exc:`.CheckFailure` を継承します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_role:10
msgid "Raise :exc:`.BotMissingRole` or :exc:`.NoPrivateMessage` instead of generic :exc:`.CheckFailure`"
msgstr "一般的な :exc:`.CheckFailure` の代わりに :exc:`.BotMissingRole` または :exc:`.NoPrivateMessage` を発生させます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_permissions:1
msgid "Similar to :func:`.has_permissions` except checks if the bot itself has the permissions listed."
msgstr "ボット自身がリストされている権限を持っているかどうかをチェックすること以外は :func:`.has_permissions` と同様です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_permissions:4
msgid "This check raises a special exception, :exc:`.BotMissingPermissions` that is inherited from :exc:`.CheckFailure`."
msgstr "このチェックは特別な例外であり、 :exc:`.CheckFailure` から継承されている :exc:`.BotMissingPermissions` を発生します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_guild_permissions:1
msgid "Similar to :func:`.has_guild_permissions`, but checks the bot members guild permissions."
msgstr ":func:`.has_guild_permissions` と似ていますが、ボットメンバーのギルドの権限をチェックします。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_any_role:1
msgid "Similar to :func:`.has_any_role` except checks if the bot itself has any of the roles listed."
msgstr "ボット自体がリストされたロールを持っているかどうかをチェックする以外は、 :func:`.has_any_role` と同様です。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_any_role:4
msgid "This check raises one of two special exceptions, :exc:`.BotMissingAnyRole` if the bot is missing all roles, or :exc:`.NoPrivateMessage` if it is used in a private message. Both inherit from :exc:`.CheckFailure`."
msgstr "このチェックは２つの特別な例外のうち１つ、もしボットがどのロールも持っていないなら :exc:`.BotMissingAnyRole` を、プライベートメッセージで使用されたなら :exc:`.NoPrivateMessage` を発生します。どちらも :exc:`.CheckFailure` を継承します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.bot_has_any_role:10
msgid "Raise :exc:`.BotMissingAnyRole` or :exc:`.NoPrivateMessage` instead of generic checkfailure"
msgstr "一般的なCheckFailureの代わりに :exc:`.BotMissingAnyRole` または :exc:`.NoPrivateMessage` を発生させます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:1
msgid "A decorator that adds a cooldown to a :class:`.Command`"
msgstr ":class:`.Command` にクールダウンを追加するデコレーター。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:3
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dynamic_cooldown:8
msgid "A cooldown allows a command to only be used a specific amount of times in a specific time frame. These cooldowns can be based either on a per-guild, per-channel, per-user, per-role or global basis. Denoted by the third argument of ``type`` which must be of enum type :class:`.BucketType`."
msgstr "クールダウンにより、コマンドは特定の時間枠内でのみ使用することができます。 これらのクールダウンは、ギルドごと、チャンネルごと、ユーザごと、ロールごと、またはグローバル基準のいずれかに基づくことができます。 列挙型 :class:`.BucketType` でなければならない第３引数 ``type`` によって示されます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:9
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dynamic_cooldown:14
msgid "If a cooldown is triggered, then :exc:`.CommandOnCooldown` is triggered in :func:`.on_command_error` and the local error handler."
msgstr "クールダウンが発生した場合、 :exc:`.CommandOnCooldown` が :func:`.on_command_error` とローカルのエラーハンドラに引っかかります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:12
#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dynamic_cooldown:17
msgid "A command can only have a single cooldown."
msgstr "コマンドは1つのクールダウンしか持つことができません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:14
msgid "The number of times a command can be used before triggering a cooldown."
msgstr "クールダウンを発生させる前にコマンドを使用できる回数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:16
msgid "The amount of seconds to wait for a cooldown when it's been triggered."
msgstr "トリガーされたときにクールダウンを待つ秒数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:18
msgid "The type of cooldown to have. If callable, should return a key for the mapping."
msgstr "持っているクールダウンの種類。呼び出し可能な場合は、マッピングのキーを返す必要があります。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:20
msgid "Callables are now supported for custom bucket types."
msgstr "カスタムされたBucketタイプの呼び出しをサポートするようになりました。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.cooldown:23
msgid "When passing a callable, it now needs to accept :class:`.Context` rather than :class:`~discord.Message` as its only argument."
msgstr "呼び出し可能オブジェクトを渡す場合、 :class:`~discord.Message` ではなく、 :class:`.Context` を引数として受け付けないといけないようになりました。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dynamic_cooldown:1
msgid "A decorator that adds a dynamic cooldown to a :class:`.Command`"
msgstr ":class:`.Command` に動的なクールダウンを追加するデコレータ。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dynamic_cooldown:3
msgid "This differs from :func:`.cooldown` in that it takes a function that accepts a single parameter of type :class:`.Context` and must return a :class:`~discord.app_commands.Cooldown` or ``None``. If ``None`` is returned then that cooldown is effectively bypassed."
msgstr "これは唯一のパラメータに :class:`.Context` を取り、 :class:`~discord.app_commands.Cooldown` または ``None`` を返す関数を取るという点で :func:`.cooldown` と異なります。もし ``None`` を返せば、クールダウンは事実上回避されます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dynamic_cooldown:21
msgid "A function that takes a message and returns a cooldown that will apply to this invocation or ``None`` if the cooldown should be bypassed."
msgstr "メッセージを受け取り、この呼び出しに適用されるクールダウンまたはクールダウンを回避する場合 ``None`` を返す関数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dynamic_cooldown:24
msgid "The type of cooldown to have."
msgstr "持っているクールダウンの種類。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.max_concurrency:1
msgid "A decorator that adds a maximum concurrency to a :class:`.Command` or its subclasses."
msgstr ":class:`.Command` またはそのサブクラスに最大同時実行数制限を追加するデコレータ。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.max_concurrency:3
msgid "This enables you to only allow a certain number of command invocations at the same time, for example if a command takes too long or if only one user can use it at a time. This differs from a cooldown in that there is no set waiting period or token bucket -- only a set number of people can run the command."
msgstr "これにより、特定の数のコマンドの同時呼び出しを許可することができます。 例えば、コマンドに時間がかかりすぎたり、あるいは一度に1人のユーザーしか使えない場合などです。 これは、設定された待ち時間やトークンバケットがないという点でクールダウンとは異なります - コマンドを実行できるのは、設定された数の人だけです。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.max_concurrency:10
msgid "The maximum number of invocations of this command that can be running at the same time."
msgstr "同時に実行できるコマンドの呼び出しの最大数。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.max_concurrency:12
msgid "The bucket that this concurrency is based on, e.g. ``BucketType.guild`` would allow it to be used up to ``number`` times per guild."
msgstr "この並行性がベースになっているバケット。例えば、``BucketType.guild`` ではギルドごとに ``number`` 回まで使用できます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.max_concurrency:15
msgid "Whether the command should wait for the queue to be over. If this is set to ``False`` then instead of waiting until the command can run again, the command raises :exc:`.MaxConcurrencyReached` to its error handler. If this is set to ``True`` then the command waits until it can be executed."
msgstr "コマンドがキューが終わるのを待つかどうか。 これが ``False`` に設定されている場合、コマンドが再び実行可能になるまで待つことのかわりに、コマンドはそのエラーハンドラに対し :exc:`.MaxConcurrencyReached` を発生させます。 これが ``True`` に設定されている場合、コマンドが実行可能になるまで待機します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.before_invoke:3
msgid "This allows you to refer to one before invoke hook for several commands that do not have to be within the same cog."
msgstr "これにより、同じコグ内になくてもよい、複数のコマンドについてフックを呼び出す前に 参照することができます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.after_invoke:3
msgid "This allows you to refer to one after invoke hook for several commands that do not have to be within the same cog."
msgstr "これにより、同じコグ内になくてもよい、複数のコマンドについてフックを呼び出した後に 参照することができます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.guild_only:1
msgid "A :func:`.check` that indicates this command must only be used in a guild context only. Basically, no private messages are allowed when using the command."
msgstr "このコマンドを示す :func:`.check` は、ギルドのコンテキスト内でのみ使用される必要があります。 基本的には、プライベートメッセージはコマンドを使用するときに許可されません。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.guild_only:5
msgid "This check raises a special exception, :exc:`.NoPrivateMessage` that is inherited from :exc:`.CheckFailure`."
msgstr "このチェックは特別な例外であり、 :exc:`.CheckFailure` から継承されている :exc:`.NoPrivateMessage` を発生します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.guild_only:8
msgid "If used on hybrid commands, this will be equivalent to the :func:`discord.app_commands.guild_only` decorator. In an unsupported context, such as a subcommand, this will still fallback to applying the check."
msgstr "ハイブリッドコマンドで使用する場合は、 :func:`discord.app_commands.guild_only` デコレータと同じです。 サブコマンドのようなサポートされていないコンテキストであっても、チェックは適用されます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dm_only:1
msgid "A :func:`.check` that indicates this command must only be used in a DM context. Only private messages are allowed when using the command."
msgstr "このコマンドを示す :func:`.check` は、DMのコンテキストでのみ使用される必要があります。このコマンドを使用するときプライベートメッセージのみが許可されます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.dm_only:5
msgid "This check raises a special exception, :exc:`.PrivateMessageOnly` that is inherited from :exc:`.CheckFailure`."
msgstr "このチェックは特別な例外であり、 :exc:`.CheckFailure` から継承されている :exc:`.PrivateMessageOnly` を発生します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.is_owner:1
msgid "A :func:`.check` that checks if the person invoking this command is the owner of the bot."
msgstr "このコマンドを実行している人がボットの所有者であるかどうかをチェックする :func:`.check` 。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.is_owner:4
msgid "This is powered by :meth:`.Bot.is_owner`."
msgstr "これは :meth:`.Bot.is_owner` を使って動作します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.is_owner:6
msgid "This check raises a special exception, :exc:`.NotOwner` that is derived from :exc:`.CheckFailure`."
msgstr "このチェックは特別な例外であり、 :exc:`.CheckFailure` から継承されている :exc:`.NotOwner` を発生します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.is_nsfw:1
msgid "A :func:`.check` that checks if the channel is a NSFW channel."
msgstr "NSFWチャンネルであるかどうかをチェックする :func:`.check` 。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.is_nsfw:3
msgid "This check raises a special exception, :exc:`.NSFWChannelRequired` that is derived from :exc:`.CheckFailure`."
msgstr "このチェックは特別な例外であり、 :exc:`.CheckFailure` から継承されている :exc:`.NSFWChannelRequired` を発生します。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.is_nsfw:6
msgid "If used on hybrid commands, this will be equivalent to setting the application command's ``nsfw`` attribute to ``True``. In an unsupported context, such as a subcommand, this will still fallback to applying the check."
msgstr "ハイブリッドコマンドで使用する場合は、アプリケーションコマンドの ``nsfw`` 属性を ``True`` に設定するのと同じです。サブコマンドのようなサポートされていないコンテキストであっても、チェックは適用されます。"

#: ../../../discord/ext/commands/core.py:docstring of discord.ext.commands.core.is_nsfw:13
msgid "Raise :exc:`.NSFWChannelRequired` instead of generic :exc:`.CheckFailure`. DM channels will also now pass this check."
msgstr "一般的な :exc:`.CheckFailure` の代わりに :exc:`NSFWChannelRequired` を発生させます。DMチャンネルもこのチェックを通過するようになります。"

#: ../../ext/commands/api.rst:407
msgid "Context"
msgstr "Context"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:1
msgid "Represents the context in which a command is being invoked under."
msgstr "コマンドが実行されているコンテキストを表します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:3
msgid "This class contains a lot of meta data to help you understand more about the invocation context. This class is not created manually and is instead passed around to commands as the first parameter."
msgstr "このクラスには、呼び出しコンテキストについて理解するのに役立つ多くのメタデータが含まれています。 このクラスは手動では作成されず、代わりに最初のパラメータとしてコマンドに渡されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:7
msgid "This class implements the :class:`~discord.abc.Messageable` ABC."
msgstr "このクラスは :class:`~discord.abc.Messageable` ABC を実装しています。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:11
msgid "The message that triggered the command being executed."
msgstr "実行中のコマンドをトリガーしたメッセージ。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:15
msgid "In the case of an interaction based context, this message is \"synthetic\" and does not actually exist. Therefore, the ID on it is invalid similar to ephemeral messages."
msgstr "インタラクションベースのコンテキストの場合、メッセージは「合成」されていて、実在しません。そのため、IDは一時的なメッセージ同様無効です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:19
msgid ":class:`.Message`"
msgstr ":class:`.Message`"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:23
msgid "The bot that contains the command being executed."
msgstr "実行中のコマンドを含むボット。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:25
msgid ":class:`.Bot`"
msgstr ":class:`.Bot`"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:29
msgid "The list of transformed arguments that were passed into the command. If this is accessed during the :func:`.on_command_error` event then this list could be incomplete."
msgstr "コマンドに渡された変換された引数のリスト。 もしこれが :func:`.on_command_error` イベント中にアクセスされた場合、このリストは不完全である可能性があります。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:33
msgid ":class:`list`"
msgstr ":class:`list`"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:37
msgid "A dictionary of transformed arguments that were passed into the command. Similar to :attr:`args`\\, if this is accessed in the :func:`.on_command_error` event then this dict could be incomplete."
msgstr "コマンドに渡された変換された引数の辞書。 :attr:`args` と似ていて、もしこれが :func:`.on_command_error` イベント中にアクセスされた場合、この辞書は不完全である可能性があります。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:45
msgid "The parameter that is currently being inspected and converted. This is only of use for within converters."
msgstr "現在検査および変換されているパラメータ。これはコンバータ内でのみ使用されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:50
msgid "Optional[:class:`Parameter`]"
msgstr "Optional[:class:`Parameter`]"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:54
msgid "The argument string of the :attr:`current_parameter` that is currently being converted. This is only of use for within converters."
msgstr "現在変換されている :attr:`current_parameter` の引数文字列。これはコンバーター内でのみ使用されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:63
msgid "The interaction associated with this context."
msgstr "このコンテキストに関連付けられたインタラクション。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:67
msgid "Optional[:class:`~discord.Interaction`]"
msgstr "Optional[:class:`~discord.Interaction`]"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:71
msgid "The prefix that was used to invoke the command. For interaction based contexts, this is ``/`` for slash commands and ``\\u200b`` for context menu commands."
msgstr "コマンドを呼び出すために使用された接頭辞。インタラクションベースのコンテキストでは、スラッシュコマンドでは ``/`` で、コンテキストメニューコマンドでは ``\\u200b`` です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:78
msgid "The command that is being invoked currently."
msgstr "現在呼び出されているコマンド。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:84
msgid "The command name that triggered this invocation. Useful for finding out which alias called the command."
msgstr "この呼び出しを引き起こしたコマンドの名前。コマンドを呼び出した別名を突き止めるのに役立ちます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:91
msgid "The command names of the parents that triggered this invocation. Useful for finding out which aliases called the command."
msgstr "この呼び出しを引き起こした親のコマンドの名前。コマンドを呼び出した別名を突き止めるのに役立ちます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:94
msgid "For example in commands ``?a b c test``, the invoked parents are ``['a', 'b', 'c']``."
msgstr "例えば、``?a b c test`` では、呼び出された親は ``['a', 'b', 'c']`` です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:102
msgid "The subcommand that was invoked. If no valid subcommand was invoked then this is equal to ``None``."
msgstr "呼び出されたサブコマンド。有効なサブコマンドが呼び出されなかった場合、これは ``None`` と等しくなります。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:109
msgid "The string that was attempted to call a subcommand. This does not have to point to a valid registered subcommand and could just point to a nonsense string. If nothing was passed to attempt a call to a subcommand then this is set to ``None``."
msgstr "サブコマンドの呼び出しを試みた文字列。 これは有効な登録されたサブコマンドを指す必要はなく、無茶な文字列を指すだけとなり得ます。 サブコマンドへの呼び出しに何も渡されなかった場合、これは ``None`` に設定されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context:118
msgid "A boolean that indicates if the command failed to be parsed, checked, or invoked."
msgstr "コマンドの分解、チェック、呼び出しに失敗したかどうかを示すブール値。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.typing:1
msgid "Returns an asynchronous context manager that allows you to send a typing indicator to the destination for an indefinite period of time, or 10 seconds if the context manager is called using ``await``."
msgstr "入力インジケーターを宛先に無期限で、または ``await`` で呼び出された場合10秒間表示できるようにする非同期コンテキストマネージャーを返します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.typing:5
msgid "In an interaction based context, this is equivalent to a :meth:`defer` call and does not do any typing calls."
msgstr "インタラクションベースのコンテキストでは、これは :meth:`defer` 呼び出しと同じで、入力呼び出しは行いません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.typing:8
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.typing:16
msgid "Example Usage: ::"
msgstr "使用例: ::"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.typing:22
msgid "This no longer works with the ``with`` syntax, ``async with`` must be used instead."
msgstr "これは ``with`` 構文で動作せず、代わりに ``async with`` を使用しないといけないように変更されました。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.typing:25
msgid "Added functionality to ``await`` the context manager to send a typing indicator for 10 seconds."
msgstr "コンテキストマネージャーを ``await`` して入力インジケーターを10秒間送信する機能を追加しました。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.typing:28
msgid "Indicates whether the deferred message will eventually be ephemeral. Only valid for interaction based contexts."
msgstr "遅延されたメッセージが後に一時的になるかどうかを示します。インタラクションベースのコンテキストでのみ有効です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.from_interaction:3
msgid "Creates a context from a :class:`discord.Interaction`. This only works on application command based interactions, such as slash commands or context menus."
msgstr ":class:`discord.Interaction` からコンテキストを作成します。これはスラッシュコマンドやコンテキストメニューなどのアプリケーションコマンドベースのインタラクションでのみ動作します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.from_interaction:7
msgid "On slash command based interactions this creates a synthetic :class:`~discord.Message` that points to an ephemeral message that the command invoker has executed. This means that :attr:`Context.author` returns the member that invoked the command."
msgstr "スラッシュコマンドベースのインタラクションではこれはコマンド実行者が実行した一時的なメッセージを指す :class:`~discord.Message` を合成します。つまり、 :attr:`Context.author` はコマンドを実行したメンバーを返します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.from_interaction:11
msgid "In a message context menu based interaction, the :attr:`Context.message` attribute is the message that the command is being executed on. This means that :attr:`Context.author` returns the author of the message being targetted. To get the member that invoked the command then :attr:`discord.Interaction.user` should be used instead."
msgstr "メッセージコンテキストメニューベースのインタラクションでは、 :attr:`Context.message` 属性はコマンドが実行されたメッセージです。つまり、 :attr:`Context.author` は対象メッセージの送信者を返します。コマンドを実行したメンバーを得るには、代わりに :attr:`discord.Interaction.user` を使用すべきです。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.from_interaction:18
msgid "The interaction to create a context with."
msgstr "コンテキストを作成するためのインタラクション。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.from_interaction:21
msgid "The interaction does not have a valid command."
msgstr "インタラクションに有効なコマンドが存在しない場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.from_interaction:22
msgid "The interaction client is not derived from :class:`Bot` or :class:`AutoShardedBot`."
msgstr "インタラクションクライアントが :class:`Bot` や :class:`AutoShardedBot` を継承していない場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.invoke:3
msgid "Calls a command with the arguments given."
msgstr "与えられた引数でコマンドを呼び出します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.invoke:5
msgid "This is useful if you want to just call the callback that a :class:`.Command` holds internally."
msgstr "単に :class:`.Command` が内部的に保持しているコールバックを呼び出したい場合に便利です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.invoke:10
msgid "This does not handle converters, checks, cooldowns, pre-invoke, or after-invoke hooks in any matter. It calls the internal callback directly as-if it was a regular function."
msgstr "これは、いずれの場合においても、コンバーター、チェック、クールダウン、呼び出し前、または呼び出し後のフックを処理しません。 内部コールバックが通常の関数であるかのように直接呼び出します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.invoke:14
msgid "You must take care in passing the proper arguments when using this function."
msgstr "この関数を使用する場合は、適切な引数を渡すことに注意しなければなりません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.invoke:21
msgid "The command that is going to be called."
msgstr "呼び出されるコマンド。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.invoke:23
msgid "The arguments to use."
msgstr "使用する引数。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.invoke:24
msgid "The keyword arguments to use."
msgstr "使用するキーワード引数。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.invoke:26
msgid "The command argument to invoke is missing."
msgstr "呼び出すコマンド引数がありません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reinvoke:3
msgid "Calls the command again."
msgstr "コマンドを再度呼び出します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reinvoke:5
msgid "This is similar to :meth:`~.Context.invoke` except that it bypasses checks, cooldowns, and error handlers."
msgstr "これはチェック、クールダウン、エラーハンドラが回避されていることを除き、 :meth:`~.Context.invoke` と同様です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reinvoke:10
msgid "If you want to bypass :exc:`.UserInputError` derived exceptions, it is recommended to use the regular :meth:`~.Context.invoke` as it will work more naturally. After all, this will end up using the old arguments the user has used and will thus just fail again."
msgstr "派生した例外の :exc:`.UserInputError` を回避したい場合は、より自然に動作する標準の :meth:`~.Context.invoke` を使用することをお勧めします。結局のところ、これはユーザが使用した古い引数を使用することになり、したがって再び失敗します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reinvoke:16
msgid "Whether to call the before and after invoke hooks."
msgstr "前後にフックを呼び出すかどうかを設定します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reinvoke:18
msgid "Whether to start the call chain from the very beginning or where we left off (i.e. the command that caused the error). The default is to start where we left off."
msgstr "呼び出しチェーンを最初、または中断した場所(例えばエラーの原因となったコマンド)から開始するかどうか。デフォルトでは、中断した位置から開始します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reinvoke:23
msgid "The context to reinvoke is not valid."
msgstr "再呼び出しするコンテキストが無効です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.Context.valid:1
msgid "Checks if the invocation context is valid to be invoked with."
msgstr "呼び出しコンテキストを呼び出すのが有効かどうかを確認します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.Context.clean_prefix:1
msgid "The cleaned up invoke prefix. i.e. mentions are ``@name`` instead of ``<@id>``."
msgstr "「クリーンアップ」されたプレフィックスを返します。たとえば、メンションは ``<@id>`` のかわりに ``@name`` となります。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.Context.cog:1
msgid "Returns the cog associated with this context's command. None if it does not exist."
msgstr "このコンテキストのコマンドに関連付けられたコグを返します。存在しない場合はNoneを返します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.Context.filesize_limit:1
msgid "Returns the maximum number of bytes files can have when uploaded to this guild or DM channel associated with this context."
msgstr ""

#: ../../docstring of discord.ext.commands.Context.guild:1
msgid "Returns the guild associated with this context's command. None if not available."
msgstr "このコンテキストのコマンドに関連付けられているギルドを返します。利用できない場合はNoneを返します。"

#: ../../docstring of discord.ext.commands.Context.channel:1
msgid "Returns the channel associated with this context's command. Shorthand for :attr:`.Message.channel`."
msgstr "このコンテキストのコマンドに関連付けられているチャンネルを返します。 :attr:`.Message.channel` のショートカットです。"

#: ../../docstring of discord.ext.commands.Context.channel:4
msgid "Union[:class:`.abc.Messageable`]"
msgstr "Union[:class:`.abc.Messageable`]"

#: ../../docstring of discord.ext.commands.Context.author:1
msgid "Union[:class:`~discord.User`, :class:`.Member`]: Returns the author associated with this context's command. Shorthand for :attr:`.Message.author`"
msgstr "Union[:class:`~discord.User`, :class:`.Member`]: このコンテキストのコマンドに関連付けられた作者を返します。 :attr:`.Message.author` のショートカットです。"

#: ../../docstring of discord.ext.commands.Context.me:1
msgid "Union[:class:`.Member`, :class:`.ClientUser`]: Similar to :attr:`.Guild.me` except it may return the :class:`.ClientUser` in private message contexts."
msgstr "Union[:class:`.Member`, :class:`ClientUser`]: これはプライベートメッセージコンテキストでは :class:`ClientUser` を返すことを除いて、 :attr:`.Guild.me` と同様です。"

#: ../../docstring of discord.ext.commands.Context.permissions:1
msgid "Returns the resolved permissions for the invoking user in this channel. Shorthand for :meth:`.abc.GuildChannel.permissions_for` or :attr:`.Interaction.permissions`."
msgstr "このチャンネルでのコマンドを実行したユーザーの解決された権限を返します。 :meth:`.abc.GuildChannel.permissions_for` と :attr:`.Interaction.permissions` のショートカットです。"

#: ../../docstring of discord.ext.commands.Context.permissions:6
#: ../../docstring of discord.ext.commands.Context.bot_permissions:13
msgid ":class:`.Permissions`"
msgstr ":class:`.Permissions`"

#: ../../docstring of discord.ext.commands.Context.bot_permissions:1
msgid "Returns the resolved permissions for the bot in this channel. Shorthand for :meth:`.abc.GuildChannel.permissions_for` or :attr:`.Interaction.app_permissions`."
msgstr "このチャンネルでのボットの解決された権限を返します。 :meth:`.abc.GuildChannel.permissions_for` と :attr:`.Interaction.app_permissions` のショートカットです。"

#: ../../docstring of discord.ext.commands.Context.bot_permissions:4
msgid "For interaction-based commands, this will reflect the effective permissions for :class:`Context` calls, which may differ from calls through other :class:`.abc.Messageable` endpoints, like :attr:`channel`."
msgstr "インタラクションベースのコマンドでは、これは :class:`Context` 呼び出しにて有効な権限を反映していて、 :attr:`channel` のような他の :class:`.abc.Messageable` に適用されるものと異なる可能性があります。"

#: ../../docstring of discord.ext.commands.Context.bot_permissions:8
msgid "Notably, sending messages, embedding links, and attaching files are always permitted, while reading messages might not be."
msgstr "特に、メッセージの送信、埋め込みリンク、ファイルの添付は常に許可されていますが、メッセージを読むことはできない場合があります。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.Context.voice_client:1
msgid "A shortcut to :attr:`.Guild.voice_client`\\, if applicable."
msgstr "該当する場合は、 :attr:`.Guild.voice_client` へのショートカット。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.Context.voice_client:3
msgid "Optional[:class:`.VoiceProtocol`]"
msgstr "Optional[:class:`.VoiceProtocol`]"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send_help:3
msgid "Shows the help command for the specified entity if given. The entity can be a command or a cog."
msgstr "指定されたエンティティのヘルプコマンドを表示します。エンティティはコマンドまたはコグになることができます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send_help:6
msgid "If no entity is given, then it'll show help for the entire bot."
msgstr "エンティティが指定されていない場合は、ボット全体のヘルプが表示されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send_help:9
msgid "If the entity is a string, then it looks up whether it's a :class:`Cog` or a :class:`Command`."
msgstr "エンティティが文字列の場合、 :class:`Cog` または :class:`Command` を検索します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send_help:14
msgid "Due to the way this function works, instead of returning something similar to :meth:`~.commands.HelpCommand.command_not_found` this returns ``None`` on bad input or no help command."
msgstr "この関数の動作によっては、 :meth:`~.commands.HelpCommand.command_not_found` のようなものを返す代わりに、不正な入力またはヘルプコマンドがない際に ``None`` を返します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send_help:18
msgid "The entity to show help for."
msgstr "ヘルプを表示するエンティティ。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send_help:21
msgid "The result of the help command, if any."
msgstr "もしあれば、ヘルプコマンドの結果。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.fetch_message:3
msgid "Retrieves a single :class:`~discord.Message` from the destination."
msgstr "出力先から、単一の :class:`~discord.Message` を取得します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.fetch_message:5
msgid "The message ID to look for."
msgstr "探索をするメッセージID。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.fetch_message:8
msgid "The specified message was not found."
msgstr "指定されたメッセージが見つからなかった場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.fetch_message:9
msgid "You do not have the permissions required to get a message."
msgstr "メッセージの取得に必要な権限がない場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.fetch_message:10
msgid "Retrieving the message failed."
msgstr "メッセージの取得に失敗した場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.fetch_message:12
msgid "The message asked for."
msgstr "要求されたメッセージ。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.fetch_message:13
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:20
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:91
msgid ":class:`~discord.Message`"
msgstr ":class:`~discord.Message`"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:1
msgid "Returns an :term:`asynchronous iterator` that enables receiving the destination's message history."
msgstr "出力先のメッセージ履歴を取得する :term:`asynchronous iterator` を返します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:3
msgid "You must have :attr:`~discord.Permissions.read_message_history` to do this."
msgstr "これを行うためには、 :attr:`~discord.Permissions.read_message_history` が必要です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:14
msgid "Flattening into a list: ::"
msgstr "リストにフラット化: ::"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:21
msgid "The number of messages to retrieve. If ``None``, retrieves every message in the channel. Note, however, that this would make it a slow operation."
msgstr "取得するメッセージの数。 ``None`` の場合、チャンネルのメッセージすべてを取得します。ただし、これには時間がかかります。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:25
msgid "Retrieve messages before this date or message. If a datetime is provided, it is recommended to use a UTC aware datetime. If the datetime is naive, it is assumed to be local time."
msgstr "この日付またはメッセージより前のメッセージを取得します。もしdatetimeを指定する場合、UTC基準のawareなdatetimeを利用することを推奨します。naiveなdatetimeである場合、これはローカル時間であるとみなされます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:29
msgid "Retrieve messages after this date or message. If a datetime is provided, it is recommended to use a UTC aware datetime. If the datetime is naive, it is assumed to be local time."
msgstr "この日付またはメッセージより後のメッセージを取得します。もしdatetimeを指定する場合、UTC基準のawareなdatetimeを利用することを推奨します。naiveなdatetimeである場合、これはローカル時間であるとみなされます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:33
msgid "Retrieve messages around this date or message. If a datetime is provided, it is recommended to use a UTC aware datetime. If the datetime is naive, it is assumed to be local time. When using this argument, the maximum limit is 101. Note that if the limit is an even number then this will return at most limit + 1 messages."
msgstr "この日付またはメッセージの周辺のメッセージを取得します。もしdatetimeを指定する場合、UTC基準のawareなdatetimeを利用することを推奨します。naiveなdatetimeである場合、これはローカル時間であるとみなされます。この引数を使うとき、最大限界値は101です。限界値が偶数なら最大で限界値+1のメッセージが返されることに注意してください。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:39
msgid "If set to ``True``, return messages in oldest->newest order. Defaults to ``True`` if ``after`` is specified, otherwise ``False``."
msgstr "``True`` の場合、古いものから新しいものの順で項目を返します。デフォルトは、 ``after`` が指定された場合には ``True`` で、そうでなければ ``False`` です。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:43
msgid "You do not have permissions to get channel message history."
msgstr "チャンネルのメッセージ履歴を読む権限がありません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:44
msgid "The request to get message history failed."
msgstr "メッセージ履歴の取得に失敗しました。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.history:46
msgid ":class:`~discord.Message` -- The message with the message data parsed."
msgstr ":class:`~discord.Message` -- メッセージのデータを解析されたメッセージ。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.pins:3
msgid "Retrieves all messages that are currently pinned in the channel."
msgstr "現時点でチャンネルにピン留めされている全てのメッセージを取得します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.pins:7
msgid "Due to a limitation with the Discord API, the :class:`.Message` objects returned by this method do not contain complete :attr:`.Message.reactions` data."
msgstr "Discord APIの制限により、このメソッドの返した :class:`.Message` オブジェクトには、完全な :attr:`Message.reactions` のデータが含まれていません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.pins:11
msgid "You do not have the permission to retrieve pinned messages."
msgstr "ピン留めされたメッセージの取得に必要な権限がない場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.pins:12
msgid "Retrieving the pinned messages failed."
msgstr "ピン留めされたメッセージの取得に失敗しました。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.pins:14
msgid "The messages that are currently pinned."
msgstr "現時点でピン留めされているメッセージ。"

#: ../../../discord/ext/commands/context.py:docstring of discord.abc.Messageable.pins:15
msgid "List[:class:`~discord.Message`]"
msgstr "List[:class:`~discord.Message`]"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:3
msgid "A shortcut method to :meth:`send` to reply to the :class:`~discord.Message` referenced by this context."
msgstr "このコンテキストで参照されている :class:`~discord.Message` に返信するための、 :meth:`send` のショートカットメソッド。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:6
msgid "For interaction based contexts, this is the same as :meth:`send`."
msgstr "インタラクションベースのコンテキストでは、 :meth:`send` と同じです。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:10
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:13
msgid "This function will now raise :exc:`TypeError` or :exc:`ValueError` instead of ``InvalidArgument``."
msgstr "この関数は ``InvalidArgument`` の代わりに :exc:`TypeError` または :exc:`ValueError` を発生するようになりました。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:14
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:85
msgid "Sending the message failed."
msgstr "メッセージの送信に失敗しました。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:15
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:86
msgid "You do not have the proper permissions to send the message."
msgstr "メッセージを送信するための適切な権限がありません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:16
msgid "The ``files`` list is not of the appropriate size"
msgstr "``files`` リストの大きさが適切ではありません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:17
msgid "You specified both ``file`` and ``files``."
msgstr "``file`` と ``files`` の両方が指定されています。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.reply:19
#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:90
msgid "The message that was sent."
msgstr "送信されたメッセージ。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.defer:3
msgid "Defers the interaction based contexts."
msgstr "インタラクションの応答を遅らせます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.defer:5
msgid "This is typically used when the interaction is acknowledged and a secondary action will be done later."
msgstr "これは通常、インタラクションを認識した後、後で他のことを実行する場合に使われます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.defer:8
msgid "If this isn't an interaction based context then it does nothing."
msgstr "これがインタラクションベースのコンテキストでない場合、何もしません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.defer:10
msgid "Indicates whether the deferred message will eventually be ephemeral."
msgstr "遅れて送信するメッセージが一時的になるかを示します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.defer:13
msgid "Deferring the interaction failed."
msgstr "インタラクションの遅延に失敗した場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.defer:14
msgid "This interaction has already been responded to before."
msgstr "既にインタラクションに応答していた場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:3
msgid "Sends a message to the destination with the content given."
msgstr "指定された内容のメッセージを出力先に送信します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:5
msgid "This works similarly to :meth:`~discord.abc.Messageable.send` for non-interaction contexts."
msgstr "これはインタラクションベースでないコンテキストでは :meth:`~discord.abc.Messageable.send` と同様に動作します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:7
msgid "For interaction based contexts this does one of the following:"
msgstr "インタラクションベースのコンテキストでは以下のどれかを行います："

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:9
msgid ":meth:`discord.InteractionResponse.send_message` if no response has been given."
msgstr "応答が与えられていない場合 :meth:`discord.InteractionResponse.send_message` 。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:10
msgid "A followup message if a response has been given."
msgstr "応答が与えられた場合フォローアップメッセージ。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:11
msgid "Regular send if the interaction has expired"
msgstr "インタラクションが期限切れの場合通常の送信"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:17
msgid "The content of the message to send."
msgstr "送信するメッセージの内容。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:19
msgid "Indicates if the message should be sent using text-to-speech."
msgstr "メッセージが読み上げテキストで送信されるべきかどうかを示します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:21
msgid "The rich embed for the content."
msgstr "内容に埋め込む、typeが ``rich`` な埋め込み。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:23
msgid "The file to upload."
msgstr "アップロードするファイル。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:25
msgid "A list of files to upload. Must be a maximum of 10."
msgstr "アップロードするファイルのリスト。ファイル数は最大で10個まででなければなりません。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:27
msgid "The nonce to use for sending this message. If the message was successfully sent, then the message will have a nonce with this value."
msgstr "メッセージの送信時に使用するナンス値。メッセージが正常に送信された場合、このメッセージにはこの値のナンス値が含まれます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:30
msgid "If provided, the number of seconds to wait in the background before deleting the message we just sent. If the deletion fails, then it is silently ignored."
msgstr "指定すると、これはメッセージを送信したあと削除するまでにバックグラウンドで待機する秒数となります。もし削除が失敗しても、それは静かに無視されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:34
msgid "Controls the mentions being processed in this message. If this is passed, then the object is merged with :attr:`~discord.Client.allowed_mentions`. The merging behaviour only overrides attributes that have been explicitly passed to the object, otherwise it uses the attributes set in :attr:`~discord.Client.allowed_mentions`. If no object is passed at all then the defaults given by :attr:`~discord.Client.allowed_mentions` are used instead."
msgstr "処理されるメッセージ内のメンションを制御します。これが渡された場合、オブジェクトは :attr:`~discord.Client.allowed_mentions` で合併されます。これは、オブジェクトに明示的に渡された属性のみを上書きするもので、それ以外は :attr:`~discord.Client.allowed_mentions` で設定された属性が使用されます。もしオブジェクトが渡されていない場合は :attr:`~discord.Client.allowed_mentions` がデフォルトとして利用されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:43
msgid "A reference to the :class:`~discord.Message` to which you are replying, this can be created using :meth:`~discord.Message.to_reference` or passed directly as a :class:`~discord.Message`. You can control whether this mentions the author of the referenced message using the :attr:`~discord.AllowedMentions.replied_user` attribute of ``allowed_mentions`` or by setting ``mention_author``."
msgstr "返信する :class:`~discord.Message` への参照。これは、 :meth:`~discord.Message.to_reference` を使用して作成することができ、また、 :class:`~discord.Message` を直接渡すこともできます。これが返信元のメッセージの投稿者をメンションすべきかは、 ``allowed_mentions`` の :attr:`~discord.AllowedMentions.replied_user` 属性や、 ``mention_author`` パラメータで制御できます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:48
msgid "This is ignored for interaction based contexts."
msgstr "インタラクションベースのコンテキストでは無視されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:52
msgid "If set, overrides the :attr:`~discord.AllowedMentions.replied_user` attribute of ``allowed_mentions``. This is ignored for interaction based contexts."
msgstr "設定された場合、 ``allowed_mentions`` の :attr:`~discord.AllowedMentions.replied_user` 属性を上書きします。インタラクションベースのコンテキストでは無視されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:57
msgid "A Discord UI View to add to the message."
msgstr "メッセージに追加するDiscord UI ビュー。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:61
msgid "A list of embeds to upload. Must be a maximum of 10."
msgstr "送信する埋め込みのリスト。最大10個まで送信できます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:65
msgid "A list of stickers to upload. Must be a maximum of 3. This is ignored for interaction based contexts."
msgstr "送信するスタンプのリスト。最大3個まで送信できます。インタラクションベースのコンテキストでは無視されます。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:69
msgid "Whether to suppress embeds for the message. This sends the message without any embeds if set to ``True``."
msgstr "メッセージの埋め込みを抑制するかどうか。これが ``True`` に設定されている場合、埋め込みなしでメッセージを送信します。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:73
msgid "Indicates if the message should only be visible to the user who started the interaction. If a view is sent with an ephemeral message and it has no timeout set then the timeout is set to 15 minutes. **This is only applicable in contexts with an interaction**."
msgstr "メッセージがインタラクションを開始したユーザーだけに表示されるかどうか。もしビューが一時的なメッセージで送信されている、かつタイムアウトが設定されていない場合、タイムアウトは15分に設定されます。 **これはインタラクションベースのコンテキストでのみ適用されます。**"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:79
msgid "Whether to suppress push and desktop notifications for the message. This will increment the mention counter in the UI, but will not actually send a notification."
msgstr ""

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:87
msgid "The ``files`` list is not of the appropriate size."
msgstr "``files`` リストの大きさが適切でない場合。"

#: ../../../discord/ext/commands/context.py:docstring of discord.ext.commands.context.Context.send:88
msgid "You specified both ``file`` and ``files``,     or you specified both ``embed`` and ``embeds``,     or the ``reference`` object is not a :class:`~discord.Message`,     :class:`~discord.MessageReference` or :class:`~discord.PartialMessage`."
msgstr "``file`` と ``files`` の両方が指定された場合、 ``embed`` と ``embeds`` の両方が指定された場合、または ``reference`` が :class:`~discord.Message` 、 :class:`~discord.MessageReference` 、 :class:`~discord.PartialMessage` でない場合。"

#: ../../ext/commands/api.rst:422
msgid "Converters"
msgstr "コンバーター"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter:1
msgid "The base class of custom converters that require the :class:`.Context` to be passed to be useful."
msgstr ":class:`.Context` を渡す必要のあるカスタムコンバーターの基底クラス。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter:4
msgid "This allows you to implement converters that function similar to the special cased ``discord`` classes."
msgstr "これにより、スペシャルケースの ``discord`` クラスと似たコンバーターを実装できます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter:7
msgid "Classes that derive from this should override the :meth:`~.Converter.convert` method to do its conversion logic. This method must be a :ref:`coroutine <coroutine>`."
msgstr "サブクラスは :meth:`~.Converter.convert` メソッドを上書きして変換ロジックを実装すべきです。このメソッドは :ref:`coroutine <coroutine>` でないといけません。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter.convert:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter.convert:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter.convert:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter.convert:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter.convert:3
msgid "The method to override to do conversion logic."
msgstr "変換ロジックを行うために上書きすべきメソッド。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter.convert:5
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter.convert:5
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter.convert:5
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter.convert:5
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter.convert:5
msgid "If an error is found while converting, it is recommended to raise a :exc:`.CommandError` derived exception as it will properly propagate to the error handlers."
msgstr "変換中にエラーが発生した場合、エラーハンドラーに適切に伝播させるために :exc:`.CommandError` から派生する例外を送出することをおすすめします。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter.convert:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter.convert:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter.convert:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter.convert:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter.convert:9
msgid "The invocation context that the argument is being used in."
msgstr "引数が使用されている呼び出しコンテキスト。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter.convert:11
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter.convert:11
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter.convert:11
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter.convert:11
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter.convert:11
msgid "The argument that is being converted."
msgstr "変換する引数。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter.convert:14
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter.convert:14
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter.convert:14
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter.convert:14
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter.convert:14
msgid "A generic exception occurred when converting the argument."
msgstr "引数変換中に一般的なエラーが発生した場合。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Converter.convert:15
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter.convert:15
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter.convert:15
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter.convert:15
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter.convert:15
msgid "The converter failed to convert the argument."
msgstr "引数の変換に失敗した場合。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter:1
msgid "Converts to a :class:`~discord.Object`."
msgstr ":class:`~discord.Object` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter:3
msgid "The argument must follow the valid ID or mention formats (e.g. ``<@80088516616269824>``)."
msgstr "引数は有効なIDかメンション(例: ``<@80088516616269824>``)でないといけません。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter:7
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:6
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:5
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter:5
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildChannelConverter:6
msgid "The lookup strategy is as follows (in order):"
msgstr "検索は以下の順で行われます:"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:8
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:7
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildChannelConverter:8
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.TextChannelConverter:8
msgid "Lookup by ID."
msgstr "IDで検索"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ObjectConverter:10
msgid "Lookup by member, role, or channel mention."
msgstr "メンバー、ロール、またはチャネルのメンションで検索。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:1
msgid "Converts to a :class:`~discord.Member`."
msgstr ":class:`~discord.Member` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildChannelConverter:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.TextChannelConverter:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.VoiceChannelConverter:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.StageChannelConverter:5
msgid "All lookups are via the local guild. If in a DM context, then the lookup is done by the global cache."
msgstr "すべて検索は現在のギルドで行われます。DMでは、グローバルキャッシュが使用されます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:8
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildChannelConverter:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.TextChannelConverter:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.VoiceChannelConverter:9
msgid "Lookup by mention."
msgstr "メンションで検索"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:10
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:9
msgid "Lookup by username#discriminator (deprecated)."
msgstr ""

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:11
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:10
msgid "Lookup by username#0 (deprecated, only gets users that migrated from their discriminator)."
msgstr ""

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:12
msgid "Lookup by guild nickname."
msgstr ""

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:13
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:11
msgid "Lookup by global name."
msgstr ""

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:14
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:12
msgid "Lookup by user name."
msgstr ""

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:16
msgid "Raise :exc:`.MemberNotFound` instead of generic :exc:`.BadArgument`"
msgstr "一般的な :exc:`.BadArgument` の代わりに :exc:`.MemberNotFound` を発生させます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:19
msgid "This converter now lazily fetches members from the gateway and HTTP APIs, optionally caching the result if :attr:`.MemberCacheFlags.joined` is enabled."
msgstr "このコンバータは、ゲートウェイやHTTP APIからメンバーを取得でき、 :attr:`.MemberCacheFlags.joined` が有効な場合には結果がキャッシュされるようになりました。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MemberConverter:23
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:21
msgid "Looking up users by discriminator will be removed in a future version due to the removal of discriminators in an API change."
msgstr ""

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:1
msgid "Converts to a :class:`~discord.User`."
msgstr ":class:`~discord.User` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:3
msgid "All lookups are via the global user cache."
msgstr "すべての検索はグローバルユーザーキャッシュを介して行われます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:14
msgid "Raise :exc:`.UserNotFound` instead of generic :exc:`.BadArgument`"
msgstr "一般的な :exc:`.BadArgument` の代わりに :exc:`.UserNotFound` を発生させます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.UserConverter:17
msgid "This converter now lazily fetches users from the HTTP APIs if an ID is passed and it's not available in cache."
msgstr "このコンバータは、ID が渡され、キャッシュされていない場合、HTTP API からユーザーを取得するようになりました。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter:1
msgid "Converts to a :class:`discord.Message`."
msgstr ":class:`discord.Message` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter:7
msgid "Lookup by \"{channel ID}-{message ID}\" (retrieved by shift-clicking on \"Copy ID\")"
msgstr "\"{チャンネルID}-{メッセージID}\" で検索 (\"Copy ID\" をシフト-クリック)"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter:8
msgid "Lookup by message ID (the message **must** be in the context channel)"
msgstr "メッセージIDによる検索 (メッセージはコンテキストチャンネル内で **なければなりません**)"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter:9
msgid "Lookup by message URL"
msgstr "メッセージURLで検索"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.MessageConverter:11
msgid "Raise :exc:`.ChannelNotFound`, :exc:`.MessageNotFound` or :exc:`.ChannelNotReadable` instead of generic :exc:`.BadArgument`"
msgstr ":exc:`.ChannelNotFound`, :exc:`.MessageNotFound` または :exc:`.ChannelNotReadable` を一般的な :exc:`.BadArgument` の代わりに発生します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.PartialMessageConverter:1
msgid "Converts to a :class:`discord.PartialMessage`."
msgstr ":class:`discord.PartialMessage` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.PartialMessageConverter:5
msgid "The creation strategy is as follows (in order):"
msgstr "作成は以下の順で行われます:"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.PartialMessageConverter:7
msgid "By \"{channel ID}-{message ID}\" (retrieved by shift-clicking on \"Copy ID\")"
msgstr "\"{チャンネルID}-{メッセージID}\" (\"Copy ID\" をシフト-クリック)"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.PartialMessageConverter:8
msgid "By message ID (The message is assumed to be in the context channel.)"
msgstr "メッセージID (メッセージは現在のチャンネル内であると推定されます。)"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.PartialMessageConverter:9
msgid "By message URL"
msgstr "メッセージURL"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildChannelConverter:1
msgid "Converts to a :class:`~discord.abc.GuildChannel`."
msgstr ":class:`~discord.abc.GuildChannel` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildChannelConverter:10
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ThreadConverter:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildStickerConverter:9
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ScheduledEventConverter:10
msgid "Lookup by name."
msgstr "名前で検索"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.TextChannelConverter:1
msgid "Converts to a :class:`~discord.TextChannel`."
msgstr ":class:`~discord.TextChannel` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.TextChannelConverter:10
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.VoiceChannelConverter:10
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.StageChannelConverter:12
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.CategoryChannelConverter:10
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ForumChannelConverter:10
msgid "Lookup by name"
msgstr "名前 で検索"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.TextChannelConverter:12
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.VoiceChannelConverter:12
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.CategoryChannelConverter:12
msgid "Raise :exc:`.ChannelNotFound` instead of generic :exc:`.BadArgument`"
msgstr "一般的な :exc:`.BadArgument` の代わりに :exc:`.ChannelNotFound` を発生させます"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.VoiceChannelConverter:1
msgid "Converts to a :class:`~discord.VoiceChannel`."
msgstr ":class:`~discord.VoiceChannel` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.StageChannelConverter:1
msgid "Converts to a :class:`~discord.StageChannel`."
msgstr ":class:`~discord.StageChannel` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.CategoryChannelConverter:1
msgid "Converts to a :class:`~discord.CategoryChannel`."
msgstr ":class:`~discord.CategoryChannel` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ForumChannelConverter:1
msgid "Converts to a :class:`~discord.ForumChannel`."
msgstr ":class:`~discord.ForumChannel` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.InviteConverter:1
msgid "Converts to a :class:`~discord.Invite`."
msgstr ":class:`~discord.Invite` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.InviteConverter:3
msgid "This is done via an HTTP request using :meth:`.Bot.fetch_invite`."
msgstr ":meth:`.Bot.fetch_invite` を使用したHTTP リクエストによって行われます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.InviteConverter:5
msgid "Raise :exc:`.BadInviteArgument` instead of generic :exc:`.BadArgument`"
msgstr "一般的な :exc:`.BadArgument` の代わりに :exc:`.BadInviteArgument` を発生させます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildConverter:1
msgid "Converts to a :class:`~discord.Guild`."
msgstr ":class:`~discord.Guild` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildConverter:6
msgid "Lookup by name. (There is no disambiguation for Guilds with multiple matching names)."
msgstr "名前で検索。(名前が一致するギルドの曖昧さ回避は行われません。)"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.RoleConverter:1
msgid "Converts to a :class:`~discord.Role`."
msgstr ":class:`~discord.Role` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.RoleConverter:3
msgid "All lookups are via the local guild. If in a DM context, the converter raises :exc:`.NoPrivateMessage` exception."
msgstr "すべて検索は現在のギルドで行われます。DMでは、 :exc:`.NoPrivateMessage` 例外が送出されます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.RoleConverter:12
msgid "Raise :exc:`.RoleNotFound` instead of generic :exc:`.BadArgument`"
msgstr "一般的な :exc:`.BadArgument` の代わりに :exc:`.RoleNotFound` を発生させます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GameConverter:1
msgid "Converts to a :class:`~discord.Game`."
msgstr ":class:`~discord.Game` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:1
msgid "Converts to a :class:`~discord.Colour`."
msgstr ":class:`~discord.Colour` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:3
msgid "Add an alias named ColorConverter"
msgstr "ColorConverter という名前のエイリアスを追加"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:6
msgid "The following formats are accepted:"
msgstr "以下のフォーマットが利用可能です:"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:8
msgid "``0x<hex>``"
msgstr "``0x<hex>``"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:9
msgid "``#<hex>``"
msgstr "``#<hex>``"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:10
msgid "``0x#<hex>``"
msgstr "``0x#<hex>``"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:11
msgid "``rgb(<number>, <number>, <number>)``"
msgstr "``rgb(<数値>, <数値>, <数値>)``"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:12
msgid "Any of the ``classmethod`` in :class:`~discord.Colour`"
msgstr ":class:`~discord.Colour` の ``classmethod`` のどれか"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:14
msgid "The ``_`` in the name can be optionally replaced with spaces."
msgstr "名前の ``_`` は任意でスペースに置き換えることができます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:16
msgid "Like CSS, ``<number>`` can be either 0-255 or 0-100% and ``<hex>`` can be either a 6 digit hex number or a 3 digit hex shortcut (e.g. #fff)."
msgstr "CSSのように、 ``<number>`` は0-255か0-100%で指定でき、 ``<hex>`` は6桁の16進数表記か3桁のショートカット (例: #fff)で指定できます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:19
msgid "Raise :exc:`.BadColourArgument` instead of generic :exc:`.BadArgument`"
msgstr "一般的な :exc:`.BadArgument` の代わりに :exc:`.BadColourArgument` を発生させます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ColourConverter:22
msgid "Added support for ``rgb`` function and 3-digit hex shortcuts"
msgstr "``rgb`` 関数と3桁の16進数ショートカットのサポートを追加しました。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.EmojiConverter:1
msgid "Converts to a :class:`~discord.Emoji`."
msgstr ":class:`~discord.Emoji` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.EmojiConverter:3
#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildStickerConverter:3
msgid "All lookups are done for the local guild first, if available. If that lookup fails, then it checks the client's global cache."
msgstr "すべて検索は利用できる場合まず現在のギルドで行われます。失敗した場合、グローバルキャッシュが使用されます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.EmojiConverter:9
msgid "Lookup by extracting ID from the emoji."
msgstr "絵文字からIDを抽出して検索。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.EmojiConverter:12
msgid "Raise :exc:`.EmojiNotFound` instead of generic :exc:`.BadArgument`"
msgstr "一般的な :exc:`.BadArgument` の代わりに :exc:`.EmojiNotFound` を発生させます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.PartialEmojiConverter:1
msgid "Converts to a :class:`~discord.PartialEmoji`."
msgstr ":class:`~discord.PartialEmoji` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.PartialEmojiConverter:3
msgid "This is done by extracting the animated flag, name and ID from the emoji."
msgstr "これは絵文字からアニメーションフラグ、名前、IDを抽出することによって行われます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.PartialEmojiConverter:5
msgid "Raise :exc:`.PartialEmojiConversionFailure` instead of generic :exc:`.BadArgument`"
msgstr "一般的な :exc:`.BadArgument` の代わりに :exc:`.PartialEmojiConversionFailure` を発生させます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ThreadConverter:1
msgid "Converts to a :class:`~discord.Thread`."
msgstr ":class:`~discord.Thread` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ThreadConverter:3
msgid "All lookups are via the local guild."
msgstr "すべての検索はローカルギルドを経由して行われます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.GuildStickerConverter:1
msgid "Converts to a :class:`~discord.GuildSticker`."
msgstr ":class:`~discord.GuildSticker` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ScheduledEventConverter:1
msgid "Converts to a :class:`~discord.ScheduledEvent`."
msgstr ":class:`~discord.ScheduledEvent` に変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ScheduledEventConverter:3
msgid "Lookups are done for the local guild if available. Otherwise, for a DM context, lookup is done by the global cache."
msgstr "可能な場合は、ローカルギルドに対して検索が行われます。それ以外の場合は、DMコンテキストでは、ルックアップはグローバルキャッシュによって行われます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.ScheduledEventConverter:9
msgid "Lookup by url."
msgstr "URL で検索する。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.clean_content:1
msgid "Converts the argument to mention scrubbed version of said content."
msgstr "引数を、メンションを削ぎ落とされたコンテンツに変換します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.clean_content:4
msgid "This behaves similarly to :attr:`~discord.Message.clean_content`."
msgstr "これは :attr:`~discord.Message.clean_content` と同様に動作します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.clean_content:8
msgid "Whether to clean channel mentions."
msgstr "チャンネルのメンションを削除するかどうか。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.clean_content:14
msgid "Whether to use nicknames when transforming mentions."
msgstr "メンションを変換する際にニックネームを使用するかどうか。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.clean_content:20
msgid "Whether to also escape special markdown characters."
msgstr "特殊なマークダウン文字もエスケープするかどうか。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.clean_content:26
msgid "Whether to also remove special markdown characters. This option is not supported with ``escape_markdown``"
msgstr "特殊なマークダウン文字も除去するかどうか。このオプションは ``escape_markdown`` と一緒にはサポートされていません。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Greedy:1
msgid "A special converter that greedily consumes arguments until it can't. As a consequence of this behaviour, most input errors are silently discarded, since it is used as an indicator of when to stop parsing."
msgstr "できなくなるまで貪欲に引数を消費する特別なコンバータ。 この挙動の結果として、ほとんどの入力エラーは解析をいつ停止するかの指標として使用されるため無視されます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Greedy:5
msgid "When a parser error is met the greedy converter stops converting, undoes the internal string parsing routine, and continues parsing regularly."
msgstr "解析エラーに遭遇すると、貪欲なコンバータは変換を停止し、内部の文字列の解析ルーチンを取り消し、通常の解析を続けます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Greedy:8
msgid "For example, in the following code:"
msgstr "例えば、以下のコードです:"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Greedy:16
msgid "An invocation of ``[p]test 1 2 3 4 5 6 hello`` would pass ``numbers`` with ``[1, 2, 3, 4, 5, 6]`` and ``reason`` with ``hello``\\."
msgstr "``[p]test 1 2 3 4 5 6 hello`` で呼び出すと ``numbers`` に ``[1, 2, 3, 4, 5, 6]`` を、 ``reason`` に ``hello`` を渡します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Greedy:19
msgid "For more information, check :ref:`ext_commands_special_converters`."
msgstr "詳細については、 :ref:`ext_commands_special_converters` を参照してください。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Greedy:23
msgid "For interaction based contexts the conversion error is propagated rather than swallowed due to the difference in user experience with application commands."
msgstr "インタラクションベースのコンテキストではアプリケーションコマンドでのユーザーエクスペリエンスの差異のため変換エラーは無視されず伝播されます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:1
msgid "A special converter that can be applied to a parameter to require a numeric or string type to fit within the range provided."
msgstr "与えられた範囲内に収まる数値または文字列型を必要とするパラメータに適用できる特別なコンバーター。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:4
msgid "During type checking time this is equivalent to :obj:`typing.Annotated` so type checkers understand the intent of the code."
msgstr "型チェック時には型チェッカがコードの意図を理解できるよう :obj:`typing.Annotated` と同様になります。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:7
msgid "Some example ranges:"
msgstr "例:"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:9
msgid "``Range[int, 10]`` means the minimum is 10 with no maximum."
msgstr "``Range[int, 10]`` は最小値10、最大値なしを意味します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:10
msgid "``Range[int, None, 10]`` means the maximum is 10 with no minimum."
msgstr "``Range[int, None, 10]`` は最小値なし、最大値10を意味します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:11
msgid "``Range[int, 1, 10]`` means the minimum is 1 and the maximum is 10."
msgstr "``Range[int, 1, 10]`` は最小値1、最大値10を意味します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:12
msgid "``Range[float, 1.0, 5.0]`` means the minimum is 1.0 and the maximum is 5.0."
msgstr ""

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:13
msgid "``Range[str, 1, 10]`` means the minimum length is 1 and the maximum length is 10."
msgstr ""

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:15
msgid "Inside a :class:`HybridCommand` this functions equivalently to :class:`discord.app_commands.Range`."
msgstr ":class:`HybridCommand` 内では、この関数は :class:`discord.app_commands.Range` と同様に動作します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.Range:17
msgid "If the value cannot be converted to the provided type or is outside the given range, :class:`~.ext.commands.BadArgument` or :class:`~.ext.commands.RangeError` is raised to the appropriate error handlers respectively."
msgstr "もし値が渡された型に変換できず、または指定された範囲外である場合、:class:`~.ext.commands.BadArgument` や :class:`~.ext.commands.RangeError` が適切なエラーハンドラに送出されます。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.run_converters:3
msgid "Runs converters for a given converter, argument, and parameter."
msgstr "指定したコンバータ、引数、パラメータのコンバータを実行します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.run_converters:5
msgid "This function does the same work that the library does under the hood."
msgstr "この関数は、内部的にはライブラリと同じように動作します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.run_converters:9
msgid "The invocation context to run the converters under."
msgstr "コンバータを実行する呼び出しコンテキスト。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.run_converters:11
msgid "The converter to run, this corresponds to the annotation in the function."
msgstr "実行するコンバータで、関数の注釈に対応します。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.run_converters:13
msgid "The argument to convert to."
msgstr "変換する引数。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.run_converters:15
msgid "The parameter being converted. This is mainly for error reporting."
msgstr "変換されるパラメータ。これは主にエラー報告用です。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.run_converters:18
msgid "The converter failed to convert."
msgstr "コンバータが変換に失敗しました。"

#: ../../../discord/ext/commands/converter.py:docstring of discord.ext.commands.converter.run_converters:20
msgid "The resulting conversion."
msgstr "変換の結果"

#: ../../ext/commands/api.rst:550
msgid "Flag Converter"
msgstr "Flag Converter"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter:1
msgid "A converter that allows for a user-friendly flag syntax."
msgstr "ユーザに優しいフラグ構文を可能にするコンバータ。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter:3
msgid "The flags are defined using :pep:`526` type annotations similar to the :mod:`dataclasses` Python module. For more information on how this converter works, check the appropriate :ref:`documentation <ext_commands_flag_converter>`."
msgstr "フラグはPythonモジュールの :mod:`dataclasses` と同様に :pep:`526` の型アノテーションを用いて定義されています。どのようにしてこのコンバータが動作するかの詳細については、当該の :ref:`ドキュメント <ext_commands_flag_converter>` を確認してください。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter:12
msgid "Returns an iterator of ``(flag_name, flag_value)`` pairs. This allows it to be, for example, constructed as a dict or a list of pairs. Note that aliases are not shown."
msgstr "``(flag_name, flag_value)`` ペアのイテレータを返します。 これにより、例えば、辞書型やペアのリストに変換できます。別名は表示されないことに注意してください。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter:18
msgid "A class parameter to toggle case insensitivity of the flag parsing. If ``True`` then flags are parsed in a case insensitive manner. Defaults to ``False``."
msgstr "フラグ解析で大文字と小文字を区別しないようにするクラスパラメータ。 ``True`` の場合、フラグは大文字と小文字を区別しない方法で解析されます。デフォルトは ``False`` です。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter:22
msgid "The prefix that all flags must be prefixed with. By default there is no prefix."
msgstr "すべてのフラグに付いていなければならない接頭辞。デフォルトでは接頭辞はありません。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter:25
msgid "The delimiter that separates a flag's argument from the flag's name. By default this is ``:``."
msgstr "フラグの引数とフラグの名前を区切る区切り文字。デフォルトでは ``:`` です。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter.get_flags:1
msgid "Dict[:class:`str`, :class:`Flag`]: A mapping of flag name to flag object this converter has."
msgstr "Dict[:class:`str`, :class:`Flag`]: このコンバータが持つフラグ名とフラグオブジェクトのマッピング。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter.convert:3
msgid "The method that actually converters an argument to the flag mapping."
msgstr "フラグのマッピングに実際に引数を変換するメソッド。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter.convert:7
msgid "The argument to convert from."
msgstr "変換する引数。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter.convert:10
msgid "A flag related parsing error."
msgstr "フラグ関連の解析エラー。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter.convert:12
msgid "The flag converter instance with all flags parsed."
msgstr "すべての解析済みフラグ付きのフラグコンバータインスタンス。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.FlagConverter.convert:13
msgid ":class:`FlagConverter`"
msgstr ":class:`FlagConverter`"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:1
msgid "Represents a flag parameter for :class:`FlagConverter`."
msgstr ":class:`FlagConverter` のフラグパラメータを表します。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:3
msgid "The :func:`~discord.ext.commands.flag` function helps create these flag objects, but it is not necessary to do so. These cannot be constructed manually."
msgstr ":func:`~discord.ext.commands.flag` 関数はこれらのフラグオブジェクトを作成するのに役立ちますが、必須ではありません。これらは手動で構築することはできません。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:9
msgid "The name of the flag."
msgstr "フラグの名前"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:15
msgid "The aliases of the flag name."
msgstr "フラグ名のエイリアス"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:21
msgid "The attribute in the class that corresponds to this flag."
msgstr "このフラグに対応するクラスの属性。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:27
msgid "The default value of the flag, if available."
msgstr "利用可能であれば、フラグのデフォルト値。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:29
#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:35
msgid "Any"
msgstr "Any"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:33
msgid "The underlying evaluated annotation of the flag."
msgstr "基礎評価済みのフラグのアノテーション。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:39
msgid "The maximum number of arguments the flag can accept. A negative value indicates an unlimited amount of arguments."
msgstr "フラグが受け付けられる引数の最大数。負の値は、引数の量が無制限であることを示します。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:46
msgid "Whether multiple given values overrides the previous value."
msgstr "指定された重複する値が前の値を上書きするかどうか。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.Flag:52
#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.flag:22
msgid "The description of the flag. Shown for hybrid commands when they're used as application commands."
msgstr "フラグの説明。ハイブリッドコマンドがアプリケーションコマンドとして使用されたときに表示されます。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.Flag.required:1
msgid "Whether the flag is required."
msgstr "フラグが必須かどうか。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.Flag.required:3
msgid "A required flag has no default value."
msgstr "必須フラグにはデフォルト値はありません。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.flag:1
msgid "Override default functionality and parameters of the underlying :class:`FlagConverter` class attributes."
msgstr ":class:`FlagConverter` クラス属性のデフォルトの機能とパラメータを上書きします。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.flag:4
msgid "The flag name. If not given, defaults to the attribute name."
msgstr "フラグ名。指定しない場合、デフォルトでは属性名です。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.flag:6
msgid "Aliases to the flag name. If not given no aliases are set."
msgstr "フラグ名の別名。渡されない場合別名は設定されません。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.flag:8
msgid "The default parameter. This could be either a value or a callable that takes :class:`Context` as its sole parameter. If not given then it defaults to the default value given to the attribute."
msgstr "デフォルトパラメータ。これは値または :class:`Context` を引数として取る呼び出し可能オブジェクトでないといけません。指定されていない場合は、属性の既定値が使用されます。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.flag:12
msgid "The maximum number of arguments the flag can accept. A negative value indicates an unlimited amount of arguments. The default value depends on the annotation given."
msgstr "フラグが受け付けられる最大の引数の数。負数を渡した場合は何個でも引数を渡せることを示します。 デフォルト値は与えられたアノテーションによって異なります。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.flag:16
msgid "Whether multiple given values overrides the previous value. The default value depends on the annotation given."
msgstr "複数の値が与えられた場合に前の値を上書きするかどうか。デフォルト値は与えられたアノテーションによって異なります。"

#: ../../../discord/ext/commands/flags.py:docstring of discord.ext.commands.flags.flag:19
msgid "The converter to use for this flag. This replaces the annotation at runtime which is transparent to type checkers."
msgstr "このフラグに使用するコンバーター。実行時にアノテーションを置き換えます。これは型チェッカに対して透過的です。"

#: ../../ext/commands/api.rst:566
msgid "Defaults"
msgstr "既定値"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.Parameter:1
msgid "A class that stores information on a :class:`Command`\\'s parameter."
msgstr ":class:`Command` のパラメータに関する情報を格納するクラス。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.Parameter:3
msgid "This is a subclass of :class:`inspect.Parameter`."
msgstr ":class:`inspect.Parameter` のサブクラスです。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.Parameter.replace:1
msgid "Creates a customized copy of the Parameter."
msgstr "パラメータのカスタマイズされたコピーを返します。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.name:1
msgid "The parameter's name."
msgstr "パラメータの名前。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.kind:1
msgid "The parameter's kind."
msgstr "パラメータの種類。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.default:1
msgid "The parameter's default."
msgstr "パラメータの既定値。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.annotation:1
msgid "The parameter's annotation."
msgstr "パラメータのアノテーション。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.required:1
msgid "Whether this parameter is required."
msgstr "パラメータが必須かどうか。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.converter:1
msgid "The converter that should be used for this parameter."
msgstr "このパラメータに使用すべきコンバーター。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.description:1
#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:20
msgid "The description of this parameter."
msgstr "このパラメータの説明。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.displayed_default:1
msgid "The displayed default in :class:`Command.signature`."
msgstr ":class:`Command.signature` で表示される既定値。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.Parameter.displayed_name:1
#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:24
msgid "The name that is displayed to the user."
msgstr ""

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.Parameter.get_default:3
msgid "Gets this parameter's default value."
msgstr "このパラメータの既定値を取得します。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.Parameter.get_default:5
msgid "The invocation context that is used to get the default argument."
msgstr "引数の既定値を取得するために使用される呼び出しコンテキスト。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:1
msgid "A way to assign custom metadata for a :class:`Command`\\'s parameter."
msgstr ":class:`Command` のパラメータにカスタムメタデータを割り当てる方法。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:7
msgid "A custom default can be used to have late binding behaviour."
msgstr "カスタムの既定値を指定すると、既定値を動的に指定できます。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:15
msgid "The converter to use for this parameter, this replaces the annotation at runtime which is transparent to type checkers."
msgstr "このフラグに使用するコンバーター。実行時にアノテーションを置き換えます。これは型チェッカに対して透過的です。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:17
msgid "The default value for the parameter, if this is a :term:`callable` or a |coroutine_link|_ it is called with a positional :class:`Context` argument."
msgstr "パラメータの既定値。これが :term:`callable` か |coroutine_link|_ の場合これは位置指定の :class:`Context` 引数を渡して呼び出されます。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:22
msgid "The displayed default in :attr:`Command.signature`."
msgstr ":attr:`Command.signature` で表示される既定値。"

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:1
msgid "param(\\*, converter=..., default=..., description=..., displayed_default=..., displayed_name=...)"
msgstr ""

#: ../../../discord/ext/commands/parameters.py:docstring of discord.ext.commands.parameters.parameter:3
msgid "An alias for :func:`parameter`."
msgstr ":func:`parameter` のエイリアス。"

#: ../../ext/commands/api.rst:579
msgid "A default :class:`Parameter` which returns the :attr:`~.Context.author` for this context."
msgstr "このコンテキストの :attr:`~.Context.author` を返すデフォルト :class:`Parameter` 。"

#: ../../ext/commands/api.rst:585
msgid "A default :class:`Parameter` which returns the :attr:`~.Context.channel` for this context."
msgstr "このコンテキストの :attr:`~.Context.channel` を返すデフォルト :class:`Parameter` 。"

#: ../../ext/commands/api.rst:591
msgid "A default :class:`Parameter` which returns the :attr:`~.Context.guild` for this context. This will never be ``None``. If the command is called in a DM context then :exc:`~discord.ext.commands.NoPrivateMessage` is raised to the error handlers."
msgstr "このコンテキストの :attr:`~.Context.guild` を返すデフォルト :class:`Parameter` 。これは ``None`` にはなりません。コマンドがDM内で呼び出された場合は :exc:`~discord.ext.commands.NoPrivateMessage` がエラーハンドラに送出されます。"

#: ../../ext/commands/api.rst:598
msgid "Exceptions"
msgstr "例外"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandError:1
msgid "The base exception type for all command related errors."
msgstr "コマンドに関連するエラーすべての基礎となる例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandError:3
msgid "This inherits from :exc:`discord.DiscordException`."
msgstr "これは :exc:`discord.DiscordException` を継承しています。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandError:5
msgid "This exception and exceptions inherited from it are handled in a special way as they are caught and passed into a special event from :class:`.Bot`\\, :func:`.on_command_error`."
msgstr "この例外及び、ここから継承された例外は、キャッチされると :class:`.Bot` の :func:`.on_command_error` に渡され、特別な方法で処理されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ConversionError:1
msgid "Exception raised when a Converter class raises non-CommandError."
msgstr "Converter クラスで、CommandErrorではない例外が発生した際に、発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ConversionError:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandNotFound:7
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.UserInputError:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MaxConcurrencyReached:3
msgid "This inherits from :exc:`CommandError`."
msgstr "これは :exc:`CommandError` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ConversionError:7
msgid "The converter that failed."
msgstr "失敗したコンバーター。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ConversionError:9
msgid ":class:`discord.ext.commands.Converter`"
msgstr ":class:`discord.ext.commands.Converter`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ConversionError:13
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandInvokeError:7
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadFlagArgument:21
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionFailed:13
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.HybridCommandError:9
msgid "The original exception that was raised. You can also get this via the ``__cause__`` attribute."
msgstr "Converter内で発生した元の例外。 ``__cause__`` からも取得できます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ConversionError:16
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandInvokeError:10
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionFailed:16
msgid ":exc:`Exception`"
msgstr ":exc:`Exception`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredArgument:1
msgid "Exception raised when parsing a command and a parameter that is required is not encountered."
msgstr "コマンドのパラメータ解析の際、要求されたパラメータに値が渡されていない場合に発生します。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredArgument:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredAttachment:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadArgument:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadUnionArgument:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:4
msgid "This inherits from :exc:`UserInputError`"
msgstr "これは :exc:`UserInputError` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredArgument:8
msgid "The argument that is missing."
msgstr "不足している引数"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredArgument:10
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredAttachment:12
msgid ":class:`Parameter`"
msgstr ":class:`Parameter`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredAttachment:1
msgid "Exception raised when parsing a command and a parameter that requires an attachment is not given."
msgstr "コマンドのパラメータ解析の際、添付ファイルを必要とするパラメータが与えられていない場合に発生します。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredAttachment:10
msgid "The argument that is missing an attachment."
msgstr "添付ファイルがない引数。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ArgumentParsingError:1
msgid "An exception raised when the parser fails to parse a user's input."
msgstr "パーサーがユーザーの入力の解析に失敗したときに発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ArgumentParsingError:3
msgid "This inherits from :exc:`UserInputError`."
msgstr "これは :exc:`UserInputError` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ArgumentParsingError:5
msgid "There are child classes that implement more granular parsing errors for i18n purposes."
msgstr "i18n のためにより細かい解析エラーを実装するサブクラスがあります。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.UnexpectedQuoteError:1
msgid "An exception raised when the parser encounters a quote mark inside a non-quoted string."
msgstr "パーサーが引用符で囲まれていない文字列の中で引用符に遭遇したときに発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.UnexpectedQuoteError:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.InvalidEndOfQuotedStringError:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExpectedClosingQuoteError:3
msgid "This inherits from :exc:`ArgumentParsingError`."
msgstr "これは :exc:`ArgumentParsingError` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.UnexpectedQuoteError:7
msgid "The quote mark that was found inside the non-quoted string."
msgstr "引用符で囲まれていない文字列の中に見つかった引用符。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.InvalidEndOfQuotedStringError:1
msgid "An exception raised when a space is expected after the closing quote in a string but a different character is found."
msgstr "文字列の引用符が閉じられた後に空白が期待されるとき、別の文字が見つかった場合の例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.InvalidEndOfQuotedStringError:8
msgid "The character found instead of the expected string."
msgstr "期待される文字列の代わりに見つかった文字。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExpectedClosingQuoteError:1
msgid "An exception raised when a quote character is expected but not found."
msgstr "引用符が期待されているが見つからない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExpectedClosingQuoteError:7
msgid "The quote character expected."
msgstr "期待された引用符。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadArgument:1
msgid "Exception raised when a parsing or conversion failure is encountered on an argument to pass into a command."
msgstr "コマンドの引数に渡された値の解析、または変換に失敗した場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadUnionArgument:1
msgid "Exception raised when a :data:`typing.Union` converter fails for all its associated types."
msgstr ":data:`typing.Union` コンバーターが対応するすべての型に対して失敗した場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadUnionArgument:8
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:10
msgid "The parameter that failed being converted."
msgstr "変換に失敗したパラメータ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadUnionArgument:10
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:12
msgid ":class:`inspect.Parameter`"
msgstr ":class:`inspect.Parameter`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadUnionArgument:14
msgid "A tuple of converters attempted in conversion, in order of failure."
msgstr "変換しようとして失敗したコンバーターの、失敗した順のタプル。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadUnionArgument:16
msgid "Tuple[Type, ``...``]"
msgstr "Tuple[Type, ``...``]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadUnionArgument:20
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:22
msgid "A list of errors that were caught from failing the conversion."
msgstr "変換失敗時に捕捉されたエラーのリスト。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadUnionArgument:22
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:24
msgid "List[:class:`CommandError`]"
msgstr "List[:class:`CommandError`]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:1
msgid "Exception raised when a :data:`typing.Literal` converter fails for all its associated values."
msgstr ":data:`typing.Literal` コンバーターが対応するすべての値に対して失敗した場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:16
msgid "A tuple of values compared against in conversion, in order of failure."
msgstr "比較が失敗した値の、失敗した順のタプル。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:18
msgid "Tuple[Any, ``...``]"
msgstr "Tuple[Any, ``...``]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadLiteralArgument:28
msgid "The argument's value that failed to be converted. Defaults to an empty string."
msgstr ""

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.PrivateMessageOnly:1
msgid "Exception raised when an operation does not work outside of private message contexts."
msgstr "プライベートメッセージコンテキスト外で、要求された処理が実行できない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.PrivateMessageOnly:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NoPrivateMessage:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NotOwner:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingPermissions:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingPermissions:4
msgid "This inherits from :exc:`CheckFailure`"
msgstr "これは :exc:`CheckFailure` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NoPrivateMessage:1
msgid "Exception raised when an operation does not work in private message contexts."
msgstr "プライベートメッセージコンテキストで、要求された処理が実行できない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CheckFailure:1
msgid "Exception raised when the predicates in :attr:`.Command.checks` have failed."
msgstr ":attr:`.Command.checks` のチェックが失敗した場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CheckFailure:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.DisabledCommand:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandInvokeError:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandOnCooldown:3
msgid "This inherits from :exc:`CommandError`"
msgstr "これは :exc:`CommandError` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CheckAnyFailure:1
msgid "Exception raised when all predicates in :func:`check_any` fail."
msgstr ":func:`check_any` のチェックがすべて失敗した場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CheckAnyFailure:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NSFWChannelRequired:3
msgid "This inherits from :exc:`CheckFailure`."
msgstr "これは :exc:`CheckFailure` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CheckAnyFailure:9
msgid "A list of errors that were caught during execution."
msgstr "実行時に捕捉されたエラーのリスト。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CheckAnyFailure:11
msgid "List[:class:`CheckFailure`]"
msgstr "List[:class:`CheckFailure`]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CheckAnyFailure:15
msgid "A list of check predicates that failed."
msgstr "失敗したチェックのリスト。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CheckAnyFailure:17
msgid "List[Callable[[:class:`Context`], :class:`bool`]]"
msgstr "List[Callable[[:class:`Context`], :class:`bool`]]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandNotFound:1
msgid "Exception raised when a command is attempted to be invoked but no command under that name is found."
msgstr "コマンドを呼び出す際に、指定された名前を持つコマンドが存在していなかった場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandNotFound:4
msgid "This is not raised for invalid subcommands, rather just the initial main command that is attempted to be invoked."
msgstr "これは無効なサブコマンドに対して発生するのではなく、呼び出されようとした最初のメインコマンドに対し発生します。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.DisabledCommand:1
msgid "Exception raised when the command being invoked is disabled."
msgstr "呼び出そうとしたコマンドが無効化されていた際に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandInvokeError:1
msgid "Exception raised when the command being invoked raised an exception."
msgstr "呼び出そうとしたコマンドが例外を送出した場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.TooManyArguments:1
msgid "Exception raised when the command was passed too many arguments and its :attr:`.Command.ignore_extra` attribute was not set to ``True``."
msgstr "コマンドに過剰の引数が渡され、 :attr:`.Command.ignore_extra` 属性が ``True`` でない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.UserInputError:1
msgid "The base exception type for errors that involve errors regarding user input."
msgstr "ユーザー入力の誤りに関するエラーの基礎例外タイプ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandOnCooldown:1
msgid "Exception raised when the command being invoked is on cooldown."
msgstr "呼び出そうとしたコマンドがクールダウン中の場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandOnCooldown:7
msgid "A class with attributes ``rate`` and ``per`` similar to the :func:`.cooldown` decorator."
msgstr ":func:`.cooldown` デコレータに似た ``rate`` と ``per`` 属性を持つクラス。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandOnCooldown:10
msgid ":class:`~discord.app_commands.Cooldown`"
msgstr ":class:`~discord.app_commands.Cooldown`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandOnCooldown:14
msgid "The type associated with the cooldown."
msgstr "クールダウンに関連付けられたタイプ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandOnCooldown:16
msgid ":class:`BucketType`"
msgstr ":class:`BucketType`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandOnCooldown:20
msgid "The amount of seconds to wait before you can retry again."
msgstr "再試行する前に待たないといけない秒数。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MaxConcurrencyReached:1
msgid "Exception raised when the command being invoked has reached its maximum concurrency."
msgstr "呼び出そうとしたコマンドがその最大同時実行数に達している場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MaxConcurrencyReached:7
msgid "The maximum number of concurrent invokers allowed."
msgstr "同時に実行できる最大の数。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MaxConcurrencyReached:13
msgid "The bucket type passed to the :func:`.max_concurrency` decorator."
msgstr ":func:`.max_concurrency` デコレータに渡されたバケットタイプ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MaxConcurrencyReached:15
msgid ":class:`.BucketType`"
msgstr ":class:`.BucketType`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NotOwner:1
msgid "Exception raised when the message author is not the owner of the bot."
msgstr "メッセージ送信者がボットの所有者でない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MessageNotFound:1
msgid "Exception raised when the message provided was not found in the channel."
msgstr "渡されたメッセージがチャンネル内に見つからなかったときに発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MessageNotFound:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MemberNotFound:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.GuildNotFound:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.UserNotFound:4
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ChannelNotFound:3
msgid "This inherits from :exc:`BadArgument`"
msgstr "これは :exc:`BadArgument` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MessageNotFound:9
msgid "The message supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかったメッセージ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MemberNotFound:1
msgid "Exception raised when the member provided was not found in the bot's cache."
msgstr "渡されたメンバーがボットのキャッシュ内に見つからなかったときに発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MemberNotFound:10
msgid "The member supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかったメンバー。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.GuildNotFound:1
msgid "Exception raised when the guild provided was not found in the bot's cache."
msgstr "渡されたギルドがボットのキャッシュ内に見つからなかったときに発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.GuildNotFound:9
msgid "The guild supplied by the called that was not found"
msgstr "呼び出し元から渡された、見つからなかったギルド。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.UserNotFound:1
msgid "Exception raised when the user provided was not found in the bot's cache."
msgstr "渡されたユーザーがボットのキャッシュ内に見つからなかったときに発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.UserNotFound:10
msgid "The user supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかったユーザー。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ChannelNotFound:1
msgid "Exception raised when the bot can not find the channel."
msgstr "ボットがチャンネルを見つけられない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ChannelNotFound:9
msgid "The channel supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかったチャンネル。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ChannelNotFound:11
msgid "Union[:class:`int`, :class:`str`]"
msgstr "Union[:class:`int`, :class:`str`]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ChannelNotReadable:1
msgid "Exception raised when the bot does not have permission to read messages in the channel."
msgstr "ボットがチャンネル内のメッセージを読み取る権限がない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ChannelNotReadable:10
msgid "The channel supplied by the caller that was not readable"
msgstr "呼び出し元から渡された、閲覧できないチャンネル。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ChannelNotReadable:12
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NSFWChannelRequired:11
msgid "Union[:class:`.abc.GuildChannel`, :class:`.Thread`]"
msgstr "Union[:class:`.abc.GuildChannel`, :class:`.Thread`]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ThreadNotFound:1
msgid "Exception raised when the bot can not find the thread."
msgstr "ボットがスレッドを見つけられない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ThreadNotFound:9
msgid "The thread supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかったスレッド。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadColourArgument:1
msgid "Exception raised when the colour is not valid."
msgstr "色が有効でない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadColourArgument:9
msgid "The colour supplied by the caller that was not valid"
msgstr "呼び出し元から渡された、無効な色。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RoleNotFound:1
msgid "Exception raised when the bot can not find the role."
msgstr "ボットがロールを見つけられない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RoleNotFound:9
msgid "The role supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかったロール。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadInviteArgument:1
msgid "Exception raised when the invite is invalid or expired."
msgstr "招待が無効または期限切れの場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadInviteArgument:9
msgid "The invite supplied by the caller that was not valid"
msgstr "呼び出し元から渡された、無効な招待。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.EmojiNotFound:1
msgid "Exception raised when the bot can not find the emoji."
msgstr "ボットが絵文字を見つけられない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.EmojiNotFound:9
msgid "The emoji supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかった絵文字。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.PartialEmojiConversionFailure:1
msgid "Exception raised when the emoji provided does not match the correct format."
msgstr "渡された絵文字が正しい形式でない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.PartialEmojiConversionFailure:10
msgid "The emoji supplied by the caller that did not match the regex"
msgstr "呼び出し元から渡された、正規表現にマッチしない絵文字。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.GuildStickerNotFound:1
msgid "Exception raised when the bot can not find the sticker."
msgstr "ボットがスタンプを見つけられない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.GuildStickerNotFound:9
msgid "The sticker supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかったスタンプ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ScheduledEventNotFound:1
msgid "Exception raised when the bot can not find the scheduled event."
msgstr "ボットがスケジュールイベントを見つけられない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ScheduledEventNotFound:9
msgid "The event supplied by the caller that was not found"
msgstr "呼び出し元から渡された、見つからなかったスケジュールイベント。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadBoolArgument:1
msgid "Exception raised when a boolean argument was not convertable."
msgstr "真偽値の引数が変換可能でない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadBoolArgument:9
msgid "The boolean argument supplied by the caller that is not in the predefined list"
msgstr "呼び出し元から渡された、事前に定義されたリスト内にない真偽値の引数の値。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RangeError:1
msgid "Exception raised when an argument is out of range."
msgstr "引数が範囲外の場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RangeError:9
msgid "The minimum value expected or ``None`` if there wasn't one"
msgstr "期待された最小値。存在しない場合 ``None`` です。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RangeError:11
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RangeError:17
msgid "Optional[Union[:class:`int`, :class:`float`]]"
msgstr "Optional[Union[:class:`int`, :class:`float`]]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RangeError:15
msgid "The maximum value expected or ``None`` if there wasn't one"
msgstr "期待された最大値。存在しない場合 ``None`` です。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RangeError:21
msgid "The value that was out of range."
msgstr "範囲外であった値。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.RangeError:23
msgid "Union[:class:`int`, :class:`float`, :class:`str`]"
msgstr "Union[:class:`int`, :class:`float`, :class:`str`]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingPermissions:1
msgid "Exception raised when the command invoker lacks permissions to run a command."
msgstr "コマンド実行者がコマンドを実行する権限を持っていない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingPermissions:8
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingPermissions:8
msgid "The required permissions that are missing."
msgstr "有していない必要な権限。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingPermissions:1
msgid "Exception raised when the bot's member lacks permissions to run a command."
msgstr "ボットのメンバーがコマンドを実行する権限を持っていない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRole:1
msgid "Exception raised when the command invoker lacks a role to run a command."
msgstr "コマンド実行者がコマンドを実行するためのロールを持っていない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRole:9
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingRole:9
msgid "The required role that is missing. This is the parameter passed to :func:`~.commands.has_role`."
msgstr "見つからなかった必須のロール。これは :func:`~.commands.has_role` に渡されたパラメータと同一です。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRole:12
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingRole:12
msgid "Union[:class:`str`, :class:`int`]"
msgstr "Union[:class:`str`, :class:`int`]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingRole:1
msgid "Exception raised when the bot's member lacks a role to run a command."
msgstr "ボットのメンバーがコマンドを実行するためのロールを持っていない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingAnyRole:1
msgid "Exception raised when the command invoker lacks any of the roles specified to run a command."
msgstr "コマンド実行者がコマンドを実行するためのロールをどれも持っていない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingAnyRole:10
msgid "The roles that the invoker is missing. These are the parameters passed to :func:`~.commands.has_any_role`."
msgstr "見つからなかったロール。これは :func:`~.commands.has_any_role` に渡されたパラメータと同一です。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingAnyRole:13
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingAnyRole:13
msgid "List[Union[:class:`str`, :class:`int`]]"
msgstr "List[Union[:class:`str`, :class:`int`]]"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingAnyRole:1
msgid "Exception raised when the bot's member lacks any of the roles specified to run a command."
msgstr "ボットのメンバーがコマンドを実行するためのロールをどれも持っていない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BotMissingAnyRole:10
msgid "The roles that the bot's member is missing. These are the parameters passed to :func:`~.commands.has_any_role`."
msgstr "見つからなかったロール。これは :func:`~.commands.has_any_role` に渡されたパラメータと同一です。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NSFWChannelRequired:1
msgid "Exception raised when a channel does not have the required NSFW setting."
msgstr "チャンネルに必要な年齢制限設定がない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NSFWChannelRequired:9
msgid "The channel that does not have NSFW enabled."
msgstr "年齢制限が有効になっていないチャンネル。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.FlagError:1
msgid "The base exception type for all flag parsing related errors."
msgstr "フラグに関連するエラーすべての基礎となる例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.FlagError:3
msgid "This inherits from :exc:`BadArgument`."
msgstr "これは :exc:`BadArgument` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadFlagArgument:1
msgid "An exception raised when a flag failed to convert a value."
msgstr "フラグが値の変換に失敗したときに発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadFlagArgument:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingFlagArgument:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredFlag:3
msgid "This inherits from :exc:`FlagError`"
msgstr "これは :exc:`FlagError` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadFlagArgument:9
msgid "The flag that failed to convert."
msgstr "変換に失敗したフラグ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadFlagArgument:11
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingFlagArgument:11
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.TooManyFlags:11
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredFlag:11
msgid ":class:`~discord.ext.commands.Flag`"
msgstr ":class:`~discord.ext.commands.Flag`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadFlagArgument:15
msgid "The argument supplied by the caller that was not able to be converted."
msgstr "変換できなかった呼び出し元によって与えられた引数。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.BadFlagArgument:24
msgid ":class:`Exception`"
msgstr ":class:`Exception`"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingFlagArgument:1
msgid "An exception raised when a flag did not get a value."
msgstr "フラグが値を受け取らなかった場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingFlagArgument:9
msgid "The flag that did not get a value."
msgstr "値を受け取らなかったフラグ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.TooManyFlags:1
msgid "An exception raised when a flag has received too many values."
msgstr "フラグが過剰な値を受け取った場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.TooManyFlags:3
msgid "This inherits from :exc:`FlagError`."
msgstr "これは :exc:`FlagError` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.TooManyFlags:9
msgid "The flag that received too many values."
msgstr "過剰な値を受け取ったフラグ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.TooManyFlags:15
msgid "The values that were passed."
msgstr "渡された値。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredFlag:1
msgid "An exception raised when a required flag was not given."
msgstr "必須のフラグが渡されなかった場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.MissingRequiredFlag:9
msgid "The required flag that was not found."
msgstr "見つからなかった必須のフラグ。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionError:1
msgid "Base exception for extension related errors."
msgstr "エクステンション関連のエラーの基礎例外です。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionError:3
msgid "This inherits from :exc:`~discord.DiscordException`."
msgstr "これは :exc:`~discord.DiscordException` を継承しています。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionError:7
msgid "The extension that had an error."
msgstr "エラーが発生したエクステンション。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionAlreadyLoaded:1
msgid "An exception raised when an extension has already been loaded."
msgstr "エクステンションが既に読み込まれている場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionAlreadyLoaded:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionNotLoaded:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NoEntryPointError:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionFailed:3
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionNotFound:3
msgid "This inherits from :exc:`ExtensionError`"
msgstr "これは :exc:`ExtensionError` から継承されます。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionNotLoaded:1
msgid "An exception raised when an extension was not loaded."
msgstr "エクステンションが読み込まれていない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.NoEntryPointError:1
msgid "An exception raised when an extension does not have a ``setup`` entry point function."
msgstr "エクステンションに ``setup`` エントリーポイント関数が存在しない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionFailed:1
msgid "An exception raised when an extension failed to load during execution of the module or ``setup`` entry point."
msgstr "モジュールまたは ``setup`` エントリーポイントの実行中にエクステンションの読み込みに失敗した場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionFailed:7
#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionNotFound:10
msgid "The extension that had the error."
msgstr "エラーが発生したエクステンション。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionNotFound:1
msgid "An exception raised when an extension is not found."
msgstr "エクステンションが見つからなかった場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.ExtensionNotFound:5
msgid "Made the ``original`` attribute always None."
msgstr "``original`` 属性を常に None にしました。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandRegistrationError:1
msgid "An exception raised when the command can't be added because the name is already taken by a different command."
msgstr "コマンドが別のコマンドによって既に使用されているためコマンドが追加できない場合に発生する例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandRegistrationError:4
msgid "This inherits from :exc:`discord.ClientException`"
msgstr "これは :exc:`discord.ClientException` を継承しています。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandRegistrationError:10
msgid "The command name that had the error."
msgstr "エラーの原因となったコマンド名。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.CommandRegistrationError:16
msgid "Whether the name that conflicts is an alias of the command we try to add."
msgstr "競合する名前が追加しようとしたコマンドの別名であるか。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.HybridCommandError:1
msgid "An exception raised when a :class:`~discord.ext.commands.HybridCommand` raises an :exc:`~discord.app_commands.AppCommandError` derived exception that could not be sufficiently converted to an equivalent :exc:`CommandError` exception."
msgstr ":class:`~discord.ext.commands.HybridCommand` が :exc:`CommandError` 相当の例外に変換できない :exc:`~discord.app_commands.AppCommandError` 派生の例外を送出したときの例外。"

#: ../../../discord/ext/commands/errors.py:docstring of discord.ext.commands.errors.HybridCommandError:12
msgid ":exc:`~discord.app_commands.AppCommandError`"
msgstr ":exc:`~discord.app_commands.AppCommandError`"

#: ../../ext/commands/api.rst:779
msgid "Exception Hierarchy"
msgstr "例外の階層構造"

#: ../../ext/commands/api.rst:840
msgid ":exc:`~.DiscordException`"
msgstr ":exc:`~.DiscordException`"

#: ../../ext/commands/api.rst:834
msgid ":exc:`~.commands.CommandError`"
msgstr ":exc:`~.commands.CommandError`"

#: ../../ext/commands/api.rst:785
msgid ":exc:`~.commands.ConversionError`"
msgstr ":exc:`~.commands.ConversionError`"

#: ../../ext/commands/api.rst:816
msgid ":exc:`~.commands.UserInputError`"
msgstr ":exc:`~.commands.UserInputError`"

#: ../../ext/commands/api.rst:787
msgid ":exc:`~.commands.MissingRequiredArgument`"
msgstr ":exc:`~.commands.MissingRequiredArgument`"

#: ../../ext/commands/api.rst:788
msgid ":exc:`~.commands.MissingRequiredAttachment`"
msgstr ":exc:`~.commands.MissingRequiredAttachment`"

#: ../../ext/commands/api.rst:789
msgid ":exc:`~.commands.TooManyArguments`"
msgstr ":exc:`~.commands.TooManyArguments`"

#: ../../ext/commands/api.rst:810
msgid ":exc:`~.commands.BadArgument`"
msgstr ":exc:`~.commands.BadArgument`"

#: ../../ext/commands/api.rst:791
msgid ":exc:`~.commands.MessageNotFound`"
msgstr ":exc:`~.commands.MessageNotFound`"

#: ../../ext/commands/api.rst:792
msgid ":exc:`~.commands.MemberNotFound`"
msgstr ":exc:`~.commands.MemberNotFound`"

#: ../../ext/commands/api.rst:793
msgid ":exc:`~.commands.GuildNotFound`"
msgstr ":exc:`~.commands.GuildNotFound`"

#: ../../ext/commands/api.rst:794
msgid ":exc:`~.commands.UserNotFound`"
msgstr ":exc:`~.commands.UserNotFound`"

#: ../../ext/commands/api.rst:795
msgid ":exc:`~.commands.ChannelNotFound`"
msgstr ":exc:`~.commands.ChannelNotFound`"

#: ../../ext/commands/api.rst:796
msgid ":exc:`~.commands.ChannelNotReadable`"
msgstr ":exc:`~.commands.ChannelNotReadable`"

#: ../../ext/commands/api.rst:797
msgid ":exc:`~.commands.BadColourArgument`"
msgstr ":exc:`~.commands.BadColourArgument`"

#: ../../ext/commands/api.rst:798
msgid ":exc:`~.commands.RoleNotFound`"
msgstr ":exc:`~.commands.RoleNotFound`"

#: ../../ext/commands/api.rst:799
msgid ":exc:`~.commands.BadInviteArgument`"
msgstr ":exc:`~.commands.BadInviteArgument`"

#: ../../ext/commands/api.rst:800
msgid ":exc:`~.commands.EmojiNotFound`"
msgstr ":exc:`~.commands.EmojiNotFound`"

#: ../../ext/commands/api.rst:801
msgid ":exc:`~.commands.GuildStickerNotFound`"
msgstr ":exc:`~.commands.GuildStickerNotFound`"

#: ../../ext/commands/api.rst:802
msgid ":exc:`~.commands.ScheduledEventNotFound`"
msgstr ":exc:`~.commands.ScheduledEventNotFound`"

#: ../../ext/commands/api.rst:803
msgid ":exc:`~.commands.PartialEmojiConversionFailure`"
msgstr ":exc:`~.commands.PartialEmojiConversionFailure`"

#: ../../ext/commands/api.rst:804
msgid ":exc:`~.commands.BadBoolArgument`"
msgstr ":exc:`~.commands.BadBoolArgument`"

#: ../../ext/commands/api.rst:805
msgid ":exc:`~.commands.RangeError`"
msgstr ":exc:`~.commands.RangeError`"

#: ../../ext/commands/api.rst:806
msgid ":exc:`~.commands.ThreadNotFound`"
msgstr ":exc:`~.commands.ThreadNotFound`"

#: ../../ext/commands/api.rst:810
msgid ":exc:`~.commands.FlagError`"
msgstr ":exc:`~.commands.FlagError`"

#: ../../ext/commands/api.rst:808
msgid ":exc:`~.commands.BadFlagArgument`"
msgstr ":exc:`~.commands.BadFlagArgument`"

#: ../../ext/commands/api.rst:809
msgid ":exc:`~.commands.MissingFlagArgument`"
msgstr ":exc:`~.commands.MissingFlagArgument`"

#: ../../ext/commands/api.rst:810
msgid ":exc:`~.commands.TooManyFlags`"
msgstr ":exc:`~.commands.TooManyFlags`"

#: ../../ext/commands/api.rst:811
msgid ":exc:`~.commands.MissingRequiredFlag`"
msgstr ":exc:`~.commands.MissingRequiredFlag`"

#: ../../ext/commands/api.rst:812
msgid ":exc:`~.commands.BadUnionArgument`"
msgstr ":exc:`~.commands.BadUnionArgument`"

#: ../../ext/commands/api.rst:813
msgid ":exc:`~.commands.BadLiteralArgument`"
msgstr ":exc:`~.commands.BadLiteralArgument`"

#: ../../ext/commands/api.rst:816
msgid ":exc:`~.commands.ArgumentParsingError`"
msgstr ":exc:`~.commands.ArgumentParsingError`"

#: ../../ext/commands/api.rst:815
msgid ":exc:`~.commands.UnexpectedQuoteError`"
msgstr ":exc:`~.commands.UnexpectedQuoteError`"

#: ../../ext/commands/api.rst:816
msgid ":exc:`~.commands.InvalidEndOfQuotedStringError`"
msgstr ":exc:`~.commands.InvalidEndOfQuotedStringError`"

#: ../../ext/commands/api.rst:817
msgid ":exc:`~.commands.ExpectedClosingQuoteError`"
msgstr ":exc:`~.commands.ExpectedClosingQuoteError`"

#: ../../ext/commands/api.rst:818
msgid ":exc:`~.commands.CommandNotFound`"
msgstr ":exc:`~.commands.CommandNotFound`"

#: ../../ext/commands/api.rst:829
msgid ":exc:`~.commands.CheckFailure`"
msgstr ":exc:`~.commands.CheckFailure`"

#: ../../ext/commands/api.rst:820
msgid ":exc:`~.commands.CheckAnyFailure`"
msgstr ":exc:`~.commands.CheckAnyFailure`"

#: ../../ext/commands/api.rst:821
msgid ":exc:`~.commands.PrivateMessageOnly`"
msgstr ":exc:`~.commands.PrivateMessageOnly`"

#: ../../ext/commands/api.rst:822
msgid ":exc:`~.commands.NoPrivateMessage`"
msgstr ":exc:`~.commands.NoPrivateMessage`"

#: ../../ext/commands/api.rst:823
msgid ":exc:`~.commands.NotOwner`"
msgstr ":exc:`~.commands.NotOwner`"

#: ../../ext/commands/api.rst:824
msgid ":exc:`~.commands.MissingPermissions`"
msgstr ":exc:`~.commands.MissingPermissions`"

#: ../../ext/commands/api.rst:825
msgid ":exc:`~.commands.BotMissingPermissions`"
msgstr ":exc:`~.commands.BotMissingPermissions`"

#: ../../ext/commands/api.rst:826
msgid ":exc:`~.commands.MissingRole`"
msgstr ":exc:`~.commands.MissingRole`"

#: ../../ext/commands/api.rst:827
msgid ":exc:`~.commands.BotMissingRole`"
msgstr ":exc:`~.commands.BotMissingRole`"

#: ../../ext/commands/api.rst:828
msgid ":exc:`~.commands.MissingAnyRole`"
msgstr ":exc:`~.commands.MissingAnyRole`"

#: ../../ext/commands/api.rst:829
msgid ":exc:`~.commands.BotMissingAnyRole`"
msgstr ":exc:`~.commands.BotMissingAnyRole`"

#: ../../ext/commands/api.rst:830
msgid ":exc:`~.commands.NSFWChannelRequired`"
msgstr ":exc:`~.commands.NSFWChannelRequired`"

#: ../../ext/commands/api.rst:831
msgid ":exc:`~.commands.DisabledCommand`"
msgstr ":exc:`~.commands.DisabledCommand`"

#: ../../ext/commands/api.rst:832
msgid ":exc:`~.commands.CommandInvokeError`"
msgstr ":exc:`~.commands.CommandInvokeError`"

#: ../../ext/commands/api.rst:833
msgid ":exc:`~.commands.CommandOnCooldown`"
msgstr ":exc:`~.commands.CommandOnCooldown`"

#: ../../ext/commands/api.rst:834
msgid ":exc:`~.commands.MaxConcurrencyReached`"
msgstr ":exc:`~.commands.MaxConcurrencyReached`"

#: ../../ext/commands/api.rst:835
msgid ":exc:`~.commands.HybridCommandError`"
msgstr ":exc:`~.commands.HybridCommandError`"

#: ../../ext/commands/api.rst:840
msgid ":exc:`~.commands.ExtensionError`"
msgstr ":exc:`~.commands.ExtensionError`"

#: ../../ext/commands/api.rst:837
msgid ":exc:`~.commands.ExtensionAlreadyLoaded`"
msgstr ":exc:`~.commands.ExtensionAlreadyLoaded`"

#: ../../ext/commands/api.rst:838
msgid ":exc:`~.commands.ExtensionNotLoaded`"
msgstr ":exc:`~.commands.ExtensionNotLoaded`"

#: ../../ext/commands/api.rst:839
msgid ":exc:`~.commands.NoEntryPointError`"
msgstr ":exc:`~.commands.NoEntryPointError`"

#: ../../ext/commands/api.rst:840
msgid ":exc:`~.commands.ExtensionFailed`"
msgstr ":exc:`~.commands.ExtensionFailed`"

#: ../../ext/commands/api.rst:841
msgid ":exc:`~.commands.ExtensionNotFound`"
msgstr ":exc:`~.commands.ExtensionNotFound`"

#: ../../ext/commands/api.rst:842
msgid ":exc:`~.ClientException`"
msgstr ":exc:`~.ClientException`"

#: ../../ext/commands/api.rst:843
msgid ":exc:`~.commands.CommandRegistrationError`"
msgstr ":exc:`~.commands.CommandRegistrationError`"

