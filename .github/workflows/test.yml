name: test

on:
  push:
  pull_request:
    types: [ opened, reopened, synchronize ]

jobs:
  pytest:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [ '3.8', '3.x' ]

    name: pytest ${{ matrix.python-version }}
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up CPython ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install -e .[test]

      - name: Run tests
        shell: bash
        run: |
          PYTHONPATH="$(pwd)" pytest -vs --cov=discord --cov-report term-missing:skip-covered
