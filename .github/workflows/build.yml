name: build

on:
  push:
  pull_request:
    types: [ opened, reopened, synchronize ]

jobs:
  dists-and-docs:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [ '3.8', '3.x' ]
        language: [ 'en', 'ja' ]

    name: dists & docs (${{ matrix.python-version }}/${{ matrix.language }})
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up CPython ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip setuptools wheel
          pip install -U -r requirements.txt

      - name: Build distributions
        run: |
          python ./setup.py sdist bdist_wheel

      # - name: Upload artifacts
      #   uses: actions/upload-artifact@v2
      #   with:
      #     name: distributions
      #     path: dist/*

      - name: Install package
        run: |
          pip install -e .[docs,speed,voice]

      - name: Build docs
        shell: bash
        run: |
          cd docs
          sphinx-build -b html -D language=${DOCS_LANGUAGE} -j auto -a -n -T -W --keep-going . _build/html
        env:
          DOCS_LANGUAGE: ${{ matrix.language }}

      # - name: Upload docs
      #   uses: actions/upload-artifact@v2
      #   if: always()
      #   with:
      #     name: docs-${{ matrix.language }}
      #     path: docs/_build/html/*
