name: lint

on:
  push:
  pull_request:
    types: [ opened, reopened, synchronize ]

jobs:
  check:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [ '3.8', '3.x' ]

    name: check ${{ matrix.python-version }}
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up CPython ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        id: install-deps
        run: |
          python -m pip install --upgrade pip setuptools wheel black==22.6 requests
          pip install -U -r requirements.txt

      - name: Setup node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'

      - name: Run Pyright
        uses: jakebailey/pyright-action@v1
        with:
          version: '1.1.394'
          warnings: false
          no-comments: ${{ matrix.python-version != '3.x' }}

      - name: Run black
        if: ${{ always() && steps.install-deps.outcome == 'success' }}
        run: |
          black --check discord examples
